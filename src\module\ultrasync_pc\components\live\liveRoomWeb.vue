<template>
    <div ref="main_live_container">
        <div v-if="isShow" class="main_live_container">
            <div class="main_live_top_container">
                <div class="top_tools"></div>
                <div class="top_tools_select_window" v-show="isShowCheckBox">
                    <div>
                        <span class="select_window_span">{{ this.$t('select_video_window_to_be_recorded') }}</span>
                    </div>
                    <div>
                        <el-button class="cancel_btn" @click="stopSelectVideoWindow">
                            {{ this.$t('cancel_btn') }}</el-button>
                        <el-button class="confirm_btn" @click="startRecordConference">
                            {{ this.$t('confirm_txt') }}</el-button>
                    </div>
                </div>
                <div class="top_tools_right">
                    <el-button plain readonly>
                        <IntervalTime :diffTime="living_time" class="inter_time"></IntervalTime>
                    </el-button>
                    <el-button plain @click="maxOrMin" icon="el-icon-full-screen"></el-button>
                </div>
            </div>
            <div class="main_live_content_container">
                <div class="live_player_left">
                    <div id="main-stream"></div>
                    <LiveRoomWhiteBoard v-if="showWhiteBoard" ref="liveRoomWhiteBoard"
                        @handleWhiteBoardStatusChange="handleWhiteBoardStatusChange"></LiveRoomWhiteBoard>
                    <SelectWindowCheckbox v-show="isShowCheckBox"
                        v-model="mainStreamCheckBox" label-id="main-stream">
                    </SelectWindowCheckbox>

                </div>
                <div class="live_player_right" v-show="LiveConferenceData.showAuxVideoDomList.length > 0">
                    <div class="aux_stream_container" v-show="findVideoStreamDomShow('video-stream-dom1')">
                        <div id="video-stream-dom1" class="aux-stream"></div>
                        <SelectWindowCheckbox v-show="isShowCheckBox" v-model="videoStreamDomCheckBox1"
                            label-id="video-stream-dom1"></SelectWindowCheckbox>
                    </div>
                    <div class="aux_stream_container" v-show="findVideoStreamDomShow('video-stream-dom2')">
                        <div id="video-stream-dom2" class="aux-stream"></div>
                        <SelectWindowCheckbox v-show="isShowCheckBox" v-model="videoStreamDomCheckBox2"
                            label-id="video-stream-dom2"></SelectWindowCheckbox>
                    </div>
                    <div class="aux_stream_container" v-show="findVideoStreamDomShow('video-stream-dom3')">
                        <div id="video-stream-dom3" class="aux-stream"></div>
                        <SelectWindowCheckbox v-show="isShowCheckBox" v-model="videoStreamDomCheckBox3"
                            label-id="video-stream-dom3"></SelectWindowCheckbox>
                    </div>
                    <!-- <div class="aux_stream_container" v-if="LiveConferenceData.use > 0">
                        <div id="aux-stream-remote" class="aux-stream"></div>
                    </div>
                    <div class="aux_stream_container" v-if="LiveConferenceData.currentSubscribeAux.length > 0">
                        <div id="aux-stream-remote" class="aux-stream"></div>
                    </div>
                    <div class="aux_stream_container" v-if="LiveConferenceData.localVideoStream === 1">
                        <div id="aux-stream-local" class="aux-stream"></div>
                    </div> -->
                </div>
                <div class="live_player_extra" v-show="showMemberList || showChat">
                    <i @click="closeExtra" class="iconfont iconsearchclose"></i>
                    <ChatComponent :chatType="CHAT_TYPE['CONFERENCE']" :cid="cid" :from="`liveRoomWeb`"
                        v-show="showChat" ref="chatComponent"></ChatComponent>
                    <ManageVideoMemberDialog v-model="showMemberList" :cid="cid" v-show="showMemberList"
                        class="managerDialogList"></ManageVideoMemberDialog>
                </div>
            </div>
            <div class="main_live_bottom_container">
                <div class="bottom_tools">
                    <div class="menu-button" v-show="!LiveConferenceData.isRecording" @click="startRecordConference"
                        v-loading="recordingRequestLoading" element-loading-spinner="el-icon-loading">
                        <div class="icon icon-cloud-record"></div>
                        <div class="text longwrap">{{ $t('cloud_record') }}</div>
                    </div>
                    <div class="menu-button" v-show="LiveConferenceData.isRecording" @click="stopRecordConference"
                        v-loading="recordingRequestLoading" element-loading-spinner="el-icon-loading">
                        <div class="icon icon-cloud-record-off"></div>
                        <div class="text longwrap">{{ $t('cloud_record') }}</div>
                    </div>
                    <div class="menu-button-arrow-left" @click="openCamera(false)"
                        v-show="LiveConferenceData.localVideoStream === 1" v-loading="isHandlingCamera"
                        element-loading-spinner="el-icon-loading">
                        <div class="icon icon-camera"></div>
                        <div class="text longwrap">{{ $t('videocamera_title') }}</div>
                    </div>
                    <div class="menu-button-arrow-left" @click="openCamera(true)"
                        v-show="LiveConferenceData.localVideoStream < 1" v-loading="isHandlingCamera"
                        element-loading-spinner="el-icon-loading">
                        <div class="icon icon-camera-off"></div>
                        <div class="text longwrap">{{ $t('videocamera_title') }}</div>
                    </div>
                    <el-popover placement="top" width="400" trigger="manual" v-model="showCamerasList"
                        ref="camerasPopover" popper-class="live_tools_menu-popper">
                        <i class="el-icon-arrow-down" slot="reference" @click="handleShowCamerasList"></i>
                        <ul class="dropdown-menu-ul">
                            <li v-for="device in camerasList" :key="device.deviceId"
                                :class="['dropdown-item', { active: selectedCamera.deviceId === device.deviceId }]"
                                @click="switchCamera(device.deviceId)">
                                <span class="indicator" v-if="selectedCamera.deviceId === device.deviceId">●</span>
                                {{ device.label }}
                            </li>
                        </ul>
                    </el-popover>
                    <div class="menu-button-arrow-left" @click="openMicrophone(false)"
                        v-show="LiveConferenceData.localAudioStream === 1">
                        <div class="icon icon-microphone"></div>
                        <div class="text longwrap">{{ $t('microphone_title') }}</div>
                    </div>
                    <div class="menu-button-arrow-left" @click="openMicrophone(true)"
                        v-show="LiveConferenceData.localAudioStream < 1">
                        <div class="icon icon-microphone-off"></div>
                        <div class="text longwrap">{{ $t('microphone_title') }}</div>
                    </div>
                    <el-popover placement="top" width="400" trigger="manual" v-model="showMicrophonesList"
                        ref="microphonesPopover" popper-class="live_tools_menu-popper">
                        <i class="el-icon-arrow-down" slot="reference" @click="handleShowMicrophonesList"></i>
                        <ul class="dropdown-menu-ul">
                            <li v-for="device in microphonesList" :key="device.deviceId"
                                :class="['dropdown-item', { active: selectedMicrophone.deviceId === device.deviceId }]"
                                @click="switchMicrophone(device.deviceId)">
                                <span class="indicator" v-if="selectedMicrophone.deviceId === device.deviceId">●</span>
                                {{ device.label }}
                            </li>
                        </ul>
                    </el-popover>
                    <div class="menu-button" v-show="LiveConferenceData.localScreenStream === 0"
                        @click="shareScreen(true)"
                        v-if="hasRegionWebShareScreenPermission">
                        <div class="icon icon-desktop-off"></div>
                        <div class="text longwrap">{{ $t('desktop_text') }}</div>
                    </div>
                    <div class="menu-button" v-show="LiveConferenceData.localScreenStream === 1"
                        @click="shareScreen(false)"
                        v-if="hasRegionWebShareScreenPermission">
                        <div class="icon icon-desktop"></div>
                        <div class="text longwrap">{{ $t('desktop_text') }}</div>
                    </div>
                    <div class="menu-button-arrow-left" @click="clickCloseWhiteBoard"
                        v-show="whiteBoardData.isShowWhiteBoardToolBar">
                        <div class="icon icon-whiteboard_open"></div>
                        <div class="text longwrap">{{ $t('white_board') }}</div>
                    </div>
                    <div class="menu-button-arrow-left" @click="clickOpenWhiteBoard"
                        v-show="!whiteBoardData.isShowWhiteBoardToolBar">
                        <div class="icon icon-whiteboard_close"></div>
                        <div class="text longwrap">{{ $t('white_board') }}</div>
                    </div>
                    <!-- <div class="menu-button-arrow-left" @click="clickRequestRemoteControl"
                        v-show="LiveConferenceData.remoteControlData.serviceRemoteControlStatus && LiveConferenceData.remoteControlData.linkToDopplerStatus === 0">
                        <div class="icon icon-remoteControlOff"></div>
                        <div class="text longwrap">{{ $t('RemoteTeaching') }}</div>
                    </div>
                    <div class="menu-button-arrow-left" @click="clickCancelRemoteControl"
                        v-show="LiveConferenceData.remoteControlData.serviceRemoteControlStatus && LiveConferenceData.remoteControlData.linkToDopplerStatus === 1">
                        <div class="icon icon-remoteControl"></div>
                        <div class="text longwrap">{{ $t('linking') }}</div>
                    </div> -->
                    <div class="menu-button-arrow-left" @click="clickCancelRemoteControl"
                        v-show="LiveConferenceData.remoteControlData.serviceRemoteControlStatus && LiveConferenceData.remoteControlData.linkToDopplerStatus === 2">
                        <div class="icon icon-remoteControl"></div>
                        <div class="text longwrap">{{ $t('RemoteTeaching') }}</div>
                    </div>
                    <div class="menu-button" @click="openMemberList()">
                        <div class="icon icon-users"></div>
                        <div class="text longwrap">{{ $t('members_manage_title') }}</div>
                    </div>
                    <div class="menu-button" @click="openChat()">
                        <div class="icon icon-chat"></div>
                        <div class="text longwrap">{{ $t('chat_text') }}</div>
                    </div>
                    <div class="menu-button" @click="openSetting()">
                        <div class="icon icon-setting"></div>
                        <div class="text longwrap">{{ $t('setting_title') }}</div>
                    </div>
                </div>
                <div class="bottom_tools_right">
                    <div v-if="showQuitLiveBtn">
                        <el-button type="danger" @click="forceLeaveChannel" v-show="isShowCloseConferenceBtn">{{
                            $t('end_live')}}</el-button>
                        <el-button type="danger" plain @click="leaveChannel" v-show="isShowLeaveConferenceBtn">{{
                            $t('quit_live')
                        }}</el-button>
                        <el-button plain @click="showQuitLiveBtn = false">{{ $t('cancel_btn') }}</el-button>
                    </div>
                    <el-button type="danger" plain @click="showQuitLiveBtn = true" v-else>{{
                        $t('quit_live')
                    }}</el-button>
                </div>
            </div>
        </div>
        <div v-loading.fullscreen="isConferenceJoining" :element-loading-text="$t('starting_rt_video')"
            class="chatWindow_isConferenceJoining"></div>
        <div v-loading.fullscreen="isReconnectChannel" :element-loading-text="$t('live_conference_reconnecting')"
            class="chatWindow_isReconnectChannel"></div>
        <LiveRoomSetting v-model="showLiveSetting" :cid="cid"></LiveRoomSetting>
        <ReverseControlPanel v-model="showReverseControlPanel" :headerShow="false" :footShow="false" :cid="cid">
        </ReverseControlPanel>
        <!-- <div v-show="false">{{ isSelectCheckBoxList }}</div> -->
    </div>
</template>
<script>
import base from "../../lib/base";
import { joinRoomWeb, resetRequestJoining } from "../../lib/liveConference";
import CLiveRoomWeb from "@/common/CLiveConferenceWeb/CLiveRoomWeb";
import { getLiveRoomObj } from "../../lib/common_base";
import Tool from "@/common/tool";
import ManageVideoMemberDialog from "./manageVideoMemberDialog.vue";
import LiveRoomSetting from "./liveRoomSetting.vue";
import ChatComponent from "../chatComponent";
import IntervalTime from "../../MRComponents/intervalTime.vue";
import { cloneDeep } from "lodash";
import { CHAT_TYPE } from "../../lib/constants";
import LiveRoomWhiteBoard from "./whiteboard/liveRoomWhiteBoard.vue";
import ReverseControlPanel from "./reverseControlPanel.vue"
import SelectWindowCheckbox from "./selectWindowCheckbox.vue";

export default {
    mixins: [base],
    name: "LiveRoomWebComponent",
    permission: true,
    components: {
        ManageVideoMemberDialog,
        ChatComponent,
        IntervalTime,
        LiveRoomSetting,
        LiveRoomWhiteBoard,
        ReverseControlPanel,
        SelectWindowCheckbox,
    },
    data() {
        return {
            CHAT_TYPE,
            isShow: false,
            title: "",
            loading: false,
            agoraClient: null,
            cid: 0,
            remoteUsers: {},
            agoraOptions: {
                appid: "",
                token: "",
                channel: "",
                uid: null,
            },
            isConferenceJoining: false,
            isReconnectChannel: false,
            isCleaningUp: false,
            liveRoom: {},
            localTracks: {
                videoTrack: null,
                audioTrack: null,
            },
            showMemberList: false,
            showChat: false,
            living_time: 0,
            showQuitLiveBtn: false,
            isShowCloseConferenceBtn: false,
            isShowLeaveConferenceBtn: false,
            showLiveSetting: false,
            microphonesList: [],
            selectedMicrophone: {},
            camerasList: [],
            selectedCamera: {},
            showMicrophonesList: false,
            showCamerasList: false,
            recordingRequestLoading: false,
            showWhiteBoard: false,
            whiteBoardData: {
                isShowWhiteBoardToolBar: false,
                isInitWhiteBoard: false
            },
            showReverseControlPanel: false,
            isHandlingCamera: false,
            isHandlingMic: false,
            isShowCheckBox: false,
            mainStreamCheckBox: true,
            videoStreamDomCheckBox1: true,
            videoStreamDomCheckBox2: true,
            videoStreamDomCheckBox3: true,
        };
    },
    computed: {
        LiveConferenceData() {
            return (
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {}
            );
        },
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        hasRegionWebShareScreenPermission(){
            return this.$checkPermission({regionPermissionKey: 'webShareScreen'})
        },
    },
    watch: {
        // 监听 isShow 变化，确保任何情况下变为 false 都会清理直播资源
        isShow(newVal, oldVal) {
            // 当 isShow 从 true 变为 false 时，执行清理
            if (oldVal === true && newVal === false) {
                console.log('检测到 isShow 变为 false，开始清理直播资源');
                this.cleanupLiveRoom();
            }
        }
    },
    mounted() {
        // 监听全局点击事件
        document.addEventListener("click", this.handleClickOutside);
        // setTimeout(()=>{
        //     this.showReverseControlPanel = true
        // },1000)
    },
    beforeDestroy() {
        // 销毁前移除事件监听
        document.removeEventListener("click", this.handleClickOutside);
    },
    methods: {
        async initLiveRoomObj(cid) {
            await Tool.handleAfterConversationCreated(cid);
            if (window.CLiveRoomWeb) {
                if (!window.CLiveRoomWeb[cid]) {
                    window.CLiveRoomWeb[cid] = new CLiveRoomWeb({
                        main_dom: "main-stream",
                        aux_dom_list: ["video-stream-dom1", "video-stream-dom2", "video-stream-dom3"],
                        cid,
                    });
                }
            } else {
                window.CLiveRoomWeb = {};
                window.CLiveRoomWeb[cid] = new CLiveRoomWeb({
                    main_dom: "main-stream",
                    aux_dom_list: ["video-stream-dom1", "video-stream-dom2", "video-stream-dom3"],
                    cid,
                });
            }
            this.liveRoom = getLiveRoomObj(cid);
            // 监听 liveRoom.data 的变化
            this.observeDataChanges();
            this.liveRoom.event.off("HandleNotifyLeaveChannelAux");
            this.liveRoom.event.on("HandleNotifyLeaveChannelAux", this.HandleNotifyLeaveChannelAux);

            this.liveRoom.event.off("HandleNotifyJoinChannelAux");
            this.liveRoom.event.on("HandleNotifyJoinChannelAux", this.HandleNotifyJoinChannelAux);

            this.liveRoom.event.off("HandleDisconnectAux");
            this.liveRoom.event.on("HandleDisconnectAux", this.HandleDisconnectAux);

            this.liveRoom.event.off("openWhiteBoard");
            this.liveRoom.event.on("openWhiteBoard", this.openWhiteBoard);

            this.liveRoom.event.off("dopplerSocketConnect");
            this.liveRoom.event.on("dopplerSocketConnect", this.handleDopplerSocketConnect);
            this.$root.eventBus.$off("startJoinRoom").$on("startJoinRoom", this.startJoinRoom);

            this.$nextTick(() => {
                const main_live_container = this.$refs.main_live_container;
                if (main_live_container) {
                    document.body.appendChild(main_live_container);
                }
            });
        },
        //禁用或启用摄像头
        openCamera: Tool.throttle(
            async function (isSwitch) {
                this.isHandlingCamera = true
                try {
                    await this.liveRoom.MuteLocalVideoStream({
                        isMute: !isSwitch,
                    });
                } catch (error) {
                    console.error(error)
                } finally {
                    this.isHandlingCamera = false
                }

            },
            1000,
            true
        ),
        //启用或禁用麦克风
        openMicrophone: Tool.throttle(
            async function (isSwitch) {
                this.isHandlingMic = true
                try {
                    await this.liveRoom.MuteLocalAudioStream({
                        isMute: !isSwitch,
                    });
                } catch (error) {
                    console.error(error)
                } finally {
                    this.isHandlingMic = false
                }
            },
            800,
            true
        ),
        // 客户离开信道
        async leaveChannel() {
            this.liveRoom.LeaveChannelAux();
        },
        forceLeaveChannel() {
            this.liveRoom.LeaveChannelAux("sender");
        },
        startJoinRoom: Tool.debounce(
            async function ({ main = 0, aux = 0, isSender = 0, cid = 0, from = "chatWindow" }, callback) {
                try {
                    this.cid = cid;
                    this.isConferenceJoining = true;
                    await this.initLiveRoomObj(cid);
                    await joinRoomWeb(
                        {
                            main,
                            aux,
                            isSender,
                            from
                        },
                        cid
                    );
                    this.isConferenceJoining = false;
                    this.isShow = true;
                    callback && callback(true);
                } catch (error) {
                    console.error(error);
                    this.isConferenceJoining = false;
                    if (aux) {
                        this.isShow = false;
                    }
                    callback && callback(false, error);
                }
            },
            800,
            true
        ),
        // 清理直播房间的统一方法
        cleanupLiveRoom() {
            // 防止重复清理
            if (this.isCleaningUp) {
                return;
            }
            this.isCleaningUp = true;

            console.log('开始清理直播房间资源');

            // 重置状态
            this.isConferenceJoining = false;
            this.isShowCloseConferenceBtn = false;
            this.isShowLeaveConferenceBtn = false;
            this.showQuitLiveBtn = false;

            // 销毁白板
            this.destroyWhiteBoard();

            // 发出离开频道事件
            this.$emit("leaveChannelAux");

            // 异步清理 DOM 和数据绑定
            setTimeout(() => {
                const main_live_container = this.$refs.main_live_container;
                if (main_live_container && main_live_container.parentNode) {
                    main_live_container.parentNode.removeChild(main_live_container);
                }
                this.unbindDataChanges();

                // 重置清理标志
                this.isCleaningUp = false;
                console.log('直播房间资源清理完成');
            }, 0);
        },

        HandleNotifyLeaveChannelAux() {
            this.cleanupLiveRoom();
        },
        HandleNotifyJoinChannelAux() {
            const res = this.liveRoom.checkShowLeaveConferenceBtn();
            this.isShowCloseConferenceBtn = res.isShowCloseConferenceBtn;
            this.isShowLeaveConferenceBtn = res.isShowLeaveConferenceBtn;
            // this.$router.push(this.$route.path + `/conference/${this.cid}`);
        },
        HandleDisconnectAux() {
            console.error("HandleDisconnectAux", 1);
            this.isConferenceJoining = false;
            resetRequestJoining();
        },
        destroyWhiteBoard() {
            if (this.$refs.liveRoomWhiteBoard) {
                this.$refs.liveRoomWhiteBoard.destroyWhiteBoard()
            }
            this.whiteBoardData = {
                isShowWhiteBoardToolBar: false,
                isInitWhiteBoard: false
            }
            this.showWhiteBoard = false
        },
        showVideoMember(value) {
            this.speechPanelVisible = value;
        },
        openMemberList() {
            if (!this.showMemberList) {
                this.showMemberList = true;
                this.showChat = false;
            } else {
                this.showMemberList = false;
            }
        },
        openChat() {
            if (!this.showChat) {
                this.showChat = true;
                this.showMemberList = false;
                this.$nextTick(() => {
                    this.$refs.chatComponent.shouldScrollBottom();
                });
            } else {
                this.showChat = false;
            }
        },
        closeExtra() {
            this.showChat = false;
            this.showMemberList = false;
        },
        maxOrMin() {
            const dom = document.querySelector(".main_live_content_container");
            this.fullScreen(dom);
        },
        fullScreen(dom) {
            // 检查是否全屏
            const isFullScreen =
                document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.mozFullScreenElement ||
                document.msFullscreenElement;

            if (isFullScreen) {
                // 退出全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen(); // 标准写法
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen(); // Webkit 浏览器（Chrome、Safari）
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen(); // Firefox
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen(); // IE 浏览器
                } else {
                    console.warn("退出全屏失败：不支持该浏览器的退出全屏 API");
                }
            } else {
                // 进入全屏
                if (dom.requestFullscreen) {
                    dom.requestFullscreen(); // 标准写法
                } else if (dom.webkitRequestFullscreen) {
                    dom.webkitRequestFullscreen(); // Webkit 浏览器（Chrome、Safari）
                } else if (dom.mozRequestFullScreen) {
                    dom.mozRequestFullScreen(); // Firefox
                } else if (dom.msRequestFullscreen) {
                    dom.msRequestFullscreen(); // IE 浏览器
                } else {
                    console.warn("进入全屏失败：不支持该浏览器的全屏 API");
                }
            }
        },
        observeDataChanges() {
            // 防抖函数用于批量替换 roomUserMap
            const debouncedUpdateRoomUserMap = Tool.debounce(() => {
                // 防抖函数内部执行深拷贝，确保拷贝最新的 roomUserMap
                // const newRoomUserMap = cloneDeep(this.liveRoom.data.roomUserMap);
                this.$store.commit("liveConference/setRoomUserMapData", {
                    cid: this.cid,
                    data: { roomUserMap: this.liveRoom.data.roomUserMap },
                });
            }, 150); // 设置防抖时间为 50ms，可以根据需求调整

            // 代理 liveRoom.data 对象
            const originData = cloneDeep(this.liveRoom.data);
            this.liveRoom.data = Tool.deepReactive(originData, (target, key, value, action, path) => {
                // 检测是否是 roomUserMap 的更新
                if (path.includes("roomUserMap")) {
                    if (action === "set" || action === "delete") {
                        // 使用防抖函数更新 roomUserMap
                        debouncedUpdateRoomUserMap();
                    }
                } else {
                    // 处理其他路径的更新
                    if (action === "set") {
                        this.$store.commit("liveConference/setLiveRoomData", { cid: this.cid, data: { path, value } });
                    } else if (action === "delete") {
                        this.$store.commit("liveConference/deleteLiveRoomData", { cid: this.cid, data: { path } });
                    }
                }

                // 更新本地状态
                if (this.liveRoom.data) {
                    this.living_time = this.liveRoom.data.living_time;
                    if (this.liveRoom.data.joinedAux && this.liveRoom.data.losing_connect_server) {
                        this.isReconnectChannel = true;
                    } else {
                        this.isReconnectChannel = false;
                    }
                    if (!this.liveRoom.data.joinedAux) {
                        this.showMemberList = false;
                        this.showChat = false;
                    }
                }
            });
            // 初始同步一次数据
            this.$store.commit("liveConference/replaceLiveRoomData", {
                cid: this.cid,
                data: cloneDeep(this.liveRoom.data),
            });
        },
        unbindDataChanges() {
            this.liveRoom.data = cloneDeep(this.liveRoom.data);
        },
        shareScreen(isShare) {
            this.liveRoom.MuteShareScreen({
                isMute: !isShare,
            });
        },
        findVideoStreamDomShow(dom) {
            // 查找 showVideoDomList 中 id 匹配的对象及其索引
            const index = this.LiveConferenceData.showAuxVideoDomList.findIndex((item) => item.dom === dom);
            if (index !== -1) {
                return true;
            }

            return false;
        },
        getCurrentVideoStreamNum() {
            const videoDomNum1 = this.findVideoStreamDomShow('video-stream-dom1')?1:0
            const videoDomNum2 = this.findVideoStreamDomShow('video-stream-dom2')?1:0
            const videoDomNum3 = this.findVideoStreamDomShow('video-stream-dom3')?1:0
            return videoDomNum1 + videoDomNum2 + videoDomNum3
        },
        openSetting() {
            this.showLiveSetting = true;
        },
        handleShowMicrophonesList() {
            if (!this.showMicrophonesList) {
                this.liveRoom.getMicrophonesList().then((res) => {
                    this.microphonesList = res;
                    this.selectedMicrophone = this.getSelectedMicrophone()
                    if (this.microphonesList && this.microphonesList.length > 0) {
                        this.$nextTick(() => {
                            this.showMicrophonesList = true
                        })
                    }
                }).catch(error => {
                    this.microphonesLis = []
                    this.selectedMicrophone = {}
                });
            } else {
                this.showMicrophonesList = false
            }


        },
        async switchMicrophone(deviceId) {
            this.showMicrophonesList = false
            await this.liveRoom.switchMicrophone(deviceId);
            this.selectedMicrophone = this.getSelectedMicrophone()
        },
        handleShowCamerasList() {
            if (!this.showCamerasList) {
                this.liveRoom.getCamerasList().then((res) => {
                    this.camerasList = res;
                    this.selectedCamera = this.getSelectedCamera()
                    if (this.camerasList && this.camerasList.length > 0) {
                        this.$nextTick(() => {
                            this.showCamerasList = true
                        })
                    }
                }).catch(error => {
                    this.camerasList = []
                    this.selectedCamera = {}
                });
            } else {
                this.showCamerasList = false
            }

        },
        getSelectedMicrophone() {
            if (this.liveRoom.getCurrentMicrophoneInfo()) {
                return this.liveRoom.getCurrentMicrophoneInfo()
            } else if (this.liveRoom.getMicrophoneSettingFromLocalStorage()) {
                const info = this.liveRoom.getMicrophoneSettingFromLocalStorage()
                if (this.microphonesList.find(item => item.deviceId === info.deviceId)) {
                    return info
                }
            }
            return {}
        },
        getSelectedCamera() {
            if (this.liveRoom.getCurrentCameraInfo()) {
                return this.liveRoom.getCurrentCameraInfo()
            } else if (this.liveRoom.getCameraSettingFromLocalStorage()) {
                const info = this.liveRoom.getCameraSettingFromLocalStorage()
                if (this.camerasList.find(item => item.deviceId === info.deviceId)) {
                    return info
                }
            }
            return {}
        },
        async switchCamera(deviceId) {
            this.showCamerasList = false
            await this.liveRoom.switchCamera(deviceId);
            this.selectedCamera = this.getSelectedCamera()
        },
        handleClickOutside(event) {
            if (this.$refs.microphonesPopover) {
                const popover = this.$refs.microphonesPopover.$el; // 获取 Popover 元素
                if (popover && !popover.contains(event.target)) {
                    this.showMicrophonesList = false; // 点击外部时关闭弹层
                }
            }
            if (this.$refs.camerasPopover) {
                const popover = this.$refs.camerasPopover.$el; // 获取 Popover 元素
                if (popover && !popover.contains(event.target)) {
                    this.showCamerasList = false; // 点击外部时关闭弹层
                }
            }
        },
        startSelectVideoWindow() {
            if (!this.LiveConferenceData.cloud_record_auth) {
                this.$message.error(this.$t('only_host_initiate_cloud_recording'))
                return
            }
            if (this.isShowCheckBox){
                return
            } else {
                this.mainStreamCheckBox = true
                this.videoStreamDomCheckBox1 = true
                this.videoStreamDomCheckBox2 = true
                this.videoStreamDomCheckBox3 = true
            }
            //需要选择弹窗
            if(this.getCurrentVideoStreamNum() > 1){
                this.isShowCheckBox = true
            }else{
                this.startRecordConference()
            }
        },
        async startRecordConference() {
            this.isShowCheckBox = false

            if (this.recordingRequestLoading) {
                return
            }
            this.recordingRequestLoading = true
            try {
                // let selectArr =  this.getSelectCheckBoxList()
                // if (selectArr.length !== 0) {
                //     await this.liveRoom.ServiceStartConferenceRecording(selectArr)
                //     this.$message.success(this.$t('recording_turned_on'))
                // } else {
                //     this.$message.error("请选择至少一个需要录制的视频窗口")

                // }
                await this.liveRoom.ServiceStartConferenceRecording()
                this.$message.success(this.$t('recording_turned_on'))

            } catch (error) {
                console.error(error)
            } finally {
                this.recordingRequestLoading = false
            }
        },
        getSelectCheckBoxList() {
            let arr = []
            this.liveRoom.data.showAuxVideoDomList.forEach((item) => {
                if (item.dom === 'video-stream-dom1' && this.videoStreamDomCheckBox1 &&this.findVideoStreamDomShow(item.dom)) {
                    arr.push(item.id)
                } else if (item.dom === 'video-stream-dom2' && this.videoStreamDomCheckBox2 &&this.findVideoStreamDomShow(item.dom)) {
                    arr.push(item.id)
                } else if (item.dom === 'video-stream-dom3' && this.videoStreamDomCheckBox3 &&this.findVideoStreamDomShow(item.dom)) {
                    arr.push(item.id)
                }
            })
            if (this.mainStreamCheckBox) {
                if(this.liveRoom.data.currentMainUid){
                    arr.unshift(this.liveRoom.data.currentMainUid)
                }else{
                    arr.unshift(0)
                }
            }
            // 0+1情况下 需要传主流uid
            if(!this.liveRoom.data.currentMainUid && this.getCurrentVideoStreamNum() == 1 && this.isShowCheckBox){
                arr.unshift(this.liveRoom.data.currentMainUid)
            }
            arr = arr.map(item => {
                if(item === 'local_dom'){
                    return this.liveRoom.data.localAuxUid
                }else{
                    return item
                }
            })
            return arr
        },
        async stopRecordConference() {
            if (!this.LiveConferenceData.cloud_record_auth) {
                this.$message.success(this.$t('only_host_end_cloud_recording'))
                return
            }
            if (this.recordingRequestLoading) {
                return
            }
            this.recordingRequestLoading = true
            if (!this.LiveConferenceData.joinedAux) {
                return
            }
            try {
                await this.liveRoom.ServiceStopConferenceRecording()
                this.$message.success(this.$t('recording_ended'))
            } catch (error) {
                console.error(error)
            } finally {
                this.recordingRequestLoading = false
            }
        },
        clickOpenWhiteBoard() {
            this.showWhiteBoard = true
            this.$nextTick(() => {
                this.liveRoom.openWhiteBoard(true)
            })

        },
        clickCloseWhiteBoard() {
            if (this.$refs.liveRoomWhiteBoard) {
                this.$refs.liveRoomWhiteBoard.closeToolBar()
            }
        },
        openWhiteBoard(data) {
            if (this.showWhiteBoard) {
                this.$refs.liveRoomWhiteBoard.openWhiteBoard({
                    liveRoom: this.liveRoom,
                    ...data
                })
            }else{
                this.showWhiteBoard = true
                setTimeout(() => {
                    this.$refs.liveRoomWhiteBoard.openWhiteBoard({
                        liveRoom: this.liveRoom,
                        ...data
                    })
                }, 100)
            }
        },
        handleWhiteBoardStatusChange(data) {
            console.error('handleWhiteBoardStatusChange', data)
            if (data.hasOwnProperty('isShowToolBar')) {
                this.whiteBoardData.isShowWhiteBoardToolBar = data.isShowToolBar
            }
            if (data.hasOwnProperty('isInitWhiteBoard')) {
                this.whiteBoardData.isInitWhiteBoard = data.isInitWhiteBoard
            }
        },
        clickRequestRemoteControl() {
            this.liveRoom.handleRequestRemoteControl()
        },
        clickCancelRemoteControl() {
            this.liveRoom.handleCancelRemoteControl()
        },
        handleDopplerSocketConnect() {
            this.showReverseControlPanel = true
        },
        stopSelectVideoWindow() {
            this.isShowCheckBox = false
        },

    },
};
</script>
<style lang="scss">
.main_live_container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    z-index: 999;
    user-select: none;

    .main_live_top_container {
        height: 60px;
        background: #fff;
        position: relative;

        .top_tools_select_window {
            position: absolute;
            right: 400px;
            top: 50%;
            transform: translate(-50%, -50%);
            display: flex;

            .select_window_span {
                font-size: 18px;
                font-weight: 900;
            }

            .el-button {
                margin-left: 60px;
                padding: 8px 40px;

                &.confirm_btn {
                    color: #fff;
                    background-color: rgb(22, 155, 213);

                    &:hover {
                        background-color: rgb(86, 175, 213);
                    }

                    &:active {
                        border-color: rgb(21, 118, 160);
                        background-color: rgb(21, 118, 160);
                    }
                }
            }
        }
        .top_tools_right {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .main_live_content_container {
        flex: 1;
        display: flex;
        background: #000;
        overflow: hidden;
        .live_player_left {
            flex: 5;
            width: 100%;
            height: 100%;
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
            position: relative;
            #main-stream {
                width: 100%;
                height: 100%;
                background: transparent url("../../../../../static/resource_pc/images/allmute.png") center no-repeat;
                background-color: #000;
            }
        }
        .live_player_right {
            display: flex;
            flex-direction: column;
            flex: 2;
            background: #000;
            .aux_stream_container {
                position: relative;
                flex: 1;
                border-bottom: 1px solid #ddd;
                .aux-stream {
                    width: 100%;
                    height: 100%;
                    background: transparent url("../../../../../static/resource_pc/images/icon-camera-off.png") center
                        no-repeat;
                    background-color: #000;
                    background-size: 40px 40px;
                }
            }
        }
        .live_player_extra {
            flex: 2;
            background: #000;
            position: relative;
            .chat_history {
                background: #ebeef5;
            }
            .iconsearchclose {
                position: absolute;
                right: 10px;
                top: 10px;
                cursor: pointer;
            }
            .managerDialogList {
                width: 100%;
                height: 100%;
                background: #fff;
                padding-top: 40px;
            }
        }
        .plyr--video {
            width: 100%;
            height: 100%;
            .plyr__controls {
                .plyr__controls__item {
                    &:first-child {
                        margin-right: unset;
                    }
                }
            }
        }
    }
    .main_live_bottom_container {
        height: 60px;
        background: #fff;
        display: flex;
        align-items: center;
        position: relative;
        .bottom_tools {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            .menu-button {
                width: 65px;
                height: 50px;
                justify-content: center;
                align-items: center;
                display: flex;
                flex-direction: column;
                cursor: pointer;
                margin: 0 10px;
                user-select: none;
                &:hover {
                    background: #ddd;
                }
                .text {
                    width: 65px;
                    height: 15px;
                    font-size: 12px;
                    text-align: center;
                }
            }
            .menu-button-arrow {
                display: flex;
                width: 75px;
                height: 50px;
                justify-content: center;
                display: flex;
                cursor: pointer;
                margin: 0 10px;
                user-select: none;
                align-items: center;
            }
            .menu-button-arrow-left {
                width: 65px;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-left: 10px;
                cursor: pointer;
                height: 50px;
                .text {
                    width: 65px;
                    height: 15px;
                    font-size: 12px;
                    text-align: center;
                }
                .icon {
                    width: 65px;
                    height: 25px;
                }
                &:hover {
                    background: #ddd;
                }
            }
            .el-icon-arrow-down {
                    height: 50px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;

                &:hover {
                    background: #ddd;
                }
                }
            .icon {
                background-size: contain; /* 设置背景图按比例缩放以完全显示 */
                width: 24px; /* 设置容器宽度 */
                height: 24px; /* 设置容器高度 */
            }
            .icon-camera-off {
                background: url("../../../../../static/resource_pc/images/icon-camera-off.png") center no-repeat;
            }
            .icon-camera {
                background: url("../../../../../static/resource_pc/images/icon-camera.png") center no-repeat;
            }
            .icon-microphone {
                background: url("../../../../../static/resource_pc/images/icon-microphone.png") center no-repeat;
            }
            .icon-microphone-off {
                background: url("../../../../../static/resource_pc/images/icon-microphone-off.png") center no-repeat;
            }
            .icon-users {
                background: url("../../../../../static/resource_pc/images/icon-users.png") center no-repeat;
            }
            .icon-chat {
                background: url("../../../../../static/resource_pc/images/icon-chat.png") center no-repeat;
            }
            .icon-desktop {
                background: url("../../../../../static/resource_pc/images/icon-desktop.png") center no-repeat;
            }
            .icon-desktop-off {
                background: url("../../../../../static/resource_pc/images/icon-desktop-off.png") center no-repeat;
            }
            .icon-setting {
                background: url("../../../../../static/resource_pc/images/icon-setting.png") center no-repeat;
                background-size: contain;
            }
            .icon-cloud-record{
                background: url("../../../../../static/resource_pc/images/icon-cloud-record.png") center no-repeat;
            }
            .icon-cloud-record-off{
                background: url("../../../../../static/resource_pc/images/icon-cloud-record-off.png") center no-repeat;
            }
            .icon-whiteboard_open{
                background: url("../../../../../static/resource_pc/images/icon-whiteboard_open.png") center no-repeat;
            }
            .icon-whiteboard_close{
                background: url("../../../../../static/resource_pc/images/icon-whiteboard_close.png") center no-repeat;
            }
            .icon-remoteControl_disabled{
                background: url("../../../../../static/resource_pc/images/icon-remoteControl_disabled.png") center no-repeat;
            }
            .icon-remoteControlOff{
                background: url("../../../../../static/resource_pc/images/icon-remoteControlOff.png") center no-repeat;
            }
            .icon-remoteControl{
                background: url("../../../../../static/resource_pc/images/icon-remoteControl.png") center no-repeat;
            }
        }
        .bottom_tools_right {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
#live_player_main {
    background: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    right: 0;
    top: 0;
    .el-dialog {
        width: 80vw;
        height: 80vh;
        position: fixed !important;
        margin: 0 !important;
        .el-dialog__header {
            position: relative;
        }
    }
    #main-stream-menu {
        width: 100%;
        height: 50px;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #ccc;
    }
}

#live_player_remote_aux {
    background: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    left: 0;
    top: 0;
    .el-dialog {
        width: 300px;
        height: 300px;
        position: fixed !important;
        margin: 0 !important;
        right: 0;
        left: auto;

        .el-dialog__header {
            position: relative;
        }
    }
}
#live_player_local_aux {
    background: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    left: 0;
    top: 0;
    .el-dialog {
        width: 300px;
        height: 300px;
        position: fixed !important;
        margin: 0 !important;
        right: 0;
        top: 350px;
        left: auto;

        .el-dialog__header {
            position: relative;
        }
    }
    .aux-stream {
        width: 100%;
        height: 100%;
        background: transparent url("../../../../../static/resource_pc/images/icon-camera-off.png") center no-repeat;
        background-color: #000;
    }
}
.live_tools_menu-popper {
    user-select: none;
    .dropdown-menu-ul {
        .dropdown-item {
            line-height: 30px;
            cursor: pointer;
            &.active {
                background: #ccc;
            }
            &:hover {
                background: #ccc;
            }
        }
    }
}
</style>
