<template>
    <transition name="slide">
        <div class="group_all_attendee_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{ $t('group_all_attendee_title') }}({{ attendeeArray.length }})
                </template>
            </mrHeader>
            <div class="group_all_attendee_container">
                <van-search
                    v-model="searchValue"
                    shape="round"
                    background="#ededed"
                    :placeholder="$t('search_input_key')"
                    @input="debounceHandleKeywordInput"
                />
                <div class="group_members clearfix">
                    <div class="clearfix">
                        <template v-if="renderList.length > 0">
                            <div
                                v-for="(member, index) of renderList"
                                class="member_item fl"
                                :key="index"
                                @click.stop="openVisitingCard(member, 4)"
                            >
                                <mr-avatar
                                    :url="getLocalAvatar(member)"
                                    :origin_url="member.avatar"
                                    :showOnlineState="true"
                                    :radius="2.4"
                                    :key="member.avatar"
                                    :onlineState="member.state"
                                ></mr-avatar>
                                <p>{{ member.showNickname }}</p>
                                <div class="tag_box">
                                    <span v-if="member.userid == conversation.creator_id" class="creator_tag">{{ $t('creator_tag_text') }}</span>
                                    <span v-else-if="member.role == systemConfig.groupRole.manager" class="manager_tag">{{ $t('manager') }}</span>
                                    <span v-if="member.userid == user.uid" class="me_tag">{{ $t('mine') }}</span>
                                </div>
                            </div>
                        </template>

                        <van-empty :description="$t('no_data_txt')" image="search" v-else />
                        <template >
                            <div class="member_item fl icon_item">
                                <i class="icon iconfont icon-plus" @click.stop="openAddAttendee"></i>
                            </div>
                            <div class="member_item fl icon_item" v-if="hasDeleteAttendeePermission && isGroupChat">
                                <i class="icon iconfont icon-minus1" @click.stop="openDeleteAttendee"></i>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import { Toast } from 'vant';
import { openVisitingCard, getLocalAvatar,formatAttendeeNickname,filterDataByNickname } from "../lib/common_base";
import { Search, Empty } from "vant";
export default {
    mixins: [base, Tool],
    name: "group_setting_attendee",
    components: {
        VanSearch: Search,
        VanEmpty: Empty,
    },
    data() {
        return {
            getLocalAvatar,
            cid: this.$route.params.cid,
            searchValue: "",
            regList: [],
        };
    },
    beforeDestroy() {},
    mounted() {
        this.$nextTick(() => {});
    },
    activated() {
        this.cid = this.$route.params.cid;
    },
    deactivated() {
        setTimeout(() => {
            this.searchValue = "";
            this.regList = "";
        }, 500);
    },
    computed: {
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        attendeeArray() {
            return formatAttendeeNickname(this.conversation.attendeeList)
        },
        sortedList() {
            // 排序规则：群主 > 管理员 > 我 > 普通成员 > 临时参会者
            const userId = this.user.uid;
            const creatorId = this.conversation.creator_id;
            const managerRole = this.systemConfig.groupRole.manager;
            return [...this.attendeeArray].sort((a, b) => {
                // 临时参会者始终最后
                const isTempA = a.attendeeState === 2 || a.attendeeState === 3;
                const isTempB = b.attendeeState === 2 || b.attendeeState === 3;
                if (isTempA && !isTempB) {
                    return 1;
                }
                if (!isTempA && isTempB) {
                    return -1;
                }
                // 群主优先
                if (a.userid === creatorId && b.userid !== creatorId) {
                    return -1;
                }
                if (a.userid !== creatorId && b.userid === creatorId) {
                    return 1;
                }
                // 管理员优先
                if (a.role === managerRole && b.role !== managerRole) {
                    return -1;
                }
                if (a.role !== managerRole && b.role === managerRole) {
                    return 1;
                }
                // "我"优先
                if (a.userid === userId && b.userid !== userId) {
                    return -1;
                }
                if (a.userid !== userId && b.userid === userId) {
                    return 1;
                }
                // 其他情况保持原顺序
                return 0;
            });
        },
        renderList() {
            if (this.searchValue || this.regList.length > 0) {
                return this.regList;
            } else {
                return this.sortedList;
            }
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        isGroupChat(){
            return this.conversation.is_single_chat == 0;
        },
        hasDeleteAttendeePermission(){
            return this.$checkPermission({conversationPermissionKey: 'member.remove'},{
                conversationId:this.cid,
            })
        }
    },
    methods: {
        openAddAttendee() {
            if (!Tool.checkSpeakPermission(this.cid, this.user.uid)) {
                Toast(this.$t('app_no_speak_permission'));
                return;
            }
            this.$router.replace(`/index/chat_window/${this.cid}/group_setting/add_attendee`);
        },
        openDeleteAttendee() {
            if (!Tool.checkSpeakPermission(this.cid, this.user.uid)) {
                Toast(this.$t('app_no_speak_permission'));
                return;
            }
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/delete_attendee`);
        },
        openVisitingCard(messages, type) {
            openVisitingCard(messages, type);
        },
        debounceHandleKeywordInput: Tool.debounce(function () {
            const keyword = this.searchValue;
            const filteredData = filterDataByNickname(this.attendeeArray, keyword);
            this.regList = filteredData;
        }, 300),
    },
};
</script>
<style lang="scss" scoped>
.group_all_attendee_container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .group_members {
        padding: 0.5rem 0.5rem;
        border-top: 1px solid #ddd;
        background-color: #fff;
        overflow: auto;
        .member_item {
            width: 25%;
            position: relative;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            & > p {
                text-align: center;
                font-size: 0.7rem;
                margin: 0.3rem 0;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                color: #666;
                width: 100%;
            }
            & > span {

            }
            .tag_box{
                position: absolute;
                bottom: 1rem;
                left: 0.4rem;
                display: flex;
                flex-direction: column;
                gap:.22rem;
                span{
                    color: #fff;
                    padding: 0.2rem 0.1rem;
                    border-radius: 0.2rem;
                    max-width: 3.2rem;
                    word-wrap: break-word;
                    line-height: 1;
                    font-size: 0.6rem;   font-size: 0.6rem;
                }
            }
            .creator_tag {
                background: #00c59d;
            }
            .manager_tag {
                background: #00c59d;
            }
            .me_tag {
                background: #f5a623;
            }
            .icon-plus {
                font-size: 2.4rem;
                line-height: 1;
                color: #aaa;
            }
            .icon-minus1 {
                font-size: 2.4rem;
                line-height: 1;
                color: #aaa;
            }
        }
        .icon_item {
            height: 4rem;
        }
        & > p {
            color: #999;
            text-align: center;
            font-size: 0.8rem;
            padding: 0.2rem 0;
        }
    }
}
</style>
