<template>
    <div class="chat_toolbar">
        <div class="toolbar clearfix">
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                :disabled="false"
            >
                <span>{{$t('emoticon')}}</span>
                <i @click.stop="onClickFace" class="fl icon iconfont iconsmile" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
            >
                <input :key="fileTag" type="file" @change="onFileChange($event)" ref="uploadInput" multiple="multiple" :accept="supportFileTypeStrings" style="display:none;">
                <span>{{$t('upload_file')}}</span>
                <i class="fl icon iconfont iconfolder" slot="reference" @click="uploadPicture"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="!isRecording"
                v-if="!isInternalNetworkEnv"
            >
                <span>{{$t('voice_recording')}}</span>
                <i @click="$emit('send-record-voice')" class="fl icon iconfont iconsoundlight" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="isRecording"
                v-if="!isInternalNetworkEnv"
            >
                <span>{{$t('voice_recording')}}</span>
                <i class="fl icon iconfont iconmicrophone" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="existLive&&!isConferenceAuxOnline&&hasLiveRegionPermission"
            >
                <span>{{$t('live')}}</span>
                <i  @click="$emit('accept-live-conference')" class="fl icon iconfont icontv" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="isShowCallVideoBtn"
            >
                <span>{{$t('conference_seeding')}}</span>
                <i @click="$emit('start-conference')" class="fl icon iconfont iconchaoshengbo" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
            >
                <span>{{$t('more_features')}}</span>
                <i @click.stop="onClickGroupSetting" class="fl icon iconfont iconplus1" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&isShowExamType"
            >
                <span>{{$t('exam_view_mode')}}</span>
                <i @click="togglePageType" class="fl icon iconfont iconlist" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="hasObstetricalAIRegionPermission"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&isObstetricQCMulticenter"
            >
                <span>{{$t('obstetric_qc_multicenter')}}</span>
                <i @click="goMulticenter" class="fl icon iconfont iconfenbushishujuku" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="hasDrAiAnalyzeRegionPermission"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&isDrAiAnalyze&&$store.state.device.isIStationInfoDR&&$store.state.device.drConnectStatus"
            >
                <span>{{$t('dr_ai_analyze_statistics')}}</span>
                <i @click="goToDrAiAnalyzeStatistics" class="fl icon iconfont iconshanxingzhanbi" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
            >
                <span>{{$t('chat_history')}}</span>
                <i @click="openHistory" class="fr icon iconfont iconyidiandiantubiao18" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
            >
                <span>{{$t('clear_history')}}</span>
                <i @click="clearHistory" class="fr icon iconfont iconicon-clearicon" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="click"
                width="250"
                v-show="chatType===CHAT_TYPE['CONFERENCE']"
                popper-class="toolbar_item_attendee"
            >
                <AttendeeList :list="attendeeArray" :cid="cid"></AttendeeList>
                <i class="fl icon iconfont icongroups" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="showMangerStopConference&&!showStopConference"
            >
                <span>{{$t('close_consultation')}}</span>
                <i @click="$emit('force-stop-conference')" class="icon iconfont iconshut-down" slot="reference">
                </i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&EnableQc_statistics&&isGroupChat"
            >
                <span>{{$t('bi_data_display')}}</span>
                <i @click="openBIData" class="fl icon iconfont icona-statisticalviewhistogram-line" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
            >
                <span>{{$t('cloud_exam')}}</span>
                <i @click="openCloudExam" class="fl icon iconfont iconzaixiankaoshi" slot="reference"></i>
            </el-popover>
        </div>

        <div v-show="isShowFacePanel" class="face_panel">
            <span @click.stop="$emit('append-face', emoji)" v-for="(emoji, index) in emojiArr" :key="index" class="emoji">
                {{ emoji }}
            </span>
        </div>

        <div v-show="isRecording" class="recording_panel">
            <div class="record_count">
                <i class="icon iconfont iconSound-wave"></i>
                <span>{{recordTimeStr}}</span>
                <i class="icon iconfont iconSound-wave"></i>
            </div>
            <i @click="$emit('record-cancel')" class="icon iconfont iconel-icon-delete2"></i>
            <i @click="$emit('record-end')" class="icon iconfont iconsend"></i>
        </div>

        <!-- <span v-if="isConferenceAuxOnline" class="realtime_toolbar_triangle"></span> -->
        <div class="realtime_toolbar" v-if="isConferenceAuxOnline">
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="isConferenceAuxOnline"
            >
                <span>{{$t('live_address')}}</span>
                <i @click="$emit('get-live-address')"  class="icon iconfont iconlocation" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="isConferenceRecording"
            >
                <span>{{$t('is_recording_text')}}</span>
                <i class="icon iconfont iconagora_luzhizhong" slot="reference"></i>
            </el-popover>
        </div>

        <div v-show="isShowGroupSetting" class="group_setting_panel">
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="isWorkStation&&!isService&&!isCE&&!isInternalNetworkEnv"
            >
                <span>{{$t('send_uf_to_conversation')}}</span>
                <i @click="emitFileTransfer" class="icon iconfont icon_chaoshenghuizhenshenqing fl" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="isWorkStation&&!isService&&!isCE"
            >
                <span>{{$t('send_realtime_to_conversation')}}</span>
                <i @click="emitRealtimeTransfer" class="icon iconfont iconcamera fl" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
            >
                <span>{{$t('setting_title')}}</span>
                <i @click="openGroupSetting" class="icon iconfont iconsetting fl" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&!isService"
            >
                <span>{{$t('export_image')}}</span>
                <i @click="exportFile" class="fl icon iconfont icondownload" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-show="!isService"
            >
                <span>{{$t('group_add_attendee_title')}}</span>
                <i @click="addAttendee" class="icon iconfont iconusers-medical fl" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="hasMemberRemoveRegionPermission"
            >
                <span>{{$t('groupset_delete_attendee')}}</span>
                <i @click="deleteAttendee" class="icon iconfont iconuser-delete fl" slot="reference"></i>
            </el-popover>
            <el-popover
                placement="top"
                trigger="hover"
                popper-class="toolbar_item"
                v-if="isGroupChat&&hasLiveRegionPermission"
            >
                <span>{{$t('reserved_conference')}}</span>
                <i @click="openReservedConference" class="icon iconfont iconyuyue fl" slot="reference"></i>
            </el-popover>
        </div>
    </div>
</template>

<script>
import AttendeeList from './attendeeList.vue'
import { CHAT_TYPE, EMOJI_LIST } from '../lib/constants.js'
import Tool from '@/common/tool.js'
import { getLanguage } from '@/common/i18n'
import base from '../lib/base'
export default {
    mixins: [base],
    name: 'ChatToolbar',
    components: { AttendeeList },
    props: {
        cid: { type: [String, Number], required: true },
        chatType: { type: Number, required: true },
        isRecording: { type: [Boolean, Number], default: false },
        existLive: { type: [Boolean, Number], default: false },
        isConferenceAuxOnline: { type: [Boolean, Number], default: false },
        isConferenceRecording: { type: [Boolean, Number], default: false },
        isShowCallVideoBtn: { type: [Boolean, Number], default: false },
        isShowExamType: { type: [Boolean, Number], default: false },
        isObstetricQCMulticenter: { type: [Boolean, Number], default: false },
        isDrAiAnalyze: { type: [Boolean, Number], default: false },
        EnableQc_statistics: { type: [Boolean, Number], default: false },
        isGroupChat: { type: [Boolean, Number], default: false },
        supportFileTypeStrings: { type: String, default: '' },
        fileTag: { type: [String, Number], default: '' },
        attendeeArray: { type: Array, default: () => [] },
        showMangerStopConference: { type: [Boolean, Number], default: false },
        showStopConference: { type: [Boolean, Number], default: false },
        recordTimeStr: { type: String, default: '' },
    },
    data(){
        return {
            CHAT_TYPE,
            // 内部管理面板开关
            isShowFacePanel: false,
            isShowGroupSetting: false,
            // 内置表情数组
            emojiArr: EMOJI_LIST
        }
    },
    computed: {
        conversation(){
            return this.$store.state.conversationList[this.cid] || {}
        },
        isInternalNetworkEnv(){
            return this.$store.state.systemConfig.serverInfo.network_environment
        },
        isCE(){
            return this.$store.state.globalParams.isCE
        },
        isService(){
            return this.conversation.service_type!=0
        },
        isWorkStation(){
            return this.isCef&&Tool.ifAppWorkstationClientType(this.systemConfig.clientType)
        },
        hasLiveRegionPermission(){
            return this.$checkPermission({regionPermissionKey: 'live'})
        },
        hasObstetricalAIRegionPermission(){
            return this.$checkPermission({regionPermissionKey: 'obstetricalAI'})
        },
        hasDrAiAnalyzeRegionPermission(){
            return this.$checkPermission({regionPermissionKey: 'drAIAssistant'})
        },
        hasMemberRemoveRegionPermission(){
            // 依赖会话权限版本号确保响应式更新
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.remove'},{conversationId:this.cid})
        },
    },
    mounted(){
        // 点击组件外部收起面板
        this._outsideClickHandler = (e)=>{
            if(!this.$el.contains(e.target)){
                this.isShowFacePanel = false
                this.isShowGroupSetting = false
            }
        }
        document.addEventListener('click', this._outsideClickHandler)
    },
    beforeDestroy(){
        document.removeEventListener('click', this._outsideClickHandler)
    },
    methods: {
        uploadPicture(){
            if(this.$refs.uploadInput){
                this.$refs.uploadInput.click()
            }
        },
        onFileChange(e){
            const files = e && e.target ? e.target.files : []
            this.$emit('upload-picture-change', files)
        },
        onClickFace(){
            this.$emit('record-cursor')
            this.isShowFacePanel = true
            this.isShowGroupSetting = false
        },
        onClickGroupSetting(){
            this.isShowGroupSetting = true
            this.isShowFacePanel = false
        },
        // 入口类操作下放
        openHistory(){
            Tool.loadModuleRouter(this.$route.fullPath + '/chat_history_search_list')
        },
        clearHistory(){
            this.$store.commit('conversationList/clearHistory', { cid: this.cid })
        },
        togglePageType(){
            this.$root.eventBus.$emit('togglePageType')
        },
        openGroupSetting(){
            this.isShowGroupSetting = false
            Tool.loadModuleRouter(this.$route.fullPath + '/edit_group_setting')
        },
        addAttendee(){
            this.isShowGroupSetting = false
            Tool.loadModuleRouter(this.$route.fullPath + '/add_attendee')
        },
        deleteAttendee(){
            this.isShowGroupSetting = false
            Tool.loadModuleRouter(this.$route.fullPath + '/delete_attendee')
        },
        openReservedConference(){
            this.isShowGroupSetting = false
            Tool.loadModuleRouter(this.$route.fullPath + '/reserved_conference')
        },
        openCloudExam(){
            this.$router.push({ path: this.$route.path + '/cloud_exam', query: this.$route.query })
        },
        exportFile(){
            this.isShowGroupSetting = false
            if(this.isCE){
                Tool.loadModuleRouter(this.$route.fullPath + '/export_file')
            }else{
                this.$message.error(this.$t('use_app_tip'))
            }
        },
        openBIData(){
            const requestConfig = this.$store.state.systemConfig.server_type
            let ajaxServer = requestConfig.protocol + requestConfig.host + requestConfig.port
            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }
            const url = Tool.transferLocationToCe(`${ajaxServer}/statistic.html#/remote_ultrasound_data_center?dataFrom=group&id=${this.cid}&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${getLanguage()}`)
            if(Tool.ifBrowserClientType(this.$store.state.systemConfig.clientType)){
                window.open(url,'blank')
            }else{
                window.CWorkstationCommunicationMng.OpenNewWindow({url})
            }
        },
        goToDrAiAnalyzeStatistics(){
            this.$router.push(this.$route.fullPath + '/dr_ai_statistics')
        },
        emitFileTransfer(){
            this.isShowGroupSetting = false
            this.$emit('file-transfer')
        },
        emitRealtimeTransfer(){
            this.isShowGroupSetting = false
            this.$emit('realtime-transfer')
        },
        goMulticenter(){
            if(!this.conversation || !this.conversation.multicenter_info){
                return
            }
            const item = this.conversation.multicenter_info
            const option = { cid: this.cid, fid: 0 }
            if(this.conversation && this.conversation.is_single_chat){
                option.fid = this.conversation.fid
            }
            const typesMap = this.$store.state.multicenter.type
            const configMap = this.$store.state.multicenter.config
            const type = typesMap[item.type]
            const config = configMap[item.type]
            this.$store.commit('multicenter/setCurrentConfig', config)
            this.$store.commit('multicenter/updateEnterByGroup', option)
            this.$store.commit('multicenter/setCurrentMulticenter', item)
            this.$store.commit('multicenter/setAnonymous', item)
            this.$router.push(`/main/index/chat_window/${this.cid}/multicenter`)
        }
    }
}
</script>

<style lang="scss" scoped>
.chat_toolbar{
    .face_panel,
    .group_setting_panel{
        position:absolute;
        width:100%;
        bottom:220px;
        background:#a9bfbe;
        padding-top:6px;
        z-index:3;
        .emoji{
            font-size:18px;
            cursor:pointer;
            width:34px;
            height:34px;
            display:inline-block;
            line-height:34px;
            text-align:center;
            &:hover{
                background-color:#f0f0f0;
                border-radius:5px;
            }
        }
        i{
            cursor:pointer;
            color:#fff;
            font-size:32px;
            margin:0 6px;
            line-height:42px;
        }
        .iconusers-medical{ font-size:28px; }
        .iconyuyue{ font-size:30px; }
        .icon_chaoshenghuizhenshenqing{ font-size:30px; }
    }

    .recording_panel{
        position:absolute;
        width:100%;
        bottom:220px;
        background:#a9bfbe;
        height:46px;
        line-height:46px;
        color:#e5edf1;
        z-index:9;
        border:1px solid rgb(255,255,255);
        .iconsend,
        .iconel-icon-delete2{
            float:right;
            width:40px;
            font-size:26px;
            cursor:pointer;
        }
        .record_count{
            float:left;
            margin-left:50px;
            line-height:1;
            i{ font-size:28px; }
            span{
                display:inline-block;
                line-height:46px;
                width:60px;
                text-align:center;
                font-size:20px;
            }
        }
    }

    .realtime_toolbar{
        position:absolute;
        bottom:220px;
        background:#a9bfbe;
        padding:6px;
        white-space:normal;
        z-index:2;
        left:0;
        width:100%;
        display:flex;
        align-items:center;
        &>span{ margin-right:10px; }
        .redColor{ color:red; }
        i{
            font-size:28px;
            color:#fff;
            margin:0 2px;
            cursor:pointer;
        }
        .iconuser-cog{
            font-size:26px;
            position:relative;
            span{
                position:absolute;
                width:8px;
                height:8px;
                background:#f00;
                right:4px;
                top:0px;
                border-radius:50%;
            }
        }
        .round_pot{ width:30px; vertical-align:sub; }
        .iconshut-down{ color:#f00; }
        .iconmicrophone{
            position:relative;
            &:before{ color:#01c59d; }
            &:after{
                content:"\e78b";
                height:100%;
                display:block;
                position:absolute;
                overflow:hidden;
                top:0px;
                left:0px;
                color:#fff;
                transition:height 0.1s;
                line-height:30px;
            }
            &.volume_0:after{height:100%}
            &.volume_1:after{height:81%}
            &.volume_2:after{height:72%}
            &.volume_3:after{height:63%}
            &.volume_4:after{height:54%}
            &.volume_5:after{height:45%}
            &.volume_6:after{height:36%}
            &.volume_7:after{height:27%}
            &.volume_8:after{height:18%}
            &.volume_9:after{height:9%}
            &.volume_10:after{height:0%}
        }
        .iconyinliang-copy{
            position:relative;
            &:before{ color:#01c59d; }
            &:after{
                content:"\e60c";
                height:100%;
                display:block;
                position:absolute;
                overflow:hidden;
                top:0px;
                left:0px;
                color:#fff;
                transition:height 0.1s;
                line-height:30px;
            }
            &.volume_0:after{height:100%}
            &.volume_1:after{height:81%}
            &.volume_2:after{height:72%}
            &.volume_3:after{height:63%}
            &.volume_4:after{height:54%}
            &.volume_5:after{height:45%}
            &.volume_6:after{height:36%}
            &.volume_7:after{height:27%}
            &.volume_8:after{height:18%}
            &.volume_9:after{height:9%}
            &.volume_10:after{height:0%}
        }
    }
    .realtime_toolbar_triangle{
        position:absolute;
        border:10px solid #a9bfbe;
        width:0;
        height:0;
        border-bottom:none;
        border-left-color:transparent;
        border-right-color:transparent;
        bottom:210px;
        left:130px;
    }
    .iconagora_luzhizhong{
        color:red;
        font-size:32px;
        font-weight:600;
        animation:star 0.5s ease-in infinite;
    }
}
@keyframes star{ 10%{opacity:0;} 90%{opacity:1;} }
</style>


