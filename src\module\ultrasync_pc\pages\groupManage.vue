<template>
    <div>
        <CommonDialog
            class="group_manage"
            :title="$t('group_manage')"
            :show.sync="visible"
            :close-on-click-modal="false"
            :append-to-body="true"
            width="40%"
            :modal="false"
            @closed="back"
            :footShow="false"
        >
            <div class="group_manage_container">
                <div class="group_manage_item">
                    <div class="group_manage_line">
                        <span class="title">{{ $t('group_join_verify') }}：</span>
                        <el-switch v-model="joinVerify" @change="toggleVerify"></el-switch>
                    </div>
                    <p>{{ $t('group_join_verify_tip') }}</p>
                </div>
                <div class="group_manage_item">
                    <el-button type="primary" size="medium" @click="openJoinVerify">{{ $t('group_join_verify_btn') }}</el-button>
                    <el-button v-if="hasGroupTransferPermission" type="primary" size="medium" @click="transferGroup">{{ $t('transfer_group') }}</el-button>
                    <el-button v-if="hasSetAdminPermission" type="primary" size="medium" @click="openGroupManagers">{{ $t('group_managers') }}</el-button>
                    <el-button v-if="hasDeleteGroupPermission" type="primary" size="medium" @click="deleteGroup">{{ $t('delete_group') }}</el-button>
                </div>
            </div>
            <router-view></router-view>
     </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import CommonDialog from "../MRComponents/commonDialog.vue";
export default {
    mixins: [base],
    name: "transferGroup",
    components: {CommonDialog},
    permission: true,
    data() {
        return {
            visible: false,
            cid: this.$route.params.cid,
            searchText: "",
            joinVerify:false,
        };
    },
    computed: {
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        attendeeList() {
            return this.conversation.attendeeList;
        },
        attendeeArray() {
            let arr = [];
            for (let key in this.attendeeList) {
                if (this.attendeeList[key].attendeeState != 0 && this.attendeeList[key].userid != this.user.id) {
                    arr.push(this.attendeeList[key]);
                }
            }
            return arr;
        },
        hasGroupTransferPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.transfer_ownership'},{
                conversationId:this.cid,
            })
        },
        hasSetAdminPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.set_admin'},{
                conversationId:this.cid,
            })
        },
        hasDeleteGroupPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.delete'},{
                conversationId:this.cid,
            })
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.visible = true;
            this.joinVerify = this.conversation.join_check===1;
        });
    },
    methods: {
        transferGroup(){
            this.$router.push(this.$route.fullPath+'/transfer_group')
        },
        openGroupManagers(){
            this.$router.push(this.$route.fullPath+'/group_managers')
        },
        deleteGroup(){
            var that=this;
            let message = this.$t('creator_user_exit_group_tip');
            this.$confirm(message,this.$t('tip_title'),{
                confirmButtonText:this.$t('confirm_button_text'),
                cancelButtonText:this.$t('cancel_button_text'),
                type:'warning'
            }).then(()=>{
                let data={};
                let message = "";
                message = "request_delete_group";
                data.uid = that.user.uid;
                data.cid = parseInt(that.cid);
                data.isCreator = true;
                that.loading=true;
                that.conversation.socket.emit(message, data, function(is_succ, info){
                    console.log("callback " + message);
                    that.loading=false;
                    if(!is_succ){
                        console.log(info);
                        that.$message.error(that.$t('user_exit_group_fail'));
                    }
                });
            })
        },
        openJoinVerify(){
            this.$router.push(this.$route.fullPath+'/join_verify')
        },
        toggleVerify(){
            let join_check=this.joinVerify?1:0;
            window.main_screen.conversation_list[this.cid].groupSettingUpdate({
                key:"join_check",
                value: join_check,
            },(res)=>{
                if(res.error_code === 0){
                    this.$store.commit('conversationList/updateConversation',{
                        cid:this.cid,
                        key:'join_check',
                        value:join_check
                    })
                }else{
                    this.$message.error(this.$t('operate_err'));
                    this.joinVerify = !this.joinVerify;
                }
            })
        }
    },
};
</script>
<style lang="scss">
.group_manage {
    .el-dialog {
    }
    .el-dialog__body {
        display: flex;
        flex-direction: column;
        .group_manage_container {
            height: 100%;
            .group_manage_item{
                margin: 10px 0 20px;
                .group_manage_line{
                    display: flex;
                }
                .title{
                    font-size: 16px;
                    font-weight: bold;
                }
            }
        }
    }
}
</style>
