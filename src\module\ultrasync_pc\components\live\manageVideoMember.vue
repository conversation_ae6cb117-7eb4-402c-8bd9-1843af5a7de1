<template>
    <div class="manage_video_member">
        <div class="manage_video_member_panel">
            <div class="group">
                <div class="group_members clearfix">
                    <div v-for="item of allUserList" class="group_member_item" :key="item.userid">
                        <div class="group_user_wrapper">
                            <mr-avatar
                                :url="getLocalAvatar(item)"
                                :showOnlineState="false"
                                :radius="40"
                                :key="item.avatar"
                                @click.native="clickMember(item)"
                            ></mr-avatar>
                            <div class="user_info">
                                <span>{{ item.nickname }}</span>
                                <div class="tag_box">
                                    <span class="tag" v-if="item.isHost">{{ $t('moderator') }}</span>
                                    <span class="tag" v-if="item.userid == user.uid">{{ $t('mine') }}</span>
                                    <span class="tag" v-if="item.userid == LiveConferenceData.currentMainUltrasyncId">{{
                                        $t('main_stream_screen')
                                    }}</span>
                                </div>
                                <el-popover
                                    placement="bottom"
                                    width="200"
                                    trigger="hover"
                                    :content="item.introduction">
                                    <div class="user_info_introduction" slot="reference">{{item.introduction}}</div>
                                </el-popover>

                            </div>
                        </div>
                        <div class="group_user_operate">
                            <span class="operate_btn" v-if="item.userid === user.uid">
                                <i
                                    v-if="item.videoStream === 1"
                                    @click="toggleSelfVideo(item)"
                                    class="icon iconfont iconshipinluxiang"
                                ></i>
                                <i
                                    v-else
                                    @click="toggleSelfVideo(item)"
                                    class="icon iconfont iconweifaxianshexiangtou"
                                ></i>
                            </span>
                            <span class="operate_btn" v-else>
                                <i
                                    v-if="item.videoStream === 1 &&!LiveConferenceData.currentSubscribeAux.includes(item.uid)"
                                    @click="toggleRemoteSubscribeVideo(item)"
                                    class="icon iconfont iconshipinluxiang"
                                ></i>
                                <i
                                    v-else-if="item.videoStream === 1 &&LiveConferenceData.currentSubscribeAux.includes(item.uid)"
                                    @click="toggleRemoteSubscribeVideo(item)"
                                    class="icon iconfont iconshipinluxiang"
                                >
                                    <i class="icon iconfont iconduihao"></i>
                                </i>
                                <i
                                    v-else-if="item.videoStream === 0"
                                    class="icon iconfont iconweifaxianshexiangtou"
                                ></i>
                            </span>
                            <!-- 麦克风操作：区分本人与远端 -->
                            <span class="operate_btn" v-if="item.userid === user.uid">
                                <i
                                    v-if="item.audioStream === 1"
                                    @click="toggleSelfAudio(item)"
                                    class="icon iconfont iconmic-line"
                                ></i>
                                <i
                                    v-else
                                    @click="toggleSelfAudio(item)"
                                    class="icon iconfont iconmic-off-line"
                                ></i>
                            </span>
                            <span class="operate_btn" v-else>
                                <!-- 仅当远端正在开麦时支持主持人一键静音 -->
                                <i
                                    v-if="item.audioStream === 1"
                                    @click="toggleRemoteMuteAudio(item)"
                                    class="icon iconfont iconmic-line"
                                ></i>
                                <i
                                    v-else
                                    class="icon iconfont iconmic-off-line"
                                ></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel_operate" v-if="allForbiddenSpeakAuth">
            <el-button type="primary" class="btn" @click="openForbiddenAllSpeakVisible">{{
                $t('all_forbidden_speak')
            }}</el-button>
        </div>
    </div>
</template>
<script>
import base from "../../lib/base";
import { openVisitingCard } from "../../lib/common_base";
import { getLiveRoomObj, getLocalAvatar} from "../../lib/common_base";
import Tool from "@/common/tool";
export default {
    mixins: [base],
    props: {
        cid: {
            type: [String,Number],
            default: 0,
        }
    },
    name: "ManageVideoMember",
    components: {},
    data() {
        return {
            getLocalAvatar,
            speakingList: [], //正在发言
            applyingList: [], //正在申请
            onlineList: [], //仅在线
            offLineList: [], //离线
            authorizeList: [], //授权发言但没接通
            search: "",
            attendVoiceFilterList: [], //筛选后的数据
            onlineFilterList: [],
            offLineFilterList: [],
            authorizeFilterList: [],
            forbiddenValue: false, //是否允许自我解除静音
            forbiddenAllSpeakVisible: false,
        };
    },
    computed: {
        isPcWeb() {
            return this.systemConfig.client_type.Client == window.clientType;
        },
        conversation() {
            return this.$store.state.conversationList[this.cid];
        },
        attendeeList() {
            if (!this.conversation) {
                return [];
            } else {
                return this.parseObjToArr(this.conversation.attendeeList);
            }
        },
        LiveConferenceData(){
            return this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].LiveConferenceData || {}
        },
        allUserList() {
            let list = [
                // {
                //     uid:9001,
                //     videoStream:1,
                //     audioStream:1,
                //     userid:128,
                // }
            ];

            if (this.LiveConferenceData.roomUserMap && this.attendeeList) {
                Object.values(this.LiveConferenceData.roomUserMap).map((item) => {
                    this.attendeeList.forEach((ele) => {
                        if (Number(ele.userid) === Number(item.user_id)) {
                            if (item.streamType === "aux") {
                                //只统计辅流
                                if (ele.userid === this.user.uid) {
                                    // 本人
                                    ele.isSelf = 1;
                                } else {
                                    ele.isSelf = 0;
                                }
                                list.push({ ...item, ...ele });
                            }
                        }
                    });
                });
            }
            if (list.length > 0) {
                list.sort(this.sortAttendeeList);
            }
            this.$emit('updateLiveUserListLength',list.length)
            return list;
        },
        allForbiddenSpeakAuth(){
            if(this.cid){
                return this.$checkPermission({
                    conversationPermissionKey:'conference.mute_all'
                },{conversationId:this.cid})||this.LiveConferenceData.isHost
            }else{
                return false
            }
        }
    },
    created() {},
    mounted() {
        this.$nextTick(() => {});
    },
    methods: {
        dealApply(isAgree, user) {
            //处理申请发言

        },
        disableSpeak(user) {
            //处理禁用发言
            this.$root.eventBus.$emit("hideRealTimeVideo");
            this.$confirm(this.$t('disable_speak_comfirm'), this.$t('tip_title'), {
                confirmButtonText: this.$t('confirm_button_text'),
                cancelButtonText: this.$t('cancel_button_text'),
                type: "warning",
            })
                .then(() => {})
                .catch(() => {});
        },
        clickMember(item) {
            if (this.$route.meta.inConference) {
                //直播中不能打开个人名片
                return;
            }
            this.$root.eventBus.$emit("hideRealTimeVideo");
            openVisitingCard(item, 3);
        },
        openForbiddenAllSpeakVisible() {
            let liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            this.$confirm(this.$t('all_members_silenced'), this.$t('tip_title'), {
                confirmButtonText: this.$t('confirm_button_text'),
                cancelButtonText: this.$t('cancel_button_text'),
                type: "warning",
            })
                .then(async () => {
                    await liveRoom.forbiddenSpeak({
                        isForce: false,
                    });
                })
                .catch(() => {});
        },
        handleHandsUp(user) {},

        sortAttendeeList(a, b) {
            if (a.isHost === b.isHost) {
                if (a.isSelf > b.isSelf || a.isHandsUp > b.isHandsUp) {
                    //3.本人或者举手者再往前排
                    return -1;
                } else {
                    return 1;
                }
            } else {
                if (a.isHost > b.isHost) {
                    //1.主讲人挪到最前面
                    return -1;
                } else {
                    return 1;
                }
            }
        },
        checkManagerAuth(){
            let liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return false;
            }
            return liveRoom.checkManagerAuth();
        },
        // 本人摄像头开关
        toggleSelfVideo :Tool.throttle(function(item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            const isMute = item.videoStream === 1; // 当前开着摄像头则执行关闭
            liveRoom.MuteLocalVideoStream({
                uid: liveRoom.data.localAuxUid,
                isMute,
            });
        },800,true),

        // 他人辅流视频的订阅/取消
        toggleRemoteSubscribeVideo :Tool.throttle(async function(item){
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            if (this.LiveConferenceData.currentSubscribeAux.includes(item.uid)) {
                liveRoom.StopSubscribeRemoteStreamAux(item.uid);
            } else {
                const { canSubscribeAuxNum } = await liveRoom.checkAfterSubscribeAux();
                if (canSubscribeAuxNum === 0) {
                    return;
                }
                liveRoom.SubscribeRemoteStreamAux(item.uid);
            }
        },800,true),
        // 本人麦克风开关
        toggleSelfAudio :Tool.throttle(function(item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            const isMute = item.audioStream === 1; // 当前开麦时执行静音
            liveRoom.MuteLocalAudioStream({
                uid: liveRoom.data.localAuxUid,
                isMute,
            });
        },800,true),

        // 远端用户麦克风静音（仅主持人可操作）
        toggleRemoteMuteAudio :Tool.throttle(function(item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            if (!liveRoom.data.isHost) {
                // 非主持人无操作权限
                return;
            }
            // 远端正在开麦，执行静音
            liveRoom.forbiddenSpeak({
                isForce: false,
                userId: item.userid,
            });
        },800,true),
    },
};
</script>
<style lang="scss" scoped>
.manage_video_member {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .manage_video_member_panel {
        color: #333;
        overflow: auto;
        background: #fff;
        flex: 1;
        .icon-close {
            float: right;
            font-size: 1.4rem;
            line-height: 1;
            color: #fff;
        }
        .group {
            .group_title {
                font-size: 0.9rem;
                color: #000;
                padding: 1rem 0;
            }
            .group_members {
                margin-top: 0.4rem;
                width: 100%;
                min-height: 60px;
                padding: 0px 20px;
                box-sizing: border-box;
                .group_member_item {
                    display: flex;
                    border-bottom: 1px solid #ccc;
                    padding: 10px 0px;
                    box-sizing: border-box;
                    .group_user_wrapper {
                        flex: 1;
                        position: relative;
                        display: flex;
                        .user_info {
                            margin-left: 10px;
                            display: flex;
                            flex-direction: column;
                            font-size: 14px;
                            flex: 1;
                            .tag_box {
                                .tag {
                                    background-color: #01c59d;
                                    color: #fff;
                                    line-height: 1;
                                    padding: 2px 4px;
                                    border-radius: 4px;
                                    font-size: 12px;
                                    margin-right: 5px;
                                }
                            }
                            .user_info_introduction{
                                display: -webkit-box;
                                -webkit-line-clamp: 1;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                line-height: 2em;
                                color: #999;
                                cursor: pointer;
                            }
                        }
                    }
                    .group_user_operate {

                        display: flex;
                        justify-content: flex-end;
                        align-items: center;
                        .operate_btn {
                            position: relative;
                            i {
                                width: 1.4rem;
                                height: 1.4rem;
                                margin-left: 0.5rem;
                                fill: #ccc;
                            }
                            .iconfont {
                                font-size: 1.4rem;
                                height: 1.4rem;
                                margin-left: 0.5rem;
                                color: #8d8d8d;
                            }
                            .iconduihao {
                                position: absolute;
                                top: 0.5rem;
                                left: 0;
                                color: green;
                                width: 1.4rem;
                            }
                        }
                    }
                    .voice_manager {
                        background-color: #00c59d;
                        font-size: 0.6rem;
                        color: #fff;
                        position: absolute;
                        right: -1rem;
                        bottom: -0.3rem;
                        padding: 0.1rem 0.2rem;
                        border-radius: 0.2rem;
                    }

                    .authorize {
                        border-radius: 0.2rem;
                        background-color: #00c59d;
                        display: inline-block;
                        font-size: 0.7rem;
                        padding: 0 0.3rem;
                        color: #fff;
                    }
                }
            }
        }
    }
    .panel_operate {
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        .btn {
            flex: 1;
            margin: 0px 15px;
            border-radius: 20px;
        }
    }
}
</style>
