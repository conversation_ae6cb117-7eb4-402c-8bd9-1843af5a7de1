<template>
    <div class="gallery_file_list_page">
        <div class="gallery_file_list_container">
            <div class="group_all_files" ref="fileList">
                <template v-if="span > 1">
                    <template v-if="showMonth">
                        <van-list
                            v-model="loading"
                            @load="onScrollToBottom"
                            :error.sync="errorLoaded"
                            :error-text="$t('loading_fail')"
                            :loading-text="$t('bottom_loading_text')"
                            :immediate-check="false"
                            :finished="finished"
                            :finished-text="$t('no_more_text')"
                            style="height: 100%; overflow: auto"
                        >
                            <!-- <van-cell v-for="item in list" :key="item" :title="item" /> -->
                            <div
                                class="observe_gallery_file_item"
                                v-for="(item, index) in renderList"
                                :key="item.send_ts"
                            >
                                <div class="month">{{ item.send_ts }}</div>
                                <template v-for="(file, file_index) in item.list">
                                    <div
                                        :class="['file_item', `file_item_span${span}`]"
                                        @click.stop="clickItem(index, file_index, item.list, file)"
                                        :key="file.resource_id+'_'+ file.send_ts"
                                        :dataId="file.resource_id"
                                    >
                                        <v-touch @press="pressMessage">
                                            <div class="file_container">
                                                <common-image
                                                    :fileItem="file"
                                                    :isMini="span >= 4"
                                                    :viewWindowDom="$refs.fileList"
                                                    :isObserve="isScrolling"
                                                ></common-image>
                                            </div>
                                            <i
                                                class="iconfont icon-comment1"
                                                v-show="
                                                    gallery.commentObj[file.resource_id] &&
                                                    gallery.commentObj[file.resource_id].comment_list.length
                                                "
                                            >
                                                <span>{{
                                                    gallery.commentObj[file.resource_id] &&
                                                    gallery.commentObj[file.resource_id].comment_list.length
                                                }}</span>
                                            </i>
                                            <van-checkbox-group
                                                v-if="editFileImage"
                                                v-model="fileCheckList"
                                            >
                                                <van-checkbox :name="file.resource_id" checked-color="#00c59d"></van-checkbox>
                                            </van-checkbox-group>
                                        </v-touch>
                                        <span v-show="false">{{ checkResourceTempState(file.resource_id) }}</span>
                                    </div>
                                </template>
                            </div>
                        </van-list>
                    </template>
                    <template v-else>
                        <van-list
                            v-model="loading"
                            @load="onScrollToBottom"
                            :error.sync="errorLoaded"
                            :error-text="$t('loading_fail')"
                            :loading-text="$t('bottom_loading_text')"
                            :immediate-check="false"
                            :finished="finished"
                            :finished-text="$t('no_more_text')"
                            style="height: 100%; overflow: auto"
                        >
                            <!-- <van-cell v-for="item in list" :key="item" :title="item" /> -->
                            <div class="observe_gallery_file_item">
                                <template v-for="(file, file_index) in renderList">
                                    <div
                                        :class="['file_item', `file_item_span${span}`]"
                                        @click.stop="clickItem(0, file_index, renderList, file)"
                                        :dataId="file.resource_id"
                                        :key="file.resource_id"
                                    >
                                        <v-touch @press="pressMessage">
                                            <div class="file_container">
                                                <common-image
                                                    :fileItem="file"
                                                    :isMini="span >= 4"
                                                    :viewWindowDom="$refs.fileList"
                                                    :isObserve="isScrolling"
                                                ></common-image>
                                            </div>
                                            <i
                                                class="iconfont icon-comment1"
                                                v-show="
                                                    gallery.commentObj[file.resource_id] &&
                                                    gallery.commentObj[file.resource_id].comment_list.length
                                                "
                                            >
                                                <span>{{
                                                    gallery.commentObj[file.resource_id] &&
                                                    gallery.commentObj[file.resource_id].comment_list.length
                                                }}</span>
                                            </i>
                                            <van-checkbox-group
                                                v-if="editFileImage"
                                                v-model="fileCheckList"
                                            >
                                                <van-checkbox :name="file.resource_id" checked-color="#00c59d"></van-checkbox>
                                            </van-checkbox-group>
                                        </v-touch>
                                        <span v-show="false">{{ checkResourceTempState(file.resource_id) }}</span>
                                    </div>
                                </template>
                            </div>
                        </van-list>
                    </template>
                </template>
                <template v-else>
                    <DynamicScroller
                        :items="renderList"
                        :min-item-size="transferRemToHeight(6)"
                        ref="scroller"
                        v-if="renderList.length > 0"
                        @scroll.native="handleScrollThrottle"
                        key-field="indexId"
                        :buffer="6000"
                        style="height: 100%"
                    >
                        <template v-slot="{ item, index, active }">
                            <DynamicScrollerItem :item="item" :active="active" :data-index="index">
                                <div :id="item.id" class="observe_gallery_file_item">
                                    <template v-for="(file, file_index) of item.list">
                                        <v-touch
                                            @press="pressMessage"
                                            :class="['file_item_block']"
                                            @click.native.stop="clickItem(index, file_index, item.list, file)"
                                            :key="file.resource_id"
                                        >
                                            <div class="file_item_left">
                                                <div class="file_container">
                                                    <common-image
                                                        :fileItem="file"
                                                        :isMini="span >= 4"
                                                        :viewWindowDom="$refs.fileList"
                                                        :isObserve="isScrolling"
                                                        :showText="false"
                                                    ></common-image>
                                                </div>
                                                <i
                                                    class="iconfont icon-comment1"
                                                    v-show="
                                                        gallery.commentObj[file.resource_id] &&
                                                        gallery.commentObj[file.resource_id].comment_list.length
                                                    "
                                                >
                                                    <span>{{
                                                        gallery.commentObj[file.resource_id] &&
                                                        gallery.commentObj[file.resource_id].comment_list.length
                                                    }}</span>
                                                </i>
                                                <van-checkbox-group
                                                    v-if="editFileImage"
                                                    v-model="fileCheckList"
                                                >
                                                    <van-checkbox
                                                    :name="file.resource_id"
                                                     checked-color="#00c59d"
                                                     :ref="'checkItem_' + (index * item.list.length + file_index)"
                                                     ></van-checkbox>
                                                </van-checkbox-group>
                                            </div>
                                            <div class="file_item_right">
                                                <p class="longwrap" :title="getFileItemSubject(file)">{{ getFileItemSubject(file) }}</p>
                                                <p class="longwrap">{{ getFileItemSender(file) }}</p>
                                                <p class="longwrap">{{ getFileItemTime(file) }}</p>
                                            </div>
                                            <span v-show="false">{{ checkResourceTempState(file.resource_id) }}</span>
                                        </v-touch>
                                    </template>
                                </div>
                            </DynamicScrollerItem>
                        </template>
                        <template #after>
                            <div class="loader-wrapper" v-if="loadingStatus === 'loading'">
                                <div class="loader"></div>
                            </div>
                        </template> </DynamicScroller
                ></template>
            </div>
        </div>
        <ReviewEditDialog
            :message="currentFile"
            ref="reviewEditDialog"
            @updateReviewForm="updateReviewForm"
        ></ReviewEditDialog>
        <ImageRenameDialog
        :message="currentFile"
            v-model="imageRenameVisible"
        >
        </ImageRenameDialog>
        <div class="share_operate" v-if="editFileImage" id="edit_operate_tools">
            <i
                v-show="checkShowEditBtn"
                class="icon iconfont icon-xiugai"
                @click="editResource"
            ></i>
            <i class="icon iconfont icon-share" @click="transmitToUltrasync"></i>
            <i class="icon iconfont icon-delete"  v-show="checkShowDeleteBtn" @click="deleteChatMessages"></i>
            <i class="icon iconfont icon-share-1-copy" v-if="isShowWechat" @click="shareToWechat"></i>
            <i class="icon iconfont icon-heart" @click="folderCollect"></i>
            <i
                class="icon iconfont icon-search-image f1"
                @click="searchInCaseData"
                v-if="
                    functionsStatus.breastCases &&
                    systemConfig.serverInfo.ai_searcher_server &&
                    systemConfig.serverInfo.ai_searcher_server.enable &&
                    !isCE
                "
            ></i>
            <!--i class="icon iconfont icon-list fl" @click="openExamManager"></i-->
            <span v-show="fileCheckList.length == 0">{{ $t('not_choose_text') }}</span>
            <span v-show="fileCheckList.length != 0">{{ $t('has_chosen_text') }}({{ fileCheckList.length }})</span>
            <i class="icon iconfont icon-close" @click="cancelShare"></i>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import { Toast } from 'vant';
import Tool from "@/common/tool.js";
import {getRecordSubject, getClipSubject,getResourceTempStatus,getResourceTempState } from "../lib/common_base";
import { initImportExamImageTempQueue } from "../lib/common_send_message";
import send_message from "../lib/send_message.js";
import { Dialog, List, Checkbox, CheckboxGroup } from "vant";
import ReviewEditDialog from "./messageItem/reviewEditDialog.vue";
import service from "../service/service";
import { cloneDeep } from "lodash";
import moment from "moment";
import ImageRenameDialog from '../components/imageRenameDialog.vue'
export default {
    name: "galleryFileList",
    permission: true,
    mixins: [base, send_message],
    props: {
        galleryList: {
            type: Array,
            default: () => {
                return [];
            },
        },
        span: {
            type: Number,
            default: 4,
        },
        from: {
            type: String,
            default: "",
        },
        loadingStatus: {
            type: String,
            default: "loading",
        },
        timeStr: {
            type: String,
            default: "send_ts",
        },
        showMonth: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        [Dialog.Component.name]: Dialog.Component,
        ReviewEditDialog,
        VanList: List,
        ImageRenameDialog,
        VanCheckbox: Checkbox,
        VanCheckboxGroup: CheckboxGroup
    },
    data() {
        return {
            getResourceTempState,
            getRecordSubject,
            getClipSubject,
            editFileImage: false,
            fileCheckList: [],
            cid: 0,
            currentFile: {},
            lastScrollTop: 0,
            lastTimestamp: 0,
            scrollSpeed: 0,
            renderList: [],
            isScrolling: false,
            isScrollingTimeout: null,
            loading: false,
            errorLoaded: false,
            finished: false,
            imageRenameVisible:false
        };
    },
    computed: {
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        globalParams() {
            return this.$store.state.globalParams;
        },
        checkShowEditBtn(){
            if(this.from === 'chatComponent' || this.from === 'groupAllFileList' || this.from === 'groupFavorite'){
                if(this.fileCheckList.length == 1 && this.galleryList.length > 0){
                    const file = this.getFileByResourceId(this.fileCheckList[0]);
                    if(this.$checkPermission({conversationPermissionKey: 'resource.edit'}, {
                        conversationId: this.cid,
                        message: file,
                    })){
                        const imageType = [this.systemConfig.msg_type.Image, this.systemConfig.msg_type.Frame, this.systemConfig.msg_type.OBAI];
                        const videoType = [this.systemConfig.msg_type.Video, this.systemConfig.msg_type.Cine];
                        if(imageType.includes(file.msg_type)||videoType.includes(file.msg_type)){
                            return true
                        }
                        if(file.msg_type ===this.systemConfig.msg_type.RealTimeVideoReview){
                            return true
                        }
                    }
                }
            }
            return false
        },
        checkShowDeleteBtn(){
            if(this.from === 'chatComponent' || this.from === 'groupAllFileList'||this.from === 'groupFavorite'){
                if(this.fileCheckList.length == 1 && this.galleryList.length > 0){
                    const file = this.getFileByResourceId(this.fileCheckList[0]);
                    if(this.$checkPermission({conversationPermissionKey: 'resource.delete'}, {
                        conversationId: this.cid,
                        message: file,
                    })){
                        return true
                    }
                }
            }

            return false
        }

    },
    watch: {
        galleryList: {
            handler(val) {
                const list = cloneDeep(val);
                if (list.length === 0) {
                    this.editFileImage = false;
                }
                this.renderList = this.formatGalleryList(list);
            },
            immediate: true,
        },
        fileCheckList:{
            handler(val){
                console.log(val)
            }
        }
    },
    beforeDestroy() {},
    created() {
        this.cid = this.$route.params.cid;
        this.fileCheckList = [];
        this.handleScrollThrottle = Tool.throttle(this.handleScroll, 500);
    },
    mounted() {
        this.$nextTick(() => {});
    },
    methods: {
        getFileByResourceId(resource_id) {
            // 直接使用 file.resource_id === resource_id 作为 find 方法的回调函数
            const file = this.galleryList.find(file => file.resource_id === resource_id);
            console.log(file)
            return file; // 如果找到，返回 file；如果没有找到，返回 undefined
        },
        getRealIndex(index, file_index,list){
            let realIndex = 0
            if(this.showMonth){
                this.renderList.forEach((item,idx)=>{
                    if(index>idx){
                        realIndex+=item.list.length
                    }
                })
                realIndex+=file_index
            }else{
                realIndex = index * list.length + file_index;
            }
            return realIndex
        },
        clickItem(index, file_index, list, file) {
            let realIndex = this.getRealIndex(index,file_index,list)

            if (this.editFileImage) {
                //编辑图片
                // let image = this.$refs["checkItem_" + realIndex][0];
                // let cancelCheck = image.checked;
                // if (this.fileCheckList.length >= this.systemConfig.maxShareNumber && !cancelCheck) {
                //     Toast(this.$t('max_share_image'));
                //     return;
                // }
                // // image.click()
                // if (!cancelCheck) {
                //     this.fileCheckList.push(realIndex);
                // } else {
                //     this.fileCheckList = this.fileCheckList.filter((ele) => ele !== realIndex);
                // }
            } else {
                this.$store.commit("gallery/setGallery", {
                    list: this.galleryList,
                    index: realIndex,
                });
                this.$nextTick(() => {
                    this.$router.push(`${this.$route.path}/gallery`);
                });
                this.$emit("clickItem", realIndex);
            }
        },
        getShowDes(name) {
            if (name.length > 8) {
                name = name.substring(0, 8) + "......";
            }
            return name;
        },
        pressMessage() {
            if (!this.checkHasOthersEditOperate()) {
                this.editFileImage = true;
            } else {
                Toast(this.$t('cannot_edit_multiple_modules'));
            }
        },
        checkHasOthersEditOperate() {
            const hasOthersOpera = document.querySelector("#edit_operate_tools");
            return hasOthersOpera ? true : false;
        },
        shareToWechat() {
            let list = this.getChooseImage();
            this.$root.eventBus.$emit("shareToWechatHandler", list);
        },
        folderCollect() {
            let list = this.getChooseImage();
            this.$root.eventBus.$emit("folderCollectHandler", list, this.cancelShare);
        },
        searchInCaseData() {
            let list = this.getChooseImage();
            if (list && list.length > 1) {
                Toast(this.$t('searc_in_case_database_many_picture'));
            } else if (list && list.length == 1) {
                let msg = list[0];
                let islegal = Tool.isLegalForematForSearchImage(msg);
                if (islegal) {
                    let pattern = new RegExp("^data:image.*", "i");
                    msg.realUrl = msg.realUrl && pattern.test(msg.realUrl) ? "" : msg.realUrl;
                    let searchParams = {
                        type: "url", //text,url,file
                        content: msg,
                    };
                    this.$store.commit("caseDatabase/updateSearchParams", searchParams);
                    this.$router.push({ path: "/index/case_database" });
                } else {
                    Toast(this.$t('picture_is_only_jpg_jpeg_bmp_png'));
                }
            } else {
                Toast(this.$t('searc_in_case_database_one_picture'));
            }
        },
        transmitToUltrasync() {
            let list = this.getChooseImage();
            if (list.length == 0) {
                Toast(this.$t('choose_transmit_tip'));
            } else {
                let path = this.$route.path;
                path += "/transmit";
                this.$root.transmitTempList = list;
                this.$router.push(path);
                this.cancelShare();
            }
        },
        async deleteChatMessages() {
            let list = this.getChooseImage();
            console.log(list,1)
            if (this.$listeners.removeResourceItemFromCategory) {
                this.$emit("removeResourceItemFromCategory", {
                    list,
                    callback: () => {
                        this.editFileImage = false;
                    },
                });
            } else {
                // this.tryToDeleteMessages(this.conversation.id, list, (err) => {
                //     if (!err) {
                //         this.cancelShare();
                //     }
                // });
                try {
                    await this.deleteResourceByGroupId(list[0])
                } catch (error) {
                    console.error(error)
                } finally{
                    this.cancelShare();
                }

            }
        },
        getChooseImage() {
            let list = [];
            this.fileCheckList.forEach(resource_id=>{
                let file = this.getFileByResourceId(resource_id);
                list.push(file)
            })
            return list;
        },
        cancelShare() {
            this.editFileImage = false;
            this.fileCheckList = [];
        },
        openExamManager() {
            let list = this.getChooseImage();
            initImportExamImageTempQueue(list, function (err) {
                if (!err) {
                    this.$router.push(this.$route.fullPath + "/exam_manager");
                }
            });
        },
        async openEditReviewModal() {
            let list = this.getChooseImage();
            if (list.length != 1) {
                Toast(this.$t('choose_only_one_file_to_handle'));
                return;
            }
            this.$set(this, "currentFile", list[0]);
            service
                .checkActionPermissions({
                    action: "conference.update.recording",
                    businessData: {
                        resource_id: this.currentFile.resource_id,
                        group_id: this.currentFile.group_id,
                    },
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        if (!res.data.data.hasPermission) {
                            Toast(this.$t('no_permission_operate'));
                            return;
                        }
                        this.$nextTick(() => {
                            this.$refs[`reviewEditDialog`].openEditTargetModal();
                        });
                    } else {
                        Toast(this.$t(res.data.key));
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        formatGalleryList(oList) {
            if (this.span === 1) {
                let arr = [];
                let list = [];
                oList.forEach((item) => {
                    if (this.getResourceTempState(item.resource_id) === 1) {
                        list.push(item);
                    }
                });
                const splitArray = Tool.splitArray(list, this.span);
                splitArray.forEach((item, index) => {
                    let id = "";
                    item.forEach((ele, index) => {
                        id += `${ele.resource_id}`;
                    });
                    arr.push({
                        id,
                        indexId: index,
                        list: item,
                    });
                });
                return arr;
            } else {
                if (!this.showMonth) {
                    return oList;
                }
                const categorizedArr = [];
                oList.forEach((album) => {
                    const sendTs = moment(album.send_ts);
                    const yearMonth = sendTs.format("YYYY-MM");

                    if (this.getResourceTempState(album.resource_id) === 1) {
                        // 查找已存在的年月对象
                        const existingObj = categorizedArr.find((obj) => obj.send_ts === yearMonth);

                        if (existingObj) {
                            existingObj.list.push(album);
                        } else {
                            categorizedArr.push({
                                send_ts: yearMonth,
                                list: [album],
                            });
                        }
                    }
                });
                // 根据send_ts从新到旧排序
                categorizedArr.sort((a, b) => {
                    return moment(b.send_ts, "YYYY-MM").diff(moment(a.send_ts, "YYYY-MM"));
                });
                return categorizedArr;
            }
        },
        handleScroll(data) {
            let parentScroll = this.$refs.scroller.$refs.scroller;
            let scrollTop = parentScroll.$el.scrollTop;
            let scrollSate = parentScroll.getScroll();
            let timestamp = new Date().getTime();
            // 计算滚动距离的差值
            let scrollDistance = scrollTop - this.lastScrollTop;
            // 计算时间的差值
            let timeDiff = timestamp - this.lastTimestamp;
            // 计算滚动速度
            this.scrollSpeed = Math.abs(scrollDistance / timeDiff);
            // 更新上一次的滚动位置和时间
            this.lastScrollTop = scrollTop;
            this.lastTimestamp = timestamp;
            if (scrollTop + parentScroll.$el.clientHeight >= parentScroll.$el.scrollHeight - 10) {
                this.onScrollToBottom();
            }
            if (this.scrollSpeed > 10 && scrollDistance < -2000) {
                parentScroll.scrollToPosition(0);
            } else if (this.scrollSpeed > 10 && scrollDistance > 2000) {
                this.$refs.scroller.scrollToBottom();
            }
            this.isScrolling = true;
            if (this.isScrollingTimeout) {
                clearTimeout(this.isScrollingTimeout);
                this.isScrollingTimeout = null;
            }
            this.isScrollingTimeout = setTimeout(() => {
                this.isScrolling = false;
            }, 300);
        },
        onScrollToBottom() {
            this.loading = true;
            this.$emit("onScrollToBottom", (status) => {
                this.loading = false;
                if (status === "error") {
                    this.errorLoaded = true;
                    return;
                }
                if (status === "finished") {
                    this.finished = true;
                }
            });
        },
        transferRemToHeight(rem) {
            return Tool.transferRemToHeight(rem);
        },
        getFileItemSubject(file) {
            if (file.msg_type == this.systemConfig.msg_type.RealTimeVideoReview) {
                return this.getRecordSubject(file);
            } else if (file.msg_type == this.systemConfig.msg_type.VIDEO_CLIP) {
                return this.getClipSubject(file);
            } else if (
                file.msg_type == this.systemConfig.msg_type.Image ||
                file.msg_type == this.systemConfig.msg_type.OBAI ||
                file.msg_type == this.systemConfig.msg_type.Frame
            ) {
                return getResourceTempStatus(file.resource_id,'custom_file_name')||file.custom_file_name||file.file_name||`${this.$t('msg_type_image')}`;
            } else if (
                file.msg_type === this.systemConfig.msg_type.Cine ||
                file.msg_type === this.systemConfig.msg_type.Video
            ) {
                return getResourceTempStatus(file.resource_id,'custom_file_name')||file.custom_file_name||file.file_name||`${this.$t('msg_type_video')}`;
            }
        },
        getFileItemSender(file) {
            if (file.msg_type == this.systemConfig.msg_type.RealTimeVideoReview) {
                return `${this.$t('moderator')}: ${file.live_record_data.speaker || file.live_record_data.creator_name}`;
            } else if (file.msg_type == this.systemConfig.msg_type.VIDEO_CLIP) {
                return `${this.$t('sender')}: ${file.nickname}`;
            } else if (
                file.msg_type == this.systemConfig.msg_type.Image ||
                file.msg_type == this.systemConfig.msg_type.OBAI ||
                file.msg_type == this.systemConfig.msg_type.Frame
            ) {
                return `${this.$t('sender')}: ${file.nickname}`;
            } else if (
                file.msg_type === this.systemConfig.msg_type.Cine ||
                file.msg_type === this.systemConfig.msg_type.Video
            ) {
                return `${this.$t('sender')}: ${file.nickname}`;
            }
        },
        getFileItemTime(file) {
            if (file.msg_type == this.systemConfig.msg_type.RealTimeVideoReview) {
                return this.formatTime(this.timeStr === "send_ts" ? file.start_ts : file[this.timeStr]);
            } else if (file.msg_type == this.systemConfig.msg_type.VIDEO_CLIP) {
                return this.formatTime(this.timeStr === "send_ts" ? file.start_ts : file[this.timeStr]);
            } else if (
                file.msg_type == this.systemConfig.msg_type.Image ||
                file.msg_type == this.systemConfig.msg_type.OBAI ||
                file.msg_type == this.systemConfig.msg_type.Frame
            ) {
                return this.formatTime(file[this.timeStr]);
            } else if (
                file.msg_type === this.systemConfig.msg_type.Cine ||
                file.msg_type === this.systemConfig.msg_type.Video
            ) {
                return this.formatTime(file[this.timeStr]);
            }
        },
        checkResourceTempState(resource_id) {
            const resourceTempStatus = this.$store.state.resourceTempStatus;
            if (resourceTempStatus.hasOwnProperty(resource_id)) {
                if (
                    resourceTempStatus[resource_id].hasOwnProperty("state") &&
                    resourceTempStatus[resource_id].state !== 1
                ) {
                    this.renderList = this.formatGalleryList(this.galleryList);
                }
            }
        },
        updateReviewForm(data) {
            console.log(data);
        },
        openEditRenameModal(){
            let list = this.getChooseImage();
            if (list.length != 1) {
                Toast(this.$t('choose_only_one_file_to_handle'));
                return;
            }
            this.$set(this, "currentFile", list[0]);
            this.imageRenameVisible = true
            this.editFileImage = false
        },
        editResource(){
            const file = this.getFileByResourceId(this.fileCheckList[0]);;
            const imageType = [this.systemConfig.msg_type.Image, this.systemConfig.msg_type.Frame, this.systemConfig.msg_type.OBAI];
            const videoType = [this.systemConfig.msg_type.Video, this.systemConfig.msg_type.Cine];
            if(imageType.includes(file.msg_type)||videoType.includes(file.msg_type)){
                this.openEditRenameModal()
            }else if(file.msg_type ===this.systemConfig.msg_type.RealTimeVideoReview){
                this.openEditReviewModal()
            }
        }
    },
};
</script>
<style lang="scss">
.gallery_file_list_page {
    height: 100%;
    .gallery_file_list_container {
        height: 100%;
        .group_all_files {
            height: 100%;
            position: relative;
            z-index: 1;
            transform: translate3d(0, 0, 0);
            overflow: hidden;
            padding:0.5rem;
            .observe_gallery_file_item {
                overflow: hidden;
                .month {
                    padding: 0.4rem;
                    font-size: 0.8rem;
                }
            }
            .file_item,
            .file_item_block {
                float: left;
                width: 100%;
                border: 1px solid #fff;
                box-sizing: border-box;
                position: relative;
                &.file_item_span2 {
                    width: 50%;
                }
                &.file_item_span3 {
                    width: 33.33%;
                }
                &.file_item_span4 {
                    width: 25%;
                }
                &.file_item_span5 {
                    width: 20%;
                }
                &.file_item_span6 {
                    width: 16.66%;
                }
                &.file_item_span1 {
                    width: 100%;
                }
                .file_container {
                    position: relative;
                    padding-top: calc(100% - 25px);
                    height: 0;
                    background-color: #333;
                    overflow: hidden;
                    border-radius: 6px;
                    margin-bottom: 10px;
                    width: calc(100% - 6.6px);
                    margin-right: 6.6px;

                    .loadMore_btn {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        top: 0;
                        left: 0;
                    }
                }
                .van-checkbox-group {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    border-radius: 0.75rem;
                    top: 0px;
                    right: 0px;
                    z-index: 1000;

                    .van-checkbox{
                        width: 100%;
                        height: 100%;
                        padding: 10px;
                        box-sizing: border-box;
                        justify-content: flex-end;
                        align-items: flex-end;
                    }
                }
                .icon-comment1 {
                    position: absolute;
                    z-index: 2;
                    color: #f00;
                    font-size: 1rem;
                    color: #56c7fd;
                    line-height: 1rem;
                    top: 0.3rem;
                    right: 0.6rem;
                    span {
                        color: #fff;
                        position: absolute;
                        font-size: 0.6rem;
                        top: 0.1rem;
                        left: 50%;
                        transform: translate(-50%, 0);
                        line-height: 0.6rem;
                        white-space: nowrap;
                    }
                }
            }
            .file_item_block {
                height: 5rem;
                float: unset;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .file_item_left {
                    width: 40%;
                    .file_container {
                        height: 4rem;
                        padding-top: 0;
                    }
                }
                .file_item_right {
                    width: 60%;
                    padding-left: .5rem;
                    box-sizing: border-box;
                    p {
                        font-size: 0.6rem;
                        line-height: 1.3rem;
                    }
                }
            }
        }
        .loader-wrapper {
            padding: 1em;
            background: #ffffff;
        }
        .loader {
            font-size: 10px;
            margin: 0px auto;
            text-indent: -9999em;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ffffff;
            background: linear-gradient(to right, #9b4dca 10%, rgba(255, 255, 255, 0) 42%);
            position: relative;
            animation: load3 1.4s infinite linear;
            transform: translateZ(0);
        }
        .loader:before {
            width: 50%;
            height: 50%;
            background: #9b4dca;
            border-radius: 100% 0 0 0;
            position: absolute;
            top: 0;
            left: 0;
            content: "";
        }
        .loader:after {
            background: #ffffff;
            width: 75%;
            height: 75%;
            border-radius: 50%;
            content: "";
            margin: auto;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }
        @-webkit-keyframes load3 {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        @keyframes load3 {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    }
}
.share_operate {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    box-sizing: border-box;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    color: #007aff;
    padding: 0.3rem 0.5rem;
    border-radius: 0.8rem;
    display: flex;
    z-index: 2;
    border: 1px solid #eee;
    user-select: none;
    -webkit-user-select: none;
    i {
        font-size: 0.9rem;
        margin: 0 0.4rem;
    }
    span {
        min-width: 3.5rem;
        max-width: 10rem;
        text-align: center;
        font-size: 0.7rem;
        margin-top: 0.1rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

/* 响应式调整 */
@media screen and (min-width: 768px) {
    .gallery_file_list_page {
        .gallery_file_list_container {
            .group_all_files {
                .file_item {
                    &.file_item_span5, &.file_item_span6 {
                        .file_container {
                            margin-right: 0.2rem;
                            margin-bottom: 0.2rem;
                        }
                    }
                }
            }
        }
    }
}

@media screen and (min-width: 1024px) {
    .gallery_file_list_page {
        .gallery_file_list_container {
            .group_all_files {
                .file_item {
                    &.file_item_span6 {
                        .file_container {
                            margin-right: 0.15rem;
                            margin-bottom: 0.15rem;
                        }
                    }
                }
            }
        }
    }
}
</style>
