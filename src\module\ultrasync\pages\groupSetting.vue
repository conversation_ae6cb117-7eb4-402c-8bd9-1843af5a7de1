<template>
    <transition name="slide">
        <div class="group_setting_page third_level_page">
            <div v-show="settingLoading" class="full_loading_spinner van_loading_spinner">
                <van-loading color="#00c59d" />
            </div>
            <mrHeader>
                <template #title>
                        {{ $t('group_setting_title') }}{{!conversation.is_single_chat?'('+groupLength+')':''}}
                </template>
            </mrHeader>
            <div class="group_setting_container">
                <template  v-if="isService">
                    <div class="group_name clearfix" >
                        <div class="flex_item">
                            <span class="subject_title">{{(conversation.service_description&&conversation.service_description.title&&conversation.service_description.title[lan])||conversation.subject}}</span>
                        </div>
                        <div class="item">
                            <pre class="announce">{{(conversation.service_description&&conversation.service_description.content&&conversation.service_description.content[lan])||$t('no_service_tip')}}</pre>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="group_menu">
                        <ul>
                            <li @click="openGroupCollectionPage">
                                <i class="iconfont icon-foldercollect">
                                </i>
                                <p>{{ $t('group_favorite_text') }}</p>
                            </li>
                            <li @click="openGroupAllFilePage">
                                <i class="iconfont icon-images">
                                </i>
                                <p>{{ $t('group_all_file') }}</p>
                            </li>
                        </ul>
                    </div>
                    <div v-if="iworksList.length>0" class="iworks_list">
                        <div v-for="(iworks,index) of iworksList" class="iworks_item" :key="index">
                            <div class="content" @click="showProtocol(iworks)">
                                <div class="iworks_name">{{getIworksProtocolInfo(iworks, "name")}}</div>
                                <!-- <div>{{getIworksProtocolInfo(iworks, "Description")}}</div> -->
                                <div>
                                    <span class="author">{{getIworksProtocolInfo(iworks, "Author")}}</span>
                                    <span class="author">{{getIworksProtocolInfo(iworks, "Version")}}</span>
                                    <span>{{getIworksProtocolInfo(iworks, "CreateDate")}}</span>
                                </div>
                            </div>
                        </div>
                        <p v-if="moreIworksList" @click.stop="openAllIworks">{{ $t('more_iworks_text') }}&gt;</p>
                    </div>
                    <!-- 群成员头像和昵称信息 + 邀请成员进群按钮 -->
                    <div class="group_members clearfix">
                        <div class="clearfix">
                            <div v-for="member of attendeeArray" class="member_item fl" :key="member.userid" @click.stop="openVisitingCard(member,3)">
                                <mr-avatar :url="getLocalAvatar(member)" :origin_url="member.avatar" :radius="2.4" :showOnlineState="true" :key="member.avatar" :onlineState="member.state"></mr-avatar>
                                <p>{{ remarkMap[member.userid] || (member.alias_name ? member.alias_name : member.nickname) }}</p>
                                <span v-if="isGroupChat&&member.userid==conversation.creator_id">{{ $t('creator_tag_text') }}</span>
                                <span v-if="isGroupChat&&member.role==systemConfig.groupRole.manager">{{ $t('manager') }}</span>
                                <span v-if="member.attendeeState==2||member.attendeeState==3">{{ $t('temp_attendee_text') }}</span>
                            </div>
                            <div class="member_item fl icon_item">
                                <i class="icon iconfont icon-plus" @click.stop="openAddAttendee"></i>
                            </div>
                            <div class="member_item fl icon_item" v-if="hasDeleteAttendeePermission && isGroupChat">
                                <i class="icon iconfont icon-minus1" @click.stop="openDeleteAttendee"></i>
                            </div>
                        </div>
                        <p v-if="isMoreMember" @click.stop="openAllAttendee">{{ $t('more_attendee_text') }}&gt;</p>
                    </div>
                    <div v-if="deviceArray.length>0" class="device_list clearfix">
                        <div class="clearfix">
                            <div class="member_item fl" v-for="device of deviceArray" :key="'device'+device.id">
                                <i class="icon iconfont icon-bijiben"></i>
                                <p>{{ device.device_name || device.device_id }}</p>
                                <van-icon name="close" @click="confirmUnBindDevice(device)" v-if="device.device_id&&(device.device_id === deviceInfo.device_id)"/>
                            </div>
                        </div>
                        <p v-if="isMoreDevice" @click.stop="gotoDeviceList">{{ $t('more_device_title') }}&gt;</p>
                    </div>
                    <div class="group_public clearfix" @click="gotoHistoryPage">
                        <span class="fl">{{ $t('chat_history') }}</span>
                    </div>
                    <div class="group_name clearfix" v-if="isGroupChat" >
                        <div class="flex_item" @click.stop="openModifySubject">
                            <span class="subject_title">{{ $t('group_chat_name') }}</span>
                            <span class="longwrap subject">{{conversation.subject}}</span>
                        </div>
                        <div class="flex_item" @click.stop="openModifyGroupNickName">
                            <span class="subject_title">{{ $t('group_nick_name') }}</span>
                            <span class="longwrap subject">{{myGroupAliasName}}</span>
                        </div>
                        <div class="item" @click.stop="openGroupQRCodeCard">
                            <span class="subject_title">{{ $t('group_qrcode_card_text') }}</span>
                        </div>
                        <div class="item" @click.stop="openModifyAnnounce">
                            <p class="announce_title">{{ $t('group_setting_announce') }}</p>
                            <pre class="announce">{{(conversation.announcement&&conversation.announcement.content)||$t('no_announcement_tip')}}</pre>
                        </div>
                    </div>
                    <div class="group_public clearfix">
                        <span class="fl">{{ $t('mute_notifications') }}</span>
                        <div class="fr needsclick">
                            <van-switch v-model="isMute"  active-color="#00c59d" inactive-color="#D9D9D9" @change="toggleMute" />
                        </div>
                    </div>
                    <div class="group_public clearfix" @click="openReserved" v-if="hasLiveRegionPermission&&conversation.is_single_chat!=1">
                        <span class="fl">{{ $t('reserved_conference') }}</span>
                    </div>
                    <div class="group_public clearfix" v-if="hasGroupPublicSettingPermission">
                        <span class="fl">{{ $t('group_setting_is_public') }}</span>
                        <div class="fr">
                            <group-public-type class="public_mode_seleter"
                                :seletedId="isPublicOptionSelected"
                                :callback="changeIsPublicType">
                            </group-public-type>
                        </div>
                    </div>
                    <div class="group_public clearfix" v-if="hasLiveRegionPermission&&(hasGroupRecordSettingPermission || !isGroupChat)">
                        <span class="fl">{{ $t('group_setting_is_live_record') }}</span>
                        <div class="fr needsclick">
                            <van-switch v-model="isLiveRecord"  active-color="#00c59d" inactive-color="#D9D9D9" @change="toggleLiveRecord" :disabled="!!conferenceState"/>
                        </div>
                    </div>
                    <div class="group_public clearfix">
                        <span class="fl">{{ $t('group_setting_view_mode') }}</span>
                        <div class="fr">
                            <mr-selecter class="page_mode_seleter"
                                :options="pageOptions"
                                :seletedId="optionSelected"
                                :callback="changePageType">
                            </mr-selecter>
                        </div>
                    </div>
                    <div class="btns_container">
                        <button v-if="isShowWechat" class="primary_bg operat_btn" @click.stop="inviteWechatUser">{{ $t('invite_wechat_user') }}</button>
                        <button @click="openBIDataShow" class="primary_bg operat_btn" v-if="EnableQc_statistics&&isGroupChat">{{ $t('bi_data_display') }}</button>
                        <button class="primary_bg operat_btn" v-if="hasGroupManagePermission && isGroupChat" @click="openGroupManage">{{ $t('group_manage') }}</button>
                        <button class="primary_bg operat_btn" v-if='isGroupChat&&hasExitGroupPermission' @click="ExitGroup">{{ $t('exit_group') }}</button>
                    </div>
                </template>
            </div>
            <keep-alive :exclude="/group_collection/">
                <router-view></router-view>
            </keep-alive>

        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import iworksTool from '../lib/iworksTool'
import send_message from '../lib/send_message.js'
import share_to_wechat from '../lib/share_to_wechat.js'
import mrSelecter from '../components/mrSelecter'
import groupPublicType from '../MRComponents/groupPublicType'
import Tool from '@/common/tool.js'
import {openVisitingCard,getLocalAvatar} from '../lib/common_base'
import { Toast, Icon, Dialog, Loading, Switch } from 'vant';
import { getLanguage } from "@/common/i18n";
export default {
    mixins: [base,send_message,iworksTool,share_to_wechat],
    name: 'groupSettingPage',
    permission: true,
    components: {
        mrSelecter,
        VanIcon:Icon,
        [Dialog.Component.name]: Dialog.Component,
        groupPublicType,
        VanLoading: Loading,
        VanSwitch: Switch
    },
    data(){
        return {
            getLocalAvatar,
            cid:this.$route.params.cid,
            isMoreMember:false,
            isMoreDevice:false,
            isPublic:false,
            isPublicOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                }
            ],
            isNeedToApprove:0,
            isNeedToApproveOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                }
            ],
            isLiveRecord:false,
            pageOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                }
            ],
            voiceCtrlOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                }
            ],
            muteCtrlOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                },
                {
                    id:2,
                    value:''
                },
            ],
            optionSelected:0,
            settingLoading:false,
            isPublicOptionSelected:0,
            isNeedToApproveOptionSelected:0,
            voiceCtrlOptionSelected:0,
            muteCtrlOptionSelected:0,
            groupLength:0,
            lan:'CN',
            isMute:false,
            moreIworksList:false,
        }
    },
    beforeDestroy(){
        this.$root.eventBus.$emit('unlockContentEditable')
    },
    mounted(){

    },
    activated(){
        this.settingLoading=false;
        this.cid=this.$route.params.cid
        this.isPublic=this.systemConfig.groupPublicState.Private == this.conversation.is_public ? 0 : 1;
        this.isPublicOptions[0].value = this.$t('private_group_text');
        this.isPublicOptions[1].value = this.$t('public_group_text');
        this.isPublicOptionSelected = this.isPublic;
        this.isMute=this.conversation.preferences.is_mute==1?true:false;
        this.isNeedToApprove = this.systemConfig.groupPublicState.SemiPublic == this.conversation.is_public ? 1 : 0;
        this.isNeedToApproveOptions[0].value = this.$t('cancel_button_text');
        this.isNeedToApproveOptions[1].value = this.$t('confirm_button_text');
        this.isNeedToApproveOptionSelected = this.isNeedToApprove;

        this.isLiveRecord = this.conversation.record_mode==1?true:false;
        this.pageOptions[0].value=this.$t('normal_view_mode')
        this.pageOptions[1].value=this.$t('exam_view_mode')
        this.voiceCtrlOptions[0].value=this.$t('freedom_voice_ctrl_mode')
        this.voiceCtrlOptions[1].value=this.$t('authorize_speak_text')

        this.muteCtrlOptions[0].value=this.$t('no_mute')
        this.muteCtrlOptions[1].value=this.$t('all_mute')
        this.muteCtrlOptions[2].value=this.$t('more_than_six_mute')
        this.optionSelected=this.conversation.view_mode||0

        this.voiceCtrlOptionSelected=this.conversation.voice_ctrl_mode||0
        this.muteCtrlOptionSelected=this.conversation.mute_ctrl_mode||0
        this.lan=getLanguage()||'CN'
    },
    computed:{
        globalParams(){
            return this.$store.state.globalParams;
        },
        conversation(){
            return this.conversationList[this.cid]||{galleryObj:{},iworksList:{}}
        },
        isGroupChat(){
            return this.conversation.is_single_chat==0;
        },
        isUnApplyTmpGroupMem(){
            var conversation = window.vm.$store.state.conversationList[this.cid];
            if(conversation){
                if(this.systemConfig.groupPublicState.SemiPublic == conversation.is_public){//半公开群
                    if(2 == conversation.attendeeList["attendee_" + this.user.uid].attendeeState){ //未申请的临时成员
                        return true;
                    }
                }
            }
            return false;
        },
        isService(){
            return this.conversation.service_type!=0
        },
        isApplyingTmpGroupMem(){
            var conversation = window.vm.$store.state.conversationList[this.cid];
            if(conversation){
                if(this.systemConfig.groupPublicState.SemiPublic == conversation.is_public){//半公开群
                    if(3 == conversation.attendeeList["attendee_" + this.user.uid].attendeeState){ //正在申请的临时成员
                        return true;
                    }
                }
            }
            return false;
        },
        attendeeArray(){
            let list=this.parseObjToArr(this.conversation.attendeeList);
            let filterList = []; //后端把所有用户都返回回来，前端只显示未退群用户
            for(let i=0; i<list.length; i++){
                if(list[i].attendeeState != 0){
                    filterList.push(list[i]);
                }
            }
            this.groupLength=filterList.length;
            let maxFileListSize = 3;
            if(this.hasGroupManagePermission){
                maxFileListSize = 2;
            }
            if (filterList.length>maxFileListSize) {
                this.isMoreMember=true;
                filterList.splice(maxFileListSize)
            }else{
                this.isMoreMember=false;
            }
            return filterList;
        },
        deviceArray(){
            let list=this.conversation.device_list;
            let arr=[];
            if (list.length>4) {
                this.isMoreDevice=true;
            }else{
                this.isMoreDevice=false;
            }
            arr=list.slice(0,4);
            return arr;
        },
        iworksList(){
            let list=this.conversation.iworksList.list||[]
            if (list.length>4) {
                this.moreIworksList=true;
                list=this.conversation.iworksList.list.slice(0,4);
            }else{
                this.moreIworksList=false;
            }
            return list
        },
        EnableQc_statistics(){
            return this.$checkPermission({regionPermissionKey: 'qcStatistics'})
                &&this.$store.state.systemConfig.serverInfo.qc_statistics
                &&this.$store.state.systemConfig.serverInfo.qc_statistics.enable
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        conferenceState(){
            return this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].conferenceState
        },
        deviceInfo(){
            return this.$store.state.device
        },
        myGroupAliasName(){
            return this.conversation.attendeeList[`attendee_${this.user.uid}`].alias_name
        },
        hasDeleteAttendeePermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.remove'},{
                conversationId:this.cid,
            })
        },
        hasGroupManagePermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.manage'},{
                conversationId:this.cid,
            })
        },
        hasExitGroupPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.exit'},{
                conversationId:this.cid,
            })
        },
        hasGroupPublicSettingPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.public_setting'},{
                conversationId:this.cid,
            })
        },
        hasGroupRecordSettingPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conference.record_setting'},{
                conversationId:this.cid,
            })
        },
        hasLiveRegionPermission(){
            return this.$checkPermission({regionPermissionKey: 'live'})
        }
    },
    methods:{
        changeIsPublicType(item){ //togglePublic
            let that = this;
            let new_selected = parseInt(item.id);
            let old_select = that.isPublicOptionSelected;

            if (new_selected == old_select) {
                return;
            }

            if(1==new_selected){
                if(0==this.isNeedToApproveOptionSelected){
                    new_selected=this.systemConfig.groupPublicState.Public;
                }else{
                    new_selected=this.systemConfig.groupPublicState.SemiPublic;
                }
            }else{
                new_selected=this.systemConfig.groupPublicState.Private;
            }

            if(1==old_select){
                if(0==this.isNeedToApproveOptionSelected){
                    old_select=this.systemConfig.groupPublicState.Public;
                }else{
                    old_select=this.systemConfig.groupPublicState.SemiPublic;
                }
            }else{
                old_select=this.systemConfig.groupPublicState.Private;
            }

            let message = "";

            if(old_select == 2){ //半公开群切换到其他类型的群
                if(new_selected == 1){ //半公开群切换到公开群
                    message = this.$t('toggle_conversation_to') + this.$t('public_group_text') + " , 所有的临时用户将自动变为正式成员";
                }else if(new_selected == 0){ //半公开群切换到私有群
                    message = this.$t('toggle_conversation_to') + this.$t('private_group_text') + " , 所有的临时用户将自动变为正式成员";
                }
            }else{
                if(new_selected == 1){
                    message = this.$t('toggle_to') + " " + this.$t('public_group_text');
                }else if(new_selected == 2){
                    message = this.$t('toggle_to') + " " + this.$t('semi_public_group_text');
                }else if(new_selected == 0){
                    message = this.$t('toggle_to') + " " + this.$t('private_group_text');
                }
            }
            Tool.openMobileDialog({
                message: message,
                confirm:()=>{
                    var data={
                        gid:that.cid,
                        is_public:new_selected
                    }
                    if(old_select == 2 && new_selected != 2){
                        data.tempToFormal = 1;
                    }
                    that.conversation.socket.emit('edit_public',data,function(is_succ,result){
                        if(1 == data.tempToFormal){
                            that.$store.commit('notifications/deleteGroupApplyByCid', {cid: that.cid});//删除群申请的系统通知
                            that.$store.commit('conversationList/updateAllAttendeeTempToFormal', {cid: that.cid});//修改群中临时成员为正式成员
                        }
                        //修改成功
                        that.$store.commit('conversationList/updateIsPublic',{
                            cid:that.cid,
                            is_public:new_selected
                        })

                        that.isPublic = that.systemConfig.groupPublicState.Private == new_selected ? 0 : 1;
                        that.isPublicOptionSelected = that.isPublic;

                        that.isNeedToApprove = that.systemConfig.groupPublicState.SemiPublic == new_selected ? 1 : 0;
                        that.isNeedToApproveOptionSelected = that.isNeedToApprove;

                        console.log('new isPublic value',that.isPublic)
                    })
                },
            })
        },
        changeIsNeedToApprove(item){
            let that = this;
            let new_selected = parseInt(item.id);
            let old_select = that.isNeedToApproveOptionSelected;

            if (new_selected == old_select) {
                return;
            }

            if(1==this.isPublicOptionSelected){
                if(0==new_selected){
                    new_selected=this.systemConfig.groupPublicState.Public;
                }else{
                    new_selected=this.systemConfig.groupPublicState.SemiPublic;
                }
            }else{
                new_selected=this.systemConfig.groupPublicState.Private;
            }

            if(1==this.isPublicOptionSelected){
                if(0==old_select){
                    old_select=this.systemConfig.groupPublicState.Public;
                }else{
                    old_select=this.systemConfig.groupPublicState.SemiPublic;
                }
            }else{
                old_select=this.systemConfig.groupPublicState.Private;
            }

            let message = "";

            if(old_select == 2){ //半公开群切换到其他类型的群
                message = this.$t('toggle_conversation_to') + this.$t('join_group_without_approve') + " , 所有的临时用户将自动变为正式成员";
            }else{
                message = this.$t('toggle_to') + " " + this.$t('join_group_with_approve');
            }
            Tool.openMobileDialog(
                {
                    message,
                    showRejectButton:true,
                    confirm:()=>{
                        var data={
                            gid:that.cid,
                            is_public:new_selected
                        }
                        if(old_select == 2 && new_selected != 2){
                            data.tempToFormal = 1;
                        }
                        that.conversation.socket.emit('edit_public',data,function(is_succ,result){
                            if(1 == data.tempToFormal){
                                that.$store.commit('notifications/deleteGroupApplyByCid', {cid: that.cid});//删除群申请的系统通知
                                that.$store.commit('conversationList/updateAllAttendeeTempToFormal', {cid: that.cid});//修改群中临时成员为正式成员
                            }
                            //修改成功
                            that.$store.commit('conversationList/updateIsPublic',{
                                cid:that.cid,
                                is_public:new_selected
                            })

                            that.isPublic = that.systemConfig.groupPublicState.Private == new_selected ? 0 : 1;
                            that.isPublicOptionSelected = that.isPublic;

                            that.isNeedToApprove = that.systemConfig.groupPublicState.SemiPublic == new_selected ? 1 : 0;
                            that.isNeedToApproveOptionSelected = that.isNeedToApprove;

                            console.log('new isPublic value',that.isPublic)
                        })
                    }
                }
            )
        },
        toggleLiveRecord(){
            let value=this.isLiveRecord;
            console.log('isLiveRecord',value)
            let message=value?this.$t('toggle_live_record_tip'):this.$t('toggle_unlive_record_tip')
            let that=this;
            Tool.openMobileDialog(
                {
                    message,
                    showRejectButton:true,
                    confirm:()=>{
                        that.isLiveRecord=value;
                        console.log('value2',that.isLiveRecord)
                        var data={
                            gid:that.cid,
                            record_mode:that.isLiveRecord?1:0
                        }
                        that.conversation.socket.emit('edit_record_mode',data,function(is_succ,data){
                            if(is_succ){
                            //修改成功
                                that.$store.commit('conversationList/updateIsLiveRecord',{
                                    cid:that.cid,
                                    record_mode:that.isLiveRecord
                                });
                            }else{//修改失败
                                that.isLiveRecord = !that.isLiveRecord;
                            }

                        })
                    },
                    reject:()=>{
                        that.isLiveRecord=!value
                    },
                    cancel:()=>{
                        that.isLiveRecord=!value
                    }
                }
            )
        },
        openAllAttendee(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/all_attendee`)
        },
        openAddAttendee(){
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                Toast(this.$t('app_no_speak_permission'));
                return;
            }
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/add_attendee`)
        },
        openDeleteAttendee(){
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                Toast(this.$t('app_no_speak_permission'));
                return;
            }
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/delete_attendee`)
        },
        openModifySubject(){
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                Toast(this.$t('app_no_speak_permission'));
                return;
            }
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/modify_subject`)
        },
        openModifyGroupNickName(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/modify_group_nickname`)
        },
        openModifyAnnounce(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/modify_announce`)
        },
        openGroupQRCodeCard(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/group_qrcode_card`)
        },
        changePageType(item){
            var that=this;
            this.optionSelected=parseInt(item.id);
            if (this.user.uid==this.conversation.creator_id) {
                //会话创建者可以设置默认视图
                that.conversation.socket.emit('edit_view_mode',{
                    view_mode:item.id
                },function(is_succ,data){
                    //修改成功
                    if (is_succ) {
                        Toast(that.$t('update_success_text'));
                        that.$store.commit('conversationList/updateViewMode',{
                            cid:that.cid,
                            value:item.id
                        });
                        that.$root.eventBus.$emit('shouldTogglePageType',item.id)
                    }
                })
            }else{
                //会话参与者可以设置默认视图本地化设置
                let settingsObj=JSON.parse(window.localStorage.getItem(`user_${this.user.uid}_viewmode`)||"{}");
                settingsObj[`group_${this.cid}`]=item.id;
                window.localStorage.setItem(`user_${this.user.uid}_viewmode`,JSON.stringify(settingsObj));
                that.$store.commit('conversationList/updateViewMode',{
                    cid:that.cid,
                    value:item.id
                });
                Toast(that.$t('update_success_text'));
                that.$root.eventBus.$emit('shouldTogglePageType',item.id)
            }
        },
        changeVoiceCtrlType(item){
            var that=this;
            this.voiceCtrlOptionSelected=parseInt(item.id);
            //修改了语音设置
            var data={
                gid:parseInt(that.cid),
                voice_ctrl_mode:this.voiceCtrlOptionSelected
            }
            that.conversation.socket.emit('edit_voice_ctrl_mode',data,function(is_succ,data){
                if(is_succ){
                    //修改成功
                    that.$store.commit('conversationList/updataVoiceCtrlMode', {
                        cid:that.cid,
                        voice_ctrl_mode:item.id
                    });
                }else{//修改失败
                    Toast(that.$t('update_failed_text'));
                    that.voiceCtrlOptionSelected=that.conversation.voice_ctrl_mode || 0;
                }
            })
        },
        changeMuteCtrlType(item){ // 修改静音模式
            var that=this;
            this.muteCtrlOptionSelected=parseInt(item.id);
            //修改了语音设置
            var data={
                gid:parseInt(that.cid),
                mute_ctrl_mode:this.muteCtrlOptionSelected
            }
            that.conversation.socket.emit('edit_mute_ctrl_mode',data,function(is_succ,data){
                if(is_succ){
                    //修改成功
                    that.$store.commit('conversationList/updataMuteCtrlMode', {
                        cid:that.cid,
                        mute_ctrl_mode:item.id
                    });
                }else{//修改失败
                    Toast(that.$t('update_failed_text'));
                    that.muteCtrlOptionSelected=that.conversation.mute_ctrl_mode || 0;
                }
            })
        },
        inviteWechatUser(){
            if (this.systemConfig.clientType==5) {
                Toast(this.$t('use_app_tip'))
                return
            }
            var that = this;
            const subject = encodeURIComponent(this.conversation.subject);
            window.main_screen.conversation_list[this.cid].generateInviteCode({
                autoMakeFriend:false,
            },(res)=>{
                if(res.error_code === 0){
                    const inviteCode = res.data.inviteCode;
                    let ajaxServer=this.systemConfig.server_type.protocol+this.systemConfig.server_type.host+this.systemConfig.server_type.port;
                    let fullUrl = Tool.transferLocationToCe(`${ajaxServer}/activity/activity.html#/qr_install_app?act=add_group&inviteCode=${inviteCode}&from=weChat&subject=${subject}`) // ps:之后再加参数，必须要在from之前加，因为APP那边在尾部会拼接其他参数
                    let thumb=`${ajaxServer}/mobile/static/resource/images/new_logo.png`
                    var shareContent = {
                        img_url: '1',
                        video_url: fullUrl,
                        title: that.user.nickname +' '+ that.$t('invite_you_join_group')+that.conversation.subject,
                        content: that.$t('mindray_final_mission'),
                        thumb: thumb
                    };
                    that.shareLinkToWeChat(shareContent);
                }
            })
        },
        ExitGroup(){
            var that=this;
            let message = "";
            message = this.$t('user_exit_group_tip');//"所有数据将被清空，操作不可逆，是否继续？"
            Tool.openMobileDialog(
                {
                    message,
                    showRejectButton:true,
                    confirm:()=>{
                        let data={};
                        let message = "";
                        message = "request_delete_attendees";
                        data.attendees = [{uid:that.user.uid}];
                        data.isActiveQuit = 1; //主动退群
                        that.conversation.socket.emit(message, data, function(is_succ, info){
                            that.settingLoading=false;
                            if(!is_succ){
                                console.log(info);
                                Toast(that.$t('user_exit_group_fail'));
                            }
                        });
                        this.settingLoading=true;
                    }
                }
            )
        },
        openReserved(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/reserved_conference`);
        },
        toggleMute(){
            let that=this;
            let is_mute=this.isMute?1:0;
            this.conversation.socket.emit('set_attendee_preferences',{
                is_mute:is_mute
            },function(result){
                if(result.error_code){
                    Toast(that.$t('update_failed_text'))
                    that.isMute=!that.isMute;
                }else{
                    that.$store.commit('conversationList/updateMuteToConversation',{
                        is_mute:is_mute,
                        cid:that.cid
                    })
                    that.$store.commit('chatList/updateMuteToChatList',{
                        is_mute:is_mute,
                        cid:that.cid
                    })
                }
            })
        },
        openAllIworks(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/all_iworks`)
        },
        showProtocol(item){
            this.$router.push(this.$route.fullPath+'/protocol_tree/'+item.protocol_guid+'/0');
        },
        gotoHistoryPage(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/chat_history_search_list`)
        },
        openVisitingCard(messages,type){
            openVisitingCard(messages,type)
        },
        openBIDataShow(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/bi_data?type=group&cid=${this.cid}`)
        },
        openGroupCollectionPage(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/group_collection`)
        },
        openGroupAllFilePage(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/all_file`)
        },
        gotoDeviceList(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/device_list`)
        },
        getDeviceList(){
            window.main_screen.getDeviceList({cid:this.cid},(res)=>{
                if (res.error_code==0) {
                    this.$store.commit('conversationList/updateDeviceList',{
                        cid:this.cid,
                        list:res.data.list
                    })
                }
            })
        },
        confirmUnBindDevice(device){
            Tool.openMobileDialog(
                {
                    message: this.$t('whether_remove_equipment_from_group'),
                    confirm:()=>{
                        window.main_screen.deviceUnBinding({
                            group_id:this.cid,
                            device_id:device.device_id,
                            device_type:device.device_type
                        },(res)=>{
                            if (res.error_code==0) {
                                this.getDeviceList()
                            }
                        })
                    },
                }
            )
        },
        openGroupManage() {
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/group_manage`);
        },
    }
}

</script>
<style lang="scss" scoped>
    .group_setting_page{

        .van_loading_spinner{
            display: flex;
            justify-content: center;
            align-items: center;

            .van-loading__spinner{
                width: 2.8rem;
                height: 2.8rem;
            }
        }

        .group_setting_container{
            overflow:auto;
            height:calc(100% - 2.2rem);
            .iworks_list{
                margin: .5rem 0;
                border-top: 1px solid #ddd;
                background-color: #fff;
                display:flex;
                flex-wrap: wrap;
                padding: 0.4rem 0 0 0.4rem;
                .iworks_item{
                    width:50%;
                    font-size:0.6rem;
                    padding: 0 0.4rem 0.4rem 0;
                    box-sizing: border-box;
                    .content{
                        position:relative;
                        padding: 0.2rem 0.4rem;
                        border-radius: .4rem;
                        word-break: break-all;
                        padding: 0.4rem 0.4rem;
                        background-color: #f2f6f9;
                        border-radius: 0.2rem;
                        box-shadow: 0.1rem 0.1rem 0.2rem rgba(140, 152, 155, 0.7);
                        position: relative;
                        overflow: hidden;
                        .iworks_name{
                            font-size:0.7rem;
                        }
                        .author{
                            margin-right:0.3rem;
                        }
                    }
                }
                p{
                    color: #999;
                    text-align: center;
                    font-size: .8rem;
                    padding: .2rem 0 .4rem;
                    width: 100%;
                }
            }
            // .group_files,.group_all_files{
            //     background-color:#fff;
            //     .file_item{
            //         float:left;
            //         width:25%;
            //         border:1px solid #fff;
            //         box-sizing:border-box;
            //         position:relative;
            //         .icon-comment1{
            //             position: absolute;
            //             top: 0.2rem;
            //             left: 0.3rem;
            //             z-index: 2;
            //             color: #f00;
            //             font-size: 1rem;
            //             line-height:1rem;
            //             span{
            //                 color:#fff;
            //                 position:absolute;
            //                 font-size: 0.6rem;
            //                 top: 0.1rem;
            //                 left: 50%;
            //                 transform: translate(-50%,0);
            //                 line-height: 0.6rem;
            //                 white-space: nowrap;
            //             }
            //         }
            //         .unread_tip{
            //             position: absolute;
            //             right: 0.3rem;
            //             top: 0.3rem;
            //             border-radius: 50%;
            //             background-color: #f00;
            //             width:0.4rem;
            //             height:0.4rem;
            //             z-index:2;
            //         }
            //         .file_container{
            //             position:relative;
            //             padding-top:100%;
            //             height:0;
            //             background-color:#333;
            //             .file_image{
            //                 max-width:100%;
            //                 max-height:100%;
            //                 position: absolute;
            //                 top: 50%;
            //                 left: 50%;
            //                 transform: translate(-50%,-50%);
            //             }

            //             .icon-videofill{
            //                 font-size: 1rem;
            //                 color: #00c59d;
            //                 position: absolute;
            //                 bottom: .2rem;
            //                 left: .3rem;
            //             }
            //             .review_time{
            //                 position: absolute;
            //                 top: 50%;
            //                 left: 50%;
            //                 transform: translate(-50%,-50%) scale(0.4);
            //                 font-size: 1rem;
            //                 color: #fff;
            //                 text-align: center;
            //                 white-space: nowrap;
            //             }
            //             .review_text{
            //                 color: yellow;
            //                 position: absolute;
            //                 top: 5%;
            //                 left: 50%;
            //                 transform: translateX(-50%) scale(0.4);
            //                 font-size: 1.2rem;
            //                 text-align: center;
            //                 white-space: nowrap;
            //                 font-weight: 600;

            //             }
            //         }
            //     }
            //     &>p{
            //         color:#999;
            //         text-align:center;
            //         font-size:.8rem;
            //         padding:.2rem 0 .4rem;
            //     }
            // }
            .group_menu{
                overflow: hidden;
                ul {
                    padding: 0;
                    margin: 0;
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    li {
                        list-style: none;
                        text-align: center;
                        font-size: 20px;
                        text-align: center;
                        color: #00c59d;
                        line-height: 1;
                        padding: .6rem 0;
                        width: 25%;
                        // margin-right: 5%;
                        // margin-bottom: 5%;
                        // box-sizing: border-box;
                        &:nth-of-type(3n) {
                            margin-right: 0;
                        }
                        &:nth-of-type(n+7) {
                            margin-bottom: 0;

                        }
                        i{
                            font-size: 1.2rem;
                            position:relative;
                            span{
                                position: absolute;
                                top: -0.5rem;
                                right: -0.5rem;
                                background: #f00;
                                border-radius: 50%;
                                font-size: 0.6rem;
                                min-width: 0.7rem;
                                min-height: 0.7rem;
                                display: inline-block;
                                line-height: 0.7rem;
                                text-align: center;
                                padding: 0.1rem;
                                color: #fff;
                            }
                        }
                        p{
                            font-size: 0.7rem;
                            line-height: 1.6;
                        }
                    }
                }
            }
            .group_members{
                padding:.5rem 0.5rem;
                border-top:1px solid #ddd;
                background-color:#fff;
                .member_item{
                    width:25%;
                    position:relative;
                    text-align:center;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    & > p{
                        text-align:center;
                        font-size:.7rem;
                        margin:.3rem 0;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        color: #666;
                        width: 100%;
                    }
                    & > span{
                        position: absolute;
                        bottom: 1rem;
                        font-size: .6rem;
                        left: 0.4rem;
                        background: #00c59d;
                        color: #fff;
                        padding: .2rem .1rem;
                        border-radius: .2rem;
                        max-width: 3.2rem;
                        word-wrap: break-word;
                        line-height: 1;
                    }
                    .icon-plus{
                        font-size:2.4rem;
                        line-height:1;
                        color:#aaa;
                    }
                    .icon-minus1{
                        font-size:2.4rem;
                        line-height:1;
                        color:#aaa;
                    }
                }
                .icon_item{
                    height:4rem
                }
                &>p{
                    color:#999;
                    text-align:center;
                    font-size:.8rem;
                    padding:.2rem 0;
                }
            }
            .device_list{
                padding:.5rem 0.5rem;
                border-top:1px solid #ddd;
                background-color:#fff;
                .member_item{
                    width:25%;
                    position:relative;
                    text-align:center;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    & > p{
                        text-align:center;
                        font-size:.6rem;
                        margin:.3rem 0;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        color: #666;
                        width: 100%;
                    }
                    .icon-bijiben{
                        background: #56c7fd;
                        color: #fff;
                        width: 2.4rem;
                        height: 2.4rem;
                        line-height: 2.4rem;
                        border-radius: 50%;
                        font-size: 1.2rem;
                    }
                    .van-icon-close{
                        position: absolute;
                        top: 0;
                        right: 0.5rem;
                        color: #626060;
                    }
                }
                &>p{
                    color:#999;
                    text-align:center;
                    font-size:.8rem;
                    padding:.2rem 0;
                }
            }
            .group_name{
                padding:0.4rem .8rem;
                // margin:.5rem 0;
                border-top:1px solid #ddd;
                background-color:#fff;
                font-size:.8rem;
                .flex_item{
                    display: flex;
                }
                .flex_item,.item{
                    padding: .5rem .5rem .5rem 0;
                    border-bottom:1px solid #ddd;
                    &:last-child{
                        border:none;
                    }

                    white-space: pre-wrap;
                    text-align: justify;
                    .subject_title{
                        white-space: nowrap;
                        margin-right: 0.8rem;
                        white-space: pre-wrap;
                        text-align: justify;
                    }
                    .subject{
                        color: #666;
                    }
                    .announce_title{
                        margin-bottom: .2rem;
                    }
                    .announce{
                        color: #666;
                        font-size: .7rem;
                        white-space: pre-wrap;
                    }
                }
            }
            .group_public{
                padding:.5rem 0.8rem;
                // margin:.5rem 0;
                border-top:1px solid #ddd;
                background-color:#fff;
                display: flex;
                .fl{
                    flex:1;
                }
                .fr{
                    font-size: .8rem;
                }
                span{
                    font-size:.8rem;
                }
                .page_mode_seleter{
                    line-height: 1.1rem;
                    height: 1.1rem;
                }
            }
            .btns_container{
                margin:0 0.5rem;
                .operat_btn{
                    display: block;
                    width: 100%;
                    border: none;
                    font-size: 1rem;
                    line-height: 2rem;
                    margin: 1rem 0rem .6rem;
                    border-radius: .2rem;
                }
            }
        }
    }
</style>
