<template>
	<div class="tooltips-menu" v-if="menuDatas.length>0">
        <div :class="['first-wrap', { 'last-wrap': isLastWrap('first') }]" v-if="firstArr.length>0">
            <template v-for="(item,index) in transfer" >
                <van-button
                    class="tooltips-menu-item"
                    type="default"
                    :key="index"
                    @click="clickItem(item)"
                    v-if="firstArr.includes(item.key)"
                    :style="getButtonStyle($t(item.name), isLastWrap('first'))">
                    <div class="button-text-wrapper">{{ $t(item.name) }}</div>
                </van-button>
            </template>
        </div>
        <div :class="['second-wrap', { 'last-wrap': isLastWrap('second') }]" v-if="secondArr.length>0">
            <template v-for="(item,index) in transfer">
                <van-button
                    class="tooltips-menu-item"
                    type="default"
                    :key="index"
                    @click="clickItem(item)"
                    v-if="secondArr.includes(item.key)"
                    :style="getButtonStyle($t(item.name), isLastWrap('second'))">
                    <div class="button-text-wrapper">{{ $t(item.name) }}</div>
                </van-button>
            </template>
        </div>
        <div :class="['third-wrap', { 'last-wrap': isLastWrap('third') }]" v-if="thirdArr.length>0">
            <template v-for="(item,index) in transfer">
                <van-button
                    class="tooltips-menu-item"
                    type="default"
                    :key="index"
                    @click="clickItem(item)"
                    v-if="thirdArr.includes(item.key)"
                    :style="getButtonStyle($t(item.name), isLastWrap('third'))">
                    <div class="button-text-wrapper">{{ $t(item.name) }}</div>
                </van-button>
            </template>
        </div>
	</div>
</template>
<script>
import { Button } from 'vant'
import base from '../lib/base'
import {getLanguage} from '@/common/i18n'
import { MENU_ITEMS, MENU_ITEM_CONFIG } from '../lib/constants.js'
export default {
    components:{
        VanButton: Button,
    },
    mixins: [base],
    data(){
        return {
            transfer: MENU_ITEM_CONFIG,
            firstArr:[],
            secondArr:[],
            thirdArr:[],
        }
    },
    props:{
        menuDatas:{
            type:Array,
            default:()=>{
                return []
            }
        },
    },
    created(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
        if(getLanguage() ==='CN'){
            this.menuDatas.forEach((item,index)=>{
                if(index<4){
                    this.firstArr.push(item)
                }else if(index>=4&&index<8){
                    this.secondArr.push(item)
                }else if(index>=8){
                    this.thirdArr.push(item)
                }
            })
        }else{
            this.menuDatas.forEach((item,index)=>{
                if(index<3){
                    this.firstArr.push(item)
                }else if(index>=3&&index<6){
                    this.secondArr.push(item)
                }else if(index>=6){
                    this.thirdArr.push(item)
                }
            })
        }



    },
    beforeDestroy(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
    },
    methods:{
        clickItem(item){
            // 使用配置中的事件名称，避免魔法值
            if (item.event) {
                this.$emit(item.event);
            } else {
                console.warn('Menu item has no event configured:', item);
            }
        },
        getButtonStyle(text, isLastRow = false) {
            // 根据文本长度动态计算按钮宽度，确保单词不会被拆分
            const textLength = text.length;
            const isEnglish = /^[a-zA-Z\s]+$/.test(text);

            // 更宽松的宽度设置，避免单词被拆分
            let minWidth = '4rem';
            let maxWidth = '7rem';

            if (isEnglish) {
                // 英文文本需要更多空间，特别是避免单词拆分
                if (textLength > 15) {
                    minWidth = '5rem';
                    maxWidth = '8rem';
                } else if (textLength > 10) {
                    minWidth = '4.5rem';
                    maxWidth = '7.5rem';
                } else if (textLength > 6) {
                    minWidth = '4rem';
                    maxWidth = '6.5rem';
                } else {
                    minWidth = '3.5rem';
                    maxWidth = '5.5rem';
                }
            } else {
                // 中文文本相对紧凑，但也要给足够空间
                if (textLength > 6) {
                    minWidth = '4.5rem';
                    maxWidth = '6rem';
                } else if (textLength > 4) {
                    minWidth = '4rem';
                    maxWidth = '5.5rem';
                } else {
                    minWidth = '3.5rem';
                    maxWidth = '5rem';
                }
            }

            const style = { minWidth, maxWidth };
            // if (isLastRow) {
            //     style.minWidth = '0';
            //     style.maxWidth = 'none';
            // }
            return style;
        },
        isLastWrap(which){
            if(which==='third') {
                return this.thirdArr.length>0 && this.secondArr.length===0;
            }
            if(which==='second') {
                return this.secondArr.length>0 && this.thirdArr.length===0;
            }
            if(which==='first') {
                return this.firstArr.length>0 && this.secondArr.length===0 && this.thirdArr.length===0;
            }
            return false;
        }
    }
}
</script>
<style lang="scss">
.tooltips-menu{
    background-color: #060607;
    position: relative;
    flex: 1;
    border-radius: 5px;
    // 由按钮自身控制留白，容器不额外留白
    padding: 0 0;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    max-width: min(24rem, calc(100vw - 2rem));
    width: fit-content;
    .tooltips-menu-item{
        padding: 0.4rem 0.7rem !important;
        font-size: 0.6rem;
        background-color: #060607;
        color: #fff;
        border: 0;
        text-overflow: unset;
        flex: 1;
        height: auto;
        min-height: 2rem;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        box-sizing: border-box !important;
        position: relative; // 便于绘制中部短分割线
    }

    // 强制覆盖Vant按钮的所有可能样式
    .tooltips-menu-item.van-button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        padding: 0.4rem 0.7rem !important;
        position: relative; // 便于绘制中部短分割线

        .van-button__text{
            white-space: pre-wrap !important;
            word-break: break-word !important;
            line-height: 1.2 !important;
            width: 100% !important;
            text-align: center !important;
            display: block !important;
            min-height: inherit !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .button-text-wrapper {
            width: 100% !important;
            text-align: center !important;
            white-space: normal !important;
            word-break: keep-all !important;
            overflow-wrap: break-word !important;
            line-height: 1.2 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-height: inherit !important;
            hyphens: none !important;
        }
    }
}

// 额外的样式重置，确保完全居中
.tooltips-menu .van-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;

    * {
        text-align: center !important;
    }

    .button-text-wrapper {
        width: 100% !important;
        text-align: center !important;
        white-space: normal !important;
        word-break: keep-all !important;
        overflow-wrap: break-word !important;
        line-height: 1.2 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        hyphens: none !important;
    }
}

.first-wrap{
    display: flex;
    width: 100%;
    gap: 0;
    align-items: stretch;
    // 使用短分割线替代整高边框
    .tooltips-menu-item:not(:last-child)::after{
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 1em; // 接近字体高度
        background-color: #757575;
    }
}
.second-wrap,.third-wrap{
    border-top: 1px solid #757575;
    width: 100%;
    display: flex;
    gap: 0;
    align-items: stretch;
    // 使用短分割线替代整高边框
    .tooltips-menu-item::after{
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 1em; // 接近字体高度
        background-color: #757575;
    }
}

// 仅当这是最后一行时，拉伸子按钮以等分整行宽度
.last-wrap{
    .tooltips-menu-item{
        flex: 1 1 0 !important;
    }
}
</style>
