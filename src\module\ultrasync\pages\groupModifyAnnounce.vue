<template>
	<transition name="slide">
		<div class="announce_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{ $t('group_setting_announce') }}
                </template>
            </mrHeader>
			<div class="container">
				<input type="hidden" name="" v-model="conversation.subject">
				<div v-if="announcement.sender_id" class="announce_info clearfix">
                    <mr-avatar :url="getLocalAvatar(sender)" :origin_url="sender.avatar" :showOnlineState="false" :key="sender.avatar"></mr-avatar>
					<div class="announce_info_wrap">
						<p class="name">{{sender.nickname}}</p>
						<p class="time">{{formatTime(announcement.send_ts)}}</p>
					</div>
				</div>
				<textarea :disabled="!hasGroupAnnouncementPermission" class="commont_input" @click="scrollInput" v-model="content" maxlength="300" ref="subject"></textarea>
				<button v-if="hasGroupAnnouncementPermission" class="primary_bg modify_subject_btn" @click="submit">{{ $t('save_txt') }}</button>
				<div v-else class="announce_tip">
					{{ $t('only_creator_can_edit') }}
				</div>
			</div>
		</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import {getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'GroupModifyAnnounce',
    permission: true,
    components: {},
    data(){
    	return {
            getLocalAvatar,
    		cid:this.$route.params.cid,
    		announcement:{},
    		content:''
    	}
    },
    activated(){
        this.cid=this.$route.params.cid
    },
    mounted(){
        this.$nextTick(()=>{

        })
    },
    computed:{
        conversation(){
        	let conversation=this.conversationList[this.cid]||{}
        	this.announcement=conversation.announcement||{};
            this.content=this.announcement.content||this.$t('no_announcement_tip');
            return conversation
        },
       	attendeeList(){
       		return this.conversation.attendeeList||{}
       	},
        sender(){
        	if (this.announcement.sender_id) {
        		return this.attendeeList['attendee_'+this.announcement.sender_id]
        	}else{
        		return {}
        	}
        },
        hasGroupAnnouncementPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.edit_announcement'},{
                conversationId:this.cid,
            })
        }
    },
    methods:{
    	submit(){
    		if (this.content=='') {
    			return
    		}
    		this.conversation.socket.emit('edit_announcement',{content:this.content},(is_succ,data)=>{
    			if (is_succ) {
    				data.cid=this.cid;
    				this.$store.commit('conversationList/updateAnnounce',data);
    			}
    		})
    		this.back();
    	},
    	scrollInput(){
            if (!this.hasGroupAnnouncementPermission) {
                return ;
            }
    		if (this.content==this.$t('no_announcement_tip')) {
    			this.content=''
    		}
    		setTimeout(()=>{
    			this.$refs.subject.scrollIntoViewIfNeeded(true);
    		},300)
    	}
    }
}

</script>
<style lang="scss">
	.announce_page{
		.container{
			margin:.8rem;
			.announce_info{
				display: flex;
				padding-bottom: .5rem;
    			border-bottom: 1px solid #ddd;
				.announce_info_wrap{
					padding-left: .4rem;
				    line-height: 1.4;
				    float: left;
				    .name{
				    	font-size: .8rem;
				    }
				    .time{
				    	font-size: .7rem;
				    	color:#666;
				    }
				}
			}
			.modify_tip{
				font-size:.8rem;
				color:#707070;
				margin:.1rem 0;
			}
			textarea{
				background-color:transparent;
				margin:0;
				color:#333;
				height:10rem;
				font-size:0.9rem;
				border:none;
			}
			.modify_subject_btn{
				display: block;
			    width: 100%;
			    border: none;
			    font-size: 1rem;
			    line-height: 2rem;
			    margin: 1rem 0 .6rem;
			    border-radius: .2rem;
			}
			.announce_tip{
				text-align:center;
				font-size:.8rem;
				margin-top:1rem;
				color:#333;
			}
		}
	}
</style>
