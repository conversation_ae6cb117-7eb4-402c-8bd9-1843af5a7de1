<template>
    <div>
        <CommonDialog
            :title="$t('group_add_attendee_title')"
            :show.sync="visible"
            width="40%"
            :close-on-click-modal="false"
            :modal="false"
            @closed="back"
            @submit="submit"
            :submitText="$t('confirm_txt')"
            :isSubmitting="false"
            :disabledSubmit = "!enable"
            submitBtnSize = "small"

        >
            <ContactSelectList :options="friendListOptions" v-model="groupUser"></ContactSelectList>

        <!-- <el-dialog
            class="add_attendee"
            :title="$t('group_add_attendee_title')"
            :visible="true"
            :close-on-click-modal="false"
            width="40%"
            :modal="false"
            :before-close="back"
        >
            <ContactSelectList :options="friendListOptions" v-model="groupUser"></ContactSelectList>
            <div class="clearfix">
                <el-button type="primary" size="medium" class="fr" :disabled="!enable" @click="submit">{{
                    $t('confirm_txt')
                }}</el-button>
            </div>
        </el-dialog> -->
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import { formatAttendeeNickname } from "../lib/common_base";
import ContactSelectList from "../components/contactSelectList";
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change

export default {
    mixins: [base],
    name: "AddAttendee",
    permission: true,
    components: {
        ContactSelectList,
        CommonDialog
    },
    data() {
        return {
            visible: false,
            groupUser: [],
            checkedUserList: [],
        };
    },
    computed: {
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        attendeeList() {
            return this.conversation.attendeeList || {};
        },
        enable() {
            return this.groupUser.length > 0;
        },
        friendListOptions() {
            var arr = [];
            for (let friend of this.$store.state.friendList.list) {
                let item = Object.assign({}, friend);
                if (item.alias) {
                    item.nickname = item.alias;
                }
                if (item.service_type == 0 && item.user_status != this.systemConfig.userStatus.Destroy) {
                    let option = {};
                    option.name = item.nickname;
                    option.id = item.id;
                    option.avatar = item.avatar;
                    //已在群里不可选
                    if (
                        this.attendeeList["attendee_" + item.id] &&
                        this.attendeeList["attendee_" + item.id].attendeeState != 0
                    ) {
                        option.disabled = true;
                    }

                    arr.push(option);
                }
            }

            return arr;
        },
        cid() {
            return this.$route.params.cid;
        },
    },

    mounted() {
        this.$nextTick(() => {
            this.visible = true;
        });
    },

    methods: {
        submit() {
            if (this.enable) {
                let list = [];
                let that = this;
                let currentGroupUser = [];
                Object.keys(this.attendeeList).map((item) => {
                    if (this.attendeeList[item].attendeeState === 1) {
                        currentGroupUser.push({
                            uid: this.attendeeList[item].userid,
                            avatar: this.attendeeList[item].avatar,
                            sex: this.attendeeList[item].sex,
                            is_single_chat: 1,
                        });
                    }
                });
                // console.log(currentGroupUser)
                // console.log("this.groupUser:",this.groupUser)
                // console.log("this.friendListOptions:",this.friendListOptions)
                for (let user of this.friendListOptions) {
                    for (let checkedUser of this.groupUser) {
                        if (user.value === checkedUser) {
                            currentGroupUser.push({
                                uid: user.value,
                                avatar: user.avatar,
                                sex: user.sex,
                                is_single_chat: 1,
                            });
                        }
                    }
                }
                // console.log(currentGroupUser)
                if (this.conversation.is_single_chat) {
                    //单聊变群聊
                    for (let user in this.attendeeList) {
                        list.push(this.attendeeList[user].userid);
                    }
                    list = list.concat(this.groupUser);
                    var data = {
                        subject: this.$t('group_chat_text'),
                        group_user_list: list.join(),
                        is_single_chat: 0,
                        type: this.systemConfig.ConversationConfig.type.Group,
                        is_public: 0,
                    };
                    this.$root.socket.emit("request_create_conversation", data, function (is_succ, data) {
                        that.$message.success(that.$t('create_group_text'));
                        that.openConversation(data, 6);
                    });
                } else {
                    for (let user of this.groupUser) {
                        list.push(user);
                    }
                    window.main_screen.conversation_list[this.cid].groupInviteJoin(
                        {
                            uidList: list,
                        },
                        (res) => {
                            if (res.error_code == 0) {
                                if (this.$checkPermission({
                                    conversationPermissionKey:'member.invite_join'
                                },{conversationId:this.cid}) && this.conversation.join_check) {
                                    this.$message.success(this.$t('group_apply_success'));
                                } else {
                                    setTimeout(() => {
                                        this.$root.eventBus.$emit("createGroupAvatar", {
                                            conversation: this.conversation,
                                            userList: currentGroupUser,
                                        });
                                    }, 2000);
                                }
                            }
                        }
                    );
                    this.back();
                }
            }
        },
    },
};
</script>
<style lang="scss">
    // .com_dialog {
    //     .dialog-content {
    //         display: flex;
    //         flex-direction: column;

    //         .list {
    //             flex: 1;
    //             overflow: hidden;
    //             padding-bottom: 50px;
    //         }
    //     }
    // }

// .add_attendee {
//     .el-dialog {
//         //         height:400px !important;
//     }
//     .el-dialog__body {
//         display: flex;
//         flex-direction: column;
//         .list {
//             flex: 1;
//             overflow: hidden;
//             padding-bottom: 50px;
//         }
//         .submit_btn {
//             position: absolute;
//             right: 10px;
//             bottom: 10px;
//         }
//     }
// }
</style>
