<template>
    <transition name="slide">
        <div class="group_manage_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{ $t('group_manage') }}
                </template>
            </mrHeader>
            <div class="group_manage_container">
                <div class="group_public clearfix">
                    <div class="clearfix">
                        <span class="fl">{{ $t('group_join_verify') }}</span>
                        <div class="fr needsclick">
                            <van-switch v-model="joinVerify"  active-color="#00c59d" inactive-color="#D9D9D9" @change="toggleVerify" />
                        </div>
                    </div>
                    <p class="verify_tip">{{ $t('group_join_verify_tip') }}</p>
                </div>
                <div class="group_public group_public_flex" @click="openJoinVerify">
                    <span>{{ $t('group_join_verify_btn') }}</span>
                    <i class="iconfont svg_icon_entry icon-entry"></i>
                </div>
                <div v-if="hasGroupTransferPermission" class="group_public group_public_flex" @click="openTransferGroup">
                    <span>{{ $t('transfer_group') }}</span>
                    <i class="iconfont svg_icon_entry icon-entry"></i>
                </div>
                <div v-if="hasSetAdminPermission" class="group_public group_public_flex" @click="openGroupManagers">
                    <span>{{ $t('group_managers') }}</span>
                    <i class="iconfont svg_icon_entry icon-entry"></i>
                </div>
                <div v-if="hasDeleteGroupPermission" class="btns_container">
                    <button class="primary_bg operat_btn" @click="deleteGroup">{{ $t('delete_group') }}</button>
                </div>
            </div>
            <keep-alive>
                <router-view></router-view>
            </keep-alive>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast, Switch } from 'vant';
import Tool from '@/common/tool'
export default {
    mixins: [base],
    name: 'groupManagePage',
    permission: true,
    components: {
        VanSwitch: Switch
    },
    data(){
        return {
            cid:this.$route.params.cid,
            joinVerify:false,
        }
    },
    beforeDestroy(){
    },
    mounted(){
    },
    activated(){
        this.cid=this.$route.params.cid;
        this.joinVerify = this.conversation.join_check===1;
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{galleryObj:{},iworksList:{}}
        },
        isCreator(){
            return this.user.uid==this.conversation.creator_id&&this.conversation.is_single_chat==0
        },
        isGroupChat(){
            return this.conversation.is_single_chat==0;
        },
        attendeeArray(){
            let list=this.parseObjToArr(this.conversation.attendeeList);
            let filterList = []; //后端把所有用户都返回回来，前端只显示未退群用户
            for(let i=0; i<list.length; i++){
                if(list[i].attendeeState != 0){
                    filterList.push(list[i]);
                }
            }
            this.groupLength=filterList.length;
            let maxFileListSize = 3;
            if(this.isCreator){
                maxFileListSize = 2;
            }
            if (filterList.length>maxFileListSize) {
                this.isMoreMember=true;
                filterList.splice(maxFileListSize)
            }else{
                this.isMoreMember=false;
            }
            return filterList;
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        hasGroupTransferPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.transfer_ownership'},{
                conversationId:this.cid,
            })
        },
        hasSetAdminPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.set_admin'},{
                conversationId:this.cid,
            })
        },
        hasDeleteGroupPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.delete'},{
                conversationId:this.cid,
            })
        },
    },
    methods:{
        openTransferGroup(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/group_manage/transfer_group`);
        },
        openGroupManagers(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/group_manage/group_managers`);
        },
        deleteGroup(){
            let message = "";
            message = this.$t('creator_user_exit_group_tip');//
            Tool.openMobileDialog(
                {
                    message,
                    showRejectButton:true,
                    confirm:()=>{
                        let data={};
                        let message = "";
                        message = "request_delete_group";
                        data.uid = this.user.uid;
                        data.cid = this.cid;
                        data.isCreator = true;
                        this.conversation.socket.emit(message, data, function(is_succ, info){
                            this.settingLoading=false;
                            if(!is_succ){
                                console.log(info);
                                Toast(this.$t('user_exit_group_fail'));
                            }
                        });
                        this.settingLoading=true;
                    }
                }
            )
        },
        openJoinVerify(){
            this.$router.push(this.$route.fullPath+'/join_verify')
        },
        toggleVerify(){
            let join_check=this.joinVerify?1:0;
            window.main_screen.conversation_list[this.cid].groupSettingUpdate({
                key:"join_check",
                value: join_check,
            },(res)=>{
                if(res.error_code === 0){
                    this.$store.commit('conversationList/updateConversation',{
                        cid:this.cid,
                        key:'join_check',
                        value:join_check
                    })
                }else{
                    Toast(this.$t('operate_err'));
                    this.joinVerify = !this.joinVerify;
                }
            })
        }
    }
}
</script>
<style lang="scss">
.group_manage_page{
    .group_manage_container{
        overflow:auto;
        height:calc(100% - 2.2rem);
        .group_public{
            padding:.6rem 0.8rem;
            border-top:1px solid #ddd;
            background-color:#fff;
            &.group_public_flex{
                align-items: center;
                display: flex;
                span{
                    flex:1;
                }
            }
            span{
                font-size:.8rem;
            }
            .verify_tip{
                font-size: .7rem;
                color: #666;
                margin-top: 0.5rem;
            }
            .svg_icon_entry{
                top:2em;
                fill:#aaa;
                color:#aaa;
                width: 0.4rem;
                height: 1.4rem;
                margin-left: 0.8rem;
            }
        }
        .btns_container{
            margin:0 0.5rem;
            .operat_btn{
                display: block;
                width: 100%;
                border: none;
                font-size: 1rem;
                line-height: 2rem;
                margin: 1rem 0rem .6rem;
                border-radius: .2rem;
            }
        }
    }
}
</style>
