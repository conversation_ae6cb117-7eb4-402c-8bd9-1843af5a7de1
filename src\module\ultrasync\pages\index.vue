<template>
    <div class="index_page">
        <cookies-notification v-if="isCookiesNotificationShow" />
        <div class="main_container" v-if="!changeUserLoading">
            <mrHeader :leftArrow="false" class="index_header">
                <!-- 自定义左侧内容 -->
                <template #left>
                    <SilenceStreamIcon v-if="isUltraSoundMobile && functionsStatus.tvwall"></SilenceStreamIcon>
                </template>
                <!-- 自定义标题 -->
                <template #title>
                    <i
                        :class="'app_title_' + PROJECT_NOV + ' iconfont icon-app_title_' + PROJECT_NOV"
                        v-show="active == 'chat'"
                    ></i>
                    <span v-show="active != 'chat'">
                        {{ $t(active) }}
                    </span>
                </template>
                <!-- 自定义右侧内容 -->
                <template #right>
                    <index-menu class="more"></index-menu>
                </template>
            </mrHeader>
            <notifyBar></notifyBar>
            <div class="page-tab-container">
                <div class="left_rainbow">
                    <div class="block1"></div>
                    <div class="block2"></div>
                    <div class="block3"></div>
                    <div class="block4"></div>
                    <div class="block5"></div>
                </div>

                <swiper :options="swiperOption" ref="mySwiper" class="page-tabbar-tab-container">
                    <swiper-slide>
                        <chat-list-page></chat-list-page>
                    </swiper-slide>
                    <swiper-slide>
                        <friend-list-page></friend-list-page>
                    </swiper-slide>
                    <swiper-slide style="overflow-y: hidden">
                        <discover-page></discover-page>
                        <!-- <file-list-page ref="fileListRef"></file-list-page> -->
                    </swiper-slide>
                    <swiper-slide>
                        <mine-page :currentTab="active"></mine-page>
                    </swiper-slide>
                </swiper>
            </div>
            <div class="nav">
                <div class="nav_item" @click.prevent="toggleTab('chat')" :class="{ active: active == 'chat' }">
                    <i class="iconfont svg_chat_active icon-chat_active" v-show="active == 'chat'"></i>
                    <i class="iconfont svg_chat icon-chat" v-show="active != 'chat'"></i>
                    <span class="total_unread" v-show="total > 0">
                        <i class="iconfont svg_ellipsis icon-shenglve" v-if="total > 99"></i>
                        <template v-else>
                            {{ total }}
                        </template>
                    </span>
                    <p>{{ $t('index_nav_chat') }}</p>
                </div>
                <div class="nav_item" @click.prevent="toggleTab('contacts')" :class="{ active: active == 'contacts' }">
                    <i class="iconfont svg_friend_active icon-friend_active" v-show="active == 'contacts'"></i>
                    <i class="iconfont svg_friend icon-friend" v-show="active != 'contacts'"></i>
                    <span class="has_apply" v-show="hasApplys"></span>
                    <p>{{ $t('contacts') }}</p>
                </div>
                <div class="nav_item" v-if="isUltraSoundMobile && functionsStatus.live">
                    <div class="middle_nav_bg">
                        <div class="middle_nav_shadow"></div>
                    </div>
                    <div class="middle_nav_button" @click.stop.prevent="quickLaunchLive">
                        <img src="static/resource/images/middle-button.png" class="img_middle_nav" />
                        <!-- <van-icon name="guide-o" color="#fff" size="40"></van-icon> -->
                    </div>
                </div>
                <div class="nav_item" @click.prevent="toggleTab('discover')" :class="{ active: active == 'discover' }">
                    <!-- <i class="files_icon">
                        <span class="total_unread" v-show="resourceUnread>0">{{resourceUnread}}</span>
                    </i> -->
                    <i class="iconfont svg_library_active icon-library_active" v-show="active == 'discover'"></i>
                    <i class="iconfont svg_library icon-library" v-show="active != 'discover'"></i>
                    <!-- <span class="has_new_msg" v-show="hasNewMsg"></span> -->
                    <p>{{ $t('discover') }}</p>
                </div>
                <div class="nav_item" @click.prevent="toggleTab('mine')" :class="{ active: active == 'mine' }">
                    <i class="iconfont svg_mine_active icon-mine_active" v-show="active == 'mine'"></i>
                    <i class="iconfont svg_mine icon-mine" v-show="active != 'mine'"></i>
                    <span class="has_new_msg" v-if="hasMineMsg"></span>
                    <p>{{ $t('index_nav_mine') }}</p>
                </div>
            </div>
            <!-- <v-touch @swiperight="globalSwipeRight" class="global_swipe_right"> </v-touch> -->
            <mr-popup :visible.sync="isShowSetPassword" :closeOnClockModal="false">
                <template v-if="presetPasswordType == 1">
                    <p class="title">{{ $t('welcome_tip') }}</p>
                    <div class="btns">
                        <button class="primary_bg" @click="gotoModifyPassword">{{ $t('set_password_first') }}</button>
                    </div>
                    <div class="btns skip_btn">
                        <button class="primary_bg" @click="skipModifyPassword">{{ $t('skip_set_password') }}</button>
                    </div>
                </template>
                <template v-else-if="presetPasswordType == 2">
                    <p class="title">{{ $t('enhance_password_tip') }}</p>
                    <div class="btns">
                        <button class="primary_bg" @click="gotoModifyPassword">{{ $t('modify_password_text') }}</button>
                    </div>
                </template>
            </mr-popup>
            <init-organization></init-organization>
            <keep-alive :include="/chat/">
                <router-view></router-view>
            </keep-alive>
        </div>
        <gallery></gallery>
        <transmit></transmit>
        <AddCustomTag></AddCustomTag>
        <group-avatar ref="group_avatar"></group-avatar>
        <user-avatar ref="user_avatar"></user-avatar>
        <quickLaunchLive :show.sync="isShowQuickLaunchModel" ref="quickLaunchLive"></quickLaunchLive>
        <ReviewEditDialog :message="currentReviewFile" ref="reviewEditDialog"></ReviewEditDialog>
        <favorite-confirm ref="favorite_confirm"></favorite-confirm>
        <loading-page v-if="showLoadingPage"></loading-page>
        <quickSelectionEntrance
            v-if="isTEAir && isFirstLoadServerInfo"
            ref="quickSelectionEntrance"
        ></quickSelectionEntrance>
        <professional-identity-dialog
            :show.sync="showProfessionalIdentityDialog"
            @success="handleProfessionalIdentitySuccess"
        ></professional-identity-dialog>
    </div>
</template>
<script>
import { Toast } from "vant";
import chatListPage from "../components/chatListPage.vue";
import friendListPage from "../components/friendListPage.vue";
// import fileListPage from "../components/fileListPage.vue";
import discoverPage from "../components/discoverPage.vue";
import minePage from "../components/minePage.vue";
// import gallery from "../components/gallery.vue";
import transmit from "../components/transmit.vue";
import cookiesNotification from "../components/cookiesNotification.vue";
import service from "../service/service";
import base from "../lib/base";
import multiCenterService from "../service/multiCenterService";
import iworksTool from "../lib/iworksTool";
import send_message from "../lib/send_message.js";
import ServiceConfig from "@/common/ServiceConfig.js";
import CMainScreen from "../lib/CMainScreen";
import CAiEngineer from "../lib/CAiEngineer";
import CFileTransferAssistanter from "../lib/CFileTransferAssistanter";
import CCentralStationProvider from "../lib/CCentralStationProvider";
import CCentralStationUser from "../lib/CCentralStationUser";
import CFeedbackQuestionAssistanter from "../lib/CFeedbackQuestionAssistanter";
import aiAnalyze from "../lib/aiAnalyze";
import "swiper/dist/css/swiper.css";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import groupAvatar from "../components/groupAvatar";
import userAvatar from "../components/userAvatar";
import notifyMsgFromOwnerMixin from "../mixins/notifyMsgFromOwnerMixin";
import mrPopup from "../MRComponents/mrPopup";
import Tool from "@/common/tool.js";
import groupsetTool from "../lib/groupsetTool";
import { newMainDB } from "@/common/indexdb/index.js";
import quickLaunchLive from "../components/quickLaunchLive.vue";
import InitOrganization from "../components/initOrganization.vue";
import ReviewEditDialog from "../components/messageItem/reviewEditDialog.vue";
import AddCustomTag from "../components/addCustomTag.vue";
import { destroyAllConference,resetGlobalCustomWindowObject, resetLoginStorage } from "../lib/common_base";
import LoadingPage from "../components/loadingPage.vue";
import quickSelectionEntrance from "../components/quickSelectionEntrance.vue";
import { cloneDeep } from "lodash";
import indexMenu from "../components/indexMenu.vue";
import notifyBar from "../components/notifyBar.vue";
import SilenceStreamIcon from "../components/silenceStreamIcon.vue";
import ProfessionalIdentityDialog from "../components/professionalIdentityDialog.vue";
import {Logger} from '@/common/console.js'
import {
    setWithDrawData,
    setExpirationResource,
    setIworksInfoToMsg,
    getLocalImgUrl,
    parseImageListToLocal,
    findServiceId,
    closeDeviceWindowIfNeed,
    closeChatWindowForce,
    sendTransferLocal,
    sendTransferExam,
    pushImageToList,
    handleAfterLogin,
    backToIndex,
    patientDesensitization,
    parseServerInfo,
    transferPatientInfo,
    getDefaultPreferences,
    getLiveRoomObj,
} from "../lib/common_base";
import { resumeAllTasks } from "@/common/oss";
import favoriteConfirm from "../components/favoriteConfirm";
export default {
    name: "IndexPage",
    permission: true,
    mixins: [base, send_message, aiAnalyze, iworksTool, groupsetTool, notifyMsgFromOwnerMixin],
    components: {
        chatListPage,
        friendListPage,
        // fileListPage,
        discoverPage,
        minePage,
        swiper,
        swiperSlide,
        gallery: () => import(/* webpackPrefetch: true */ '../components/gallery.vue'),
        transmit,
        groupAvatar,
        userAvatar,
        mrPopup,
        // VanIcon:Icon,
        quickLaunchLive,
        InitOrganization,
        ReviewEditDialog,
        AddCustomTag,
        favoriteConfirm,
        LoadingPage,
        quickSelectionEntrance,
        indexMenu,
        cookiesNotification,
        notifyBar,
        SilenceStreamIcon,
        ProfessionalIdentityDialog,
    },
    computed: {
        total() {
            let showTotal = 0;
            let chatList;
            chatList = this.chatList;
            showTotal = showTotal + this.$store.state.ultrasoundMachine.unread;
            for (let chatItem of chatList) {
                if (!chatItem.is_mute) {
                    showTotal += chatItem.unread;
                }
            }
            return showTotal;
        },
        // resourceUnreview(){
        //     return this.$store.state.resourceUnreview
        // },
        // resourceUnread(){
        //     return this.resourceUnreview.count;
        // },
        hasApplys() {
            let has = false;
            if (this.notifications.friendApply.length > 0 || this.notifications.groupApply.length > 0) {
                has = true;
            }
            return has;
        },
        consultationImageList() {
            return this.$store.state.consultationImageList.list;
        },
        hasMineMsg() {
            console.log("*************************************hasMineMsg*****************************");

            return (
                !!this.user.probationary_expiry ||
                !this.user.is_enhance_password ||
                !this.user.is_password_privatized ||
                0
            );
        },
        chatList() {
            return this.$store.state.chatList.list;
        },
        loadingConfig() {
            return this.$store.state.loadingConfig;
        },
        appPlus() {
            return this.isApp;
        },
        deviceInfo() {
            return this.$store.state.device;
        },
        isTEAir() {
            return this.deviceInfo.isTEAir;
        },
        isUltraSoundMobile() {
            return this.deviceInfo.isUltraSoundMobile;
        },
        groupPublicState() {
            return this.$store.state.systemConfig.groupPublicState;
        },
        showLoadingPage() {
            return (this.isTEAir && this.showTEAirLoading) || this.changeUserLoading;
        },
    },
    data() {
        return {
            PROJECT_NOV: process.env.VUE_APP_PROJECT_NOV,
            isCookiesNotificationShow: false,
            active: "chat",
            popupMore: false,
            notifications: this.$store.state.notifications,
            hasDisconnectTip: false,
            swiperOption: {
                on: {
                    slideChange: this.slideChange,
                    // init:function(){
                    //     setTimeout(function(){
                    //         document.getElementById('test').style.height='100%'
                    //     },1000)

                    // }
                },
            },
            appVersion: "",
            operateChat: {},
            notifying: false,
            isShowSetPassword: false,
            presetPasswordType: 0,
            isShowQuickLaunchModel: false,
            isTopFileTransferAssistant: false, //是否置顶文件传输助手(多端登录)
            hasSetCurrentList: false,
            isAutoLogging: false, //防止重复自动登录
            currentReviewFile: {},
            isFirstLoadServerInfo: false,
            showTEAirLoading: true,
            currentReLoginTimestamp: 0,
            pushStreamReady: false,
            changeUserLoading:false,
            showProfessionalIdentityDialog: false,
            autoLoginTimeout: null,
            // 新增：用于处理回到前台时的自动登录逻辑
            hasTriedAutoLoginAfterResume: false, // 是否已经尝试过回到前台后的自动登录
            resumeCheckTimeout: null, // 回到前台后的检查定时器
        };
    },
    async beforeCreate() {
        //初始化离线数据
        let localUid = localStorage.getItem("uid");
        if (localUid) {
            const mainDB = newMainDB(localUid);
            let chatList = await mainDB.chatList.orderBy("last_message_ts").reverse().limit(100).toArray();
            const friendList = await mainDB.friendList.toCollection().toArray();
            const groupList = await mainDB.groupList.toCollection().toArray();
            this.setCurrentList(true, chatList, true);
            this.setFriendList(true, friendList, true);
            this.setGroupList(true, groupList, true);
            let lastMessageList = await mainDB.lastMessageList.toCollection().toArray();
            if (!lastMessageList || Object.keys(lastMessageList).length == 0) {
                return;
            }
            for (var i in chatList) {
                for (var j in lastMessageList) {
                    if (chatList[i].cid == lastMessageList[j].group_id) {
                        chatList[i].message = { ...lastMessageList[j] };
                        break;
                    }
                }
            }
            this.setLastMessage(true, chatList);
        }
    },
    created() {
        this.debounceUpdateLiveCount = Tool.debounce(this.updateLiveCount, 1000);
        this.debounceSortChatList = Tool.throttle(this.sortChatList, 600, true);
    },
    mounted() {
        var that = this;
        setTimeout(() => {
            this.isCookiesNotificationShow = Tool.checkAppClient("Browser");
        }, 2000);
        window.addEventListener("online", function () {
            console.log("网络已连接");
        });

        window.addEventListener("offline", function () {
            //兼容IOS下，socket无法主动检测到应用wifi关闭问题
            console.log("网络已断开");
        });
        this.$nextTick(() => {
            console.log("--------------- index.vue this.$nextTick ------------");
            if (this.systemConfig.appVersion != "Prod") {
                if (this.systemConfig.appVersion == "Beta") {
                    this.appVersion = "（Beta版）";
                } else {
                    this.appVersion = "（Dev版）";
                }
            }
            if (this.user.fromLogin) {
                console.log("--------------- this.user.fromLogin ------------");
                var login_user = this.user;
                var account = window.localStorage.getItem("account") || "";
                login_user.login_name = account;
                that.initPage();
                that.initNetworkData();
                that.$refs["user_avatar"].ifCreateUserAvatar();
            } else {
                that.autoLogin(() => {
                    that.initPage();
                    that.initNetworkData();
                    that.$refs["user_avatar"].ifCreateUserAvatar();
                    console.log("*************** autoLogin finish  **************");
                });
            }
            //一键转发会诊文件回调，方法在send_message.js中
            this.$root.eventBus.$off("initMachineTransfer").$on("initMachineTransfer", this.initMachineTransfer);
            this.$root.eventBus.$off("updateMachineTransfer").$on("updateMachineTransfer", this.updateMachineTransfer);
            this.$root.eventBus.$off("finishMachineTransfer").$on("finishMachineTransfer", this.finishMachineTransfer);
            this.$root.eventBus.$off("checkWebimPageState").$on("checkWebimPageState", this.checkWebimPageState);
            // this.$root.eventBus.$off('wechatSendGroupInfo').$on('wechatSendGroupInfo',function(data){
            //     console.log("event bus message wechatSendGroupInfo",data)
            //     setTimeout(function () {
            //         that.autoOpenWechatShareWithParam(data); //当用户从外部网页直接快速进入APP时会进入
            //     }, 1500);
            // });
            this.$root.eventBus.$off("getUnreadMsgNum").$on("getUnreadMsgNum", function () {
                window.CWorkstationCommunicationMng.NotifyUnreadMsg({ msgNum: that.total });
            });
            this.$root.eventBus.$off("HttpNotifyProgress").$on("HttpNotifyProgress", function (data) {
                if (data.error) {
                    Toast(that.$t('file_upload_exception'));
                } else {
                    //App上传后会触发update_file_transmit_progress事件
                }
            });
            this.$root.eventBus.$off("updateProgressOSS").$on("updateProgressOSS", function (data) {
                if (data.error) {
                    that.$store.commit("conversationList/updateUploadFail", {
                        cid: data.cid,
                        file_id: data.file_id,
                    });
                } else {
                    that.$store.commit("conversationList/updateFileProgress", {
                        msg: {
                            file_id: data.file_id,
                            group_id: data.cid,
                        },
                        percent: data.progress,
                    });
                    if (data.progress == 100) {
                        that.updateUploadProgress(data);
                    } else {
                    }
                }
            });
            this.$root.eventBus.$off("generalTransmitSubmit").$on("generalTransmitSubmit", (data) => {
                let queue = this.$root.transmitTempList;
                if (queue && queue.length > 0 && queue[0].msg_type === this.systemConfig.msg_type.LIVE_INVITE) {
                    this.sendLiveTransmitMessage(data);
                } else {
                    this.ai_share_to_group = data.ai_share_to_group;
                    //转发给cid或者未开启会话过的id
                    this.$root.transmitQueue[data.cid || "f-" + data.id] = queue;
                    if (this.conversationList[data.cid]) {
                        //会话已开启则直接转发
                        this.sendTransmitMessage(data.cid);
                    } else {
                        //会话未开启则开启会话
                        if (data.cid) {
                            this.openConversation(data.cid, 7);
                        } else {
                            this.openConversation(data.id, 3);
                        }
                    }
                }
            });
            this.$root.eventBus.$off("toggleTab").$on("toggleTab", this.toggleTab);
            this.$root.eventBus
                .$off("openConversationFromIndexByUserId")
                .$on("openConversationFromIndexByUserId", that.openConversationFromIndexByUserId);
            this.$root.eventBus
                .$off("closeChatWindowFromIndex")
                .$on("closeChatWindowFromIndex", that.closeChatWindowFromIndex);
            this.$root.eventBus
                .$off("NotifyCloudLoginOrLogout")
                .$on("NotifyCloudLoginOrLogout", that.NotifyCloudLoginOrLogout);
            this.$root.eventBus
                .$off("operatePushStreamByULinker")
                .$on("operatePushStreamByULinker", that.operatePushStreamByULinker);
            this.$root.eventBus.$off("updateLiveCount").$on("updateLiveCount", that.debounceUpdateLiveCount);
            document.addEventListener("visibilitychange", this.handleAppHidden);
            this.$root.eventBus.$off("clearAndDirectToLogin").$on("clearAndDirectToLogin", that.clearAndDirectToLogin);
            this.$root.eventBus.$off('unBindControllerEvent').$on('unBindControllerEvent',that.unBindControllerEvent)
            window.addEventListener("beforeunload", function (event) {
                // 页面即将卸载时的处理逻辑
                destroyAllConference();
            });
        });
    },
    beforeDestroy() {
        document.removeEventListener("visibilitychange", this.handleAppHidden);
        // 清理定时器
        if (this.resumeCheckTimeout) {
            clearTimeout(this.resumeCheckTimeout);
            this.resumeCheckTimeout = null;
        }
    },
    methods: {
        // 检查用户的职业身份设置
        checkProfessionalIdentity(userInfo) {
            // 根据functionsStatus.professionalIdentityForce来判断是否需要强制设置职业身份
            if (!this.functionsStatus.professionalIdentityForce) {
                return;
            }

            // 检查用户信息中是否包含professional_identity字段并且有值
            if (!userInfo.professional_identity) {
                // 没有设置职业身份，显示弹窗
                this.showProfessionalIdentityDialog = true;
            }
        },

        // 职业身份设置成功回调
        handleProfessionalIdentitySuccess() {
            this.showProfessionalIdentityDialog = false;
        },

        initPage() {
            this.initMainScreen();
        },
        initNetworkData() {
            this.hasDisconnectTip = false;
            window.main_screen.initGateway(this.$store.state.user.client_uuid);

            var controller = window.main_screen.controller;
            controller.init(this);
            this.initMainScreenControllerEvent(controller);
            this.handleAutoJoinGroup();
            // this.checkPassword(this.user)//注册后不弹设置密码了
            // setTimeout(()=>{
            //     this.ifNeedAutoPushStream()
            // },2000)
            // setTimeout(()=>{
            //     if(this.$store.state.chatList.list){

            //     }

            // },500)
        },
        initMainScreen() {
            let socketServer =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.websocket_prot;
            var option = {
                uid: this.user.id,
                client_type: this.systemConfig.clientType,
                client_uuid: "",
                url: socketServer,
                service_type: this.user.service_type,
            };
            window.main_screen = this.newMainScreen(option);
            var controller = window.main_screen.controller;
            controller.init(this);
            // this.initMainScreenControllerEvent(controller);
        },
        newMainScreen(option) {
            var main_screen = null;
            if (
                ServiceConfig.type.AiAnalyze == option.service_type ||
                ServiceConfig.type.DrAiAnalyze == option.service_type
            ) {
                main_screen = new CAiEngineer(option);
            } else if (ServiceConfig.type.FileTransferAssistant == option.service_type) {
                main_screen = new CFileTransferAssistanter(option);
            } else if (ServiceConfig.type.CentralStation == option.service_type) {
                main_screen = new CCentralStationProvider(option);
            } else if (ServiceConfig.type.CentralStationUser == option.service_type) {
                main_screen = new CCentralStationUser(option);
            } else if (ServiceConfig.type.FeedbackQuestionAssistant == option.service_type) {
                main_screen = new CFeedbackQuestionAssistanter(option);
            } else {
                main_screen = new CMainScreen(option);
            }

            return main_screen;
        },
        initMainScreenControllerEvent(controller) {
            this.isAutoLogging = false;
            var that = this;
            //Gateway
            controller.on("gateway_connect", () => {
                this.socketConnectSuccess(controller);
            });
            controller.on("gateway_error", function (data) {
                that.socketError(data);
            });
            controller.on("gateway_reconnecting", function () {
                that.socketReconnecting();
            });
            controller.on("gateway_reconnect_fail", function (data) {
                that.socketReconnectFail(data);
            });
            controller.on("gateway_reconnect", function () {
                that.socketReconnect();
            });
            controller.on("gateway_disconnect", function (e) {
                that.socketDisconnect(e);
            });
            //MainScreen
            controller.on("recent_active_conversation_list", function (is_succ, data) {
                // let info = that.$store.state.clipBoard.data;
                // if (!info) {
                //     window.CWorkstationCommunicationMng.getClipboard();
                // }
                that.setCurrentList(is_succ, data, false, () => {
                    setTimeout(() => {
                        that.getClipboardInfo();
                    }, 400);
                });
            });
            controller.on("recent_active_conversation_list_last_message", function (is_succ, data) {
                that.setLastMessage(is_succ, data);
                that.$store.commit("chatList/updateUnReadMap", data);
            });
            controller.on("friend_list", function (is_succ, data) {
                that.setFriendList(is_succ, data);
            });
            controller.on("userAddLoginClient", function (data) {
                if (data.allClientType.length > 1) {
                    that.isTopFileTransferAssistant = true;
                    if (that.hasSetCurrentList) {
                        //已登录的一端监听到多端登录
                        that.topFileTransferAssistant();
                    }
                }
            });
            controller.on("conversation_list", function (is_succ, data) {
                let list = that.parseObjToArr(data);
                that.setGroupList(is_succ, list);
            });
            controller.on("group_applys", function (is_succ, data) {
                that.setGroupApplys(is_succ, data);
            });
            controller.on("friend_applys", function (is_succ, data) {
                that.dealFriendApplys(is_succ, data);
            });
            controller.on("userResponseFriendApply", function (data) {
                that.friendApplyResponse(data);
            });
            controller.on("userAddFriend", function (data) {
                that.notifyAddFriend(data.friendInfo);
            });
            controller.on("userApplyAddFriend", function (data) {
                that.setFriendApplys(data);
            });

            //Other
            controller.on("notify_add_friend", function (data) {
                that.notifyAddFriend(data);
            });
            controller.on("update_friend_info", function (data) {
                that.updateFriendInfo(data);
            });
            controller.on("notify_friend_destroy", function (data) {
                that.updateFriendDestroy(data);
            });
            controller.on("update_user_info", function (data) {
                that.onUpdateUserInfo(data);
            });
            controller.on("update_user_portrait_img", function (data) {
                that.onUpdateUserPortraitInfo(data);
            });
            controller.on("notify_start_conversation", function (is_succ, conversation, start_type) {
                that.NotifyStartConversation(is_succ, conversation, start_type);
            });
            controller.on("notify_login_another", function () {
                if (!window.iSLanguageChanged) {
                    that.notifyLoginAnother();
                }
            });
            controller.on("notify_user_destroy", function () {
                that.notifyUserDestroy();
            });
            controller.on("server_info", function (data) {
                let json = parseServerInfo(data);
                that.$store.commit("systemConfig/updateSystemConfig", {
                    serverInfo: json,
                });
                if (json.network_environment === 1 && json.storageReplaceInfo.replace) {
                    that.observeImageLoad(json);
                }
                if (Tool.checkAppClient("App") && !Tool.checkAppClient("Browser")) {
                    window.CWorkstationCommunicationMng.QueryAppDeviceInfo();
                    if (!that.isFirstLoadServerInfo) {
                        that.isFirstLoadServerInfo = true;
                        Tool.initNativeAgoraSdk(json.agora_appid).then(async () => {
                            setTimeout(() => {
                                that.showTEAirLoading = false;
                                that.pushStreamReady = true;
                                that.autoPushStream();
                                window.CWorkstationCommunicationMng.NotifyUserState({ errorcode: 0, errormsg: "" });
                            }, 1000);
                        });
                    }
                }

                // 在server_info更新后检查并自动设置隐私协议状态
                setTimeout(() => {
                    that.checkAndSetPrivacyAgreement();
                }, 100);
            });
            // controller.on("tag_top_info",function(is_succ, data){
            //     if(is_succ){
            //         that.$store.commit('gallery/addTagTopInfo', data)
            //     }else{
            //         console.log("get tag_top_info failed")
            //     }

            // });
            controller.on("notify_delete_group", function (json_str) {
                console.log("notify_delete_group", json_str);
                let data = typeof json_str == "string" ? JSON.parse(json_str) : json_str;
                if (window.main_screen.conversation_list.hasOwnProperty(data.cid)) {
                    window.main_screen.conversation_list[data.cid].onResponseDeleteAttendee();
                }

                that.$store.commit("chatList/deleteChatList", { cid: data.cid });
                that.$store.commit("groupList/deleteGroupList", { cid: data.cid });
                that.$store.commit("conversationList/deleteConversationList", { cid: data.cid });
                that.$store.commit("notifications/deleteGroupApplyByCid", { cid: data.cid });

                //删除群相关的图像
                that.$store.commit("consultationImageList/deleteConsultationImageListByGroupID", { cid: data.cid });
                that.$store.commit("gallery/deleteGalleryListByGroupID", { cid: data.cid });
                let liveRoom = getLiveRoomObj(window.vm.$root.currentLiveCid);
                if (liveRoom) {
                    liveRoom.LeaveChannelAux();
                }
                closeChatWindowForce();
            });

            controller.on("notify_device_event", function (data) {
                that.notifyDeviceEvent(data);
            });
            controller.on("notify_exception", function (data) {
                that.notifyException(data);
            });
            controller.on("notify_add_groupset", function (err, result) {
                if (!err) {
                    that.notifyAddGroupset(result.groupset);
                }
            });

            controller.on("notify_update_groupset", function (err, result) {
                if (!err) {
                    if (result.groupset.list) {
                        that.notifyUpdateGroupset(result.groupset);
                    } else {
                    }
                }
            });

            controller.on("notify_delete_groupset_list", function (err, result) {
                if (!err) {
                    that.notifyDeleteGroupset(result.groupset_id_list);
                }
            });

            controller.on("notify_update_groupset_portrait", function (err, result) {
                if (!err) {
                    that.notifyUpdateGroupsetAvatar(result);
                }
            });
            controller.on("user_info", (is_succ, info) => {
                info.preferences = getDefaultPreferences(info);
                that.$store.commit("user/updateUser", info);
                // 检查职业身份设置
                that.checkProfessionalIdentity(info);
            });
            //未开启会话，接受消息通知
            controller.on("receive_group_message", (data) => {
                console.log("receive_group_message", data);
                if (data.group_id && !that.conversationList.hasOwnProperty(data.group_id)) {
                    that.setSayChatMessageReceiveGroupMessage(data, false);
                    if (
                        data.msg_type === that.systemConfig.msg_type.LIVE_INVITE ||
                        data.groupInfo.service_type === that.systemConfig.ServiceConfig.type.LiveBroadCast
                    ) {
                        that.debounceUpdateLiveCount();
                    }
                }
            });
            controller.on("notify_agora_live_start", function (data) {
                that.NotifyAgoraLiveStart(data);
            });
            controller.on("notify_agora_live_stop", function (data) {
                that.NotifyAgoraLiveStop(data);
            });
            controller.on("notify_update_recording", function (data) {
                that.NotifyUpdateLiveRecord(data);
            });
            controller.on("notify_update_announcement", function (data) {
                that.NotifyUpdateAnnouncement(data);
            });
            controller.on("notify.group.resource.delete.exam", function (data) {
                that.$store.commit("examList/deleteExamListItem", {
                    cid: data.groupID,
                    exam_id: data.examID,
                });
                that.$root.eventBus.$emit("deleteExamItem");
                if (Array.isArray(data.deleteResourceIDList)) {
                    data.deleteResourceIDList.forEach((resource_id) => {
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id,
                            data: {
                                state: 0, //被删除
                            },
                        });
                        that.$store.commit("chatList/updateLastChatMessageByResourceDelete", {
                            cid: data.groupID,
                            data: {
                                msg_type: that.systemConfig.msg_type.ResourceDelete,
                                resource_id,
                            },
                        });
                    });
                }
                if (Array.isArray(data.deleteMessageIDList)) {
                    that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                        gmsg_id_list: data.deleteMessageIDList,
                        cid: data.groupID,
                    });
                }
                that.$root.eventBus.$emit('notifyDeleteChatMessages',{
                    cid: data.groupID,
                    gmsg_id_list:data.deleteMessageIDList
                })
            });
            controller.on("notify.group.resource.delete.resource", (data) => {
                if (Array.isArray(data.deleteResourceIDList)) {
                    data.deleteResourceIDList.forEach((resource_id) => {
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id,
                            data: {
                                state: 0, //被删除
                            },
                        });
                        if (data.deleteMessageIDList.length > 0) {
                            that.$store.commit("chatList/updateLastChatMessageByResourceDelete", {
                                cid: data.groupID,
                                data: {
                                    msg_type: that.systemConfig.msg_type.ResourceDelete,
                                    resource_id,
                                },
                            });
                        }
                        that.$root.eventBus.$emit("deleteFileToExamList", {
                            cid: data.groupID,
                            resource_id,
                        });
                    });
                }
                if (Array.isArray(data.deleteMessageIDList)) {
                    that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                        gmsg_id_list: data.deleteMessageIDList,
                        cid: data.groupID,
                    });
                    that.$root.eventBus.$emit('notifyDeleteChatMessages',{
                        cid: data.groupID,
                        gmsg_id_list:data.deleteMessageIDList
                    })
                }
            });
            controller.on("notify_refresh_manager_groupset_list", (data) => {
                console.log("notify_refresh_manager_groupset_list", data);
                if (data.action === "delete" && Number(this.$route.params.id) === data.groupSetID) {
                    Tool.backToRoute("/index/groupsets");
                }
                this.getManagerGroupsetList();
            });
            controller.on("notify_refresh_my_groupset_list", (data) => {
                console.log("notify_refresh_my_groupset_list", data);
                if (data.action === "delete" && Number(this.$route.params.id) === data.groupSetID) {
                    Tool.backToRoute("/index/groupsets");
                }
                this.getGroupSetList();
            });
            //主控--直播分析结果
            controller.on("update_ai_analyze_report", (is_succ, data) => {
                console.log("mainScreen update_ai_analyze_report", data);
                //数据返回正常
                if (data.task_id && this.isUltraSoundMobile) {
                    if (data.request_params && data.request_params.file_type === "STREAMSINGAL") {
                        //视频流--只有ulinker支持
                        data.report = (data.report || []).reduce((h, v) => {
                            v.name = this.$t(v.clip_id);
                            if (v.sei) {
                                let now = new Date();
                                let timestamp = now.getTime();
                                console.info("analyze delay(ms):", Math.abs(timestamp - v.sei));
                            }
                            h.push(v);
                            return h;
                        }, []);
                        window.CWorkstationCommunicationMng.NotifyImageAnalyzeReport(data);
                        if (
                            data.error_code ||
                        data.state == "SUCCESS" ||
                        data.state == "FAILURE" ||
                        data.state == "REVOKED"
                        ) {
                            let liveRoom = getLiveRoomObj();
                            this.$root.eventBus.$emit("sendImageWithAiReport", data.task_id);
                            // window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                            //     error_code:1,
                            //     data:{
                            //         switch:false
                            //     },
                            //     error_message: data.error_message,
                            //     key:data.key
                            // });
                            //通知直播间
                            window.vm.$root.eventBus.$emit("NotifyApplySwitchAIAnalyze", {
                                switch: false,
                                aiInfo: {},
                            });
                        }
                    }

                }
            });
            //云作业待完成列表更新通知
            controller.on("student_answer_sheet_update", (data) => {
                if (data.type === "add") {
                    // 新增时全局待完成数为0才更新
                    if (this.$store.state.homework.globalUnfinish === 0) {
                        this.getUnfinishedHomework(0);
                    }
                } else {
                    this.getUnfinishedHomework(0);
                }
                data.gidList.forEach((cid) => {
                    if (this.$store.state.conversationList[cid]) {
                        this.getUnfinishedHomework(cid);
                    }
                });

                // 添加对已批改作业的处理
                this.getCorrectedHomework(0);
                data.gidList.forEach(cid=>{
                    if (this.$store.state.conversationList[cid]) {
                        this.getCorrectedHomework(cid);
                    }
                })
            });
            //云作业待批改列表更新通知
            controller.on("teacher_answer_sheet_update", (data) => {
                if (data.type === "add") {
                    // 新增时全局待批改数为0才更新
                    // if (this.$store.state.homework.globalUnCorrect === undefined) {
                    this.getUncorrectHomework(0);
                    // }
                } else {
                    this.getUncorrectHomework(0);
                }
                data.gidList.forEach((cid) => {
                    if (this.$store.state.conversationList[cid]) {
                        this.getUncorrectHomework(cid);
                    }
                });
            });
            this.initNotifyMsgFromOwnerListener(controller);
            //同账号自己给自己发通知
            controller.on('notify.user.device.sync.live',async (data)=>{
                if(!this.isUltraSoundMobile){
                    return
                }
                if(this.changeUserLoading){
                    return
                }
                if(data.userId === this.user.uid){
                    this.startLiveToULinker(data)
                }else{
                    if(data.token){
                        // window.main_screen && window.main_screen.controller.off("gateway_error");
                        // window.main_screen && window.main_screen.controller.off("gateway_disconnect");
                        this.unBindControllerEvent();
                        if (window.main_screen && window.main_screen.gateway) {
                            window.main_screen.CloseSocket();
                        }
                        destroyAllConference();
                        resetGlobalCustomWindowObject()
                        resetLoginStorage()
                        window.vm.$root.resetCustomRootObject();
                        this.changeUserLoading = true
                        await Tool.backToRoute('/index')
                        window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
                        this.clearAllStore();

                        window.localStorage.setItem("loginToken", data.token);
                    }
                    console.info("change user notify.user.device.sync.live");
                    setTimeout(()=>{
                        this.autoLogin(async(is_succ) => {
                            if(!is_succ){
                                return
                            }
                            this.initPage();
                            this.initNetworkData();
                            Toast(this.$t('account_sync_completed_tips'))
                            if(data.group_id){
                                setTimeout(async()=>{
                                    await Tool.handleAfterMainScreenCreated();
                                    this.changeUserLoading = false
                                    this.startLiveToULinker(data)
                                },500)
                            }else{
                                this.changeUserLoading = false
                            }

                        });
                    },1000)

                }


            });

            // 监听协和发起直播消息
            controller.on("pumch.qclive", (data) => {
                console.log("[index] 接收到pumch.qclive消息", data);
                // 弹出确认对话框让用户确认是否进入会诊模式
                Tool.openMobileDialog({
                    message: "收到来自远程会诊平台的直播邀请，是否切换到会诊模式进入直播？",
                    closeOnPopstate: false,
                    showRejectButton: true,
                    confirm: () => {
                        console.log("[index] 用户确认进入会诊模式");
                        // 将消息存储到store中，等待切换到会诊模式时处理
                        this.$store.commit('globalParams/setPendingLiveInfo', data);
                        console.log("[index] pumch.qclive消息已存储到store");
                        // 这里可以添加切换到会诊模式的逻辑，或者让用户手动切换
                        this.clearAndDirectToULinkerConsultation()
                    }
                });
            });
        },
        notifyUpdateGroupsetAvatar(result) {
            let groupset = {
                avatar: result.avatar,
            };
            this.$store.commit("groupset/updateGroupsetAvatar", {
                avatar: groupset.avatar,
                id: result.groupset_id,
            });
            Toast(this.$t('modify_photo_success'));
            this.back();
        },
        toggleTab(page) {
            this.active = page;
            let index = 0;
            this.$root.currentActiveTab = page;
            /* eslint-disable  */
            switch (page) {
                case "chat":
                    index = 0;
                    break;
                case "contacts":
                    index = 1;
                    break;
                case "discover":
                    index = 2;
                    break;
                case "mine":
                    index = 3;
                    break;
            }
            /* eslint-disable  */
            this.$refs.mySwiper.swiper.slideTo(index, 0, false);
        },
        setCurrentList(is_succ, list, fromLocal, cb) {
            //设置最近会话列表数据
            if (is_succ) {
                this.$store.commit("loadingConfig/updateLoaded", {
                    key: "loadedChatList",
                    loaded: true,
                });
                if (fromLocal) {
                    if (list.length == 0 || this.$store.state.chatList.list.length > 0) {
                        return;
                    }
                }
                this.$store.commit("chatList/initChatList", { arr: list });
                cb && cb();
                //网络数据
                if (!fromLocal) {
                    if (this.isTopFileTransferAssistant) {
                        this.topFileTransferAssistant();
                    }
                    this.hasSetCurrentList = true;
                }
            } else {
                cb && cb();
                Toast("setCurrentList error");
            }
            //this.$root.socket.initLastMessage(this.setLastMessage);
        },
        setLastMessage(is_succ, list) {
            //设置最后一条消息记录
            if (is_succ) {
                for (let item of list) {
                    item.message.original_msg_body = item.message.msg_body
                    item.message.msg_body = this.parseMessageBody(item.message.msg_body);
                    if (item.message.been_withdrawn === 2) {
                        item.message.msg_type = this.systemConfig.msg_type.WITHDRAW;
                    } else if (item.message.been_withdrawn === 1) {
                        item.message.msg_type = this.systemConfig.msg_type.ResourceDelete;
                    }
                    this.$store.commit("chatList/setLastMessage", item);
                }
            } else {
                Toast("setLastMessage error");
            }
        },
        setFriendList(is_succ, list, fromLocal) {
            //设置好友列表
            if (is_succ) {
                this.$store.commit("loadingConfig/updateLoaded", {
                    key: "loadedFriendList",
                    loaded: true,
                });
                if (fromLocal && this.$store.state.friendList.list.length > 0) {
                    return;
                }
                list.sort(function (a, b) {
                    //先排序把相同姓氏的好友排在一起
                    if (a.nickname > b.nickname) {
                        return 1;
                    } else {
                        return -1;
                    }
                });
                this.$store.commit("friendList/initFriendList", list);
            } else {
                Toast("setFriendList error");
            }
        },
        async topFileTransferAssistant() {
            //置顶文件传输助手
            let service_type = this.systemConfig.ServiceConfig.type.FileTransferAssistant;
            let analyze = await findServiceId(service_type);
            console.log("文件传输助手id:", analyze);
            if (analyze.cid) {
                //会话列表中存在文件传输助手
                this.$store.commit("chatList/setTopChat", analyze.cid);
            } else {
                //会话列表没有则新开一个会话
                var that = this;
                let fid = analyze.id;
                this.$root.socket.emit(
                    "request_start_single_chat_conversation",
                    {
                        list: [fid, this.user.uid],
                        start_type: undefined,
                        mode: this.systemConfig.ConversationConfig.mode.Single,
                        type: this.systemConfig.ConversationConfig.type.Single,
                    },
                    function (is_succ, data) {
                        if (is_succ) {
                            that.$store.commit("conversationList/initConversation", data);
                            that.$store.commit("examList/initExamObj", data);
                            setTimeout(() => {
                                that.$store.commit("chatList/setTopChat", data);
                            }, 1000); //如果立即执行 setTopChat查不到该cid(data)的会话对象
                        } else {
                            // callback&&callback(is_succ)
                            Toast(that.$t('start_conversation_error'));
                        }
                    }
                );
            }
        },
        setGroupList(is_succ, list, fromLocal) {
            //设置群组列表
            if (is_succ) {
                if (fromLocal && this.$store.state.groupList.length > 0) {
                    return;
                }
                list.sort(function (a, b) {
                    //先排序把相同姓氏的好友排在一起
                    if (a.nickname > b.nickname) {
                        return 1;
                    } else {
                        return -1;
                    }
                });
                this.$store.commit("groupList/initGroupList", list);
            } else {
                Toast("setGroupList error");
            }
        },
        setGroupApplys(list) {
            //设置入群申请
            for (let item of list) {
                this.$store.commit("notifications/addGroupApply", item);
            }
        },
        dealFriendApplys(data) {
            //设置好友申请
            if (data.notify_type == "request_add_friend") {
                this.$store.commit("notifications/addFriendApply", data);
            } else if (data.notify_type == "response_add_friend") {
                if (data.param.accept) {
                    var str = this.$t('response_accept_friend').replace("${1}", data.param.nickname);
                } else {
                    var str = this.$t('response_disaccept_friend').replace("${1}", data.param.nickname);
                    this.$store.commit("relationship/removeApplyingList", data.param);
                }
                Toast(str);
            }
        },
        setFriendApplys(data) {
            let json = {};
            json.param = data.friendInfo;
            json.notify_id = data.applyInfo.id;
            json.description = data.applyInfo.msg_body.description;
            this.$store.commit("notifications/addFriendApply", json);
        },
        friendApplyResponse(data) {
            if (data.isAgree) {
                var str = this.$t('response_accept_friend').replace("${1}", data.userInfo.nickname);
            } else {
                var str = this.$t('response_disaccept_friend').replace("${1}", data.userInfo.nickname);
                this.$store.commit("relationship/removeApplyingList", data.userInfo);
            }
            Toast(str);
        },
        updateFriendInfo(data) {
            //更新好友信息
            this.$store.commit("friendList/updateFriendToFriendList", data);
            this.$store.commit("chatList/updateFriendToChatList", data);
            this.$store.commit("conversationList/updateFriendToConversationList", data);
            // this.$store.commit('conversationList/updateFriendToAttendeeList',data)
        },
        updateFriendDestroy(data) {
            this.updateFriendInfo(data);
            this.$store.commit("conversationList/updateFriendToAttendeeList", data);
        },
        onUpdateUserInfo(data) {
            //更新用户信息
            this.changeDefaultImg(data);
            this.$store.commit("user/updateUser", data);
            this.$store.commit("chatList/updateFriendToChatList", this.user);
            this.$store.commit("conversationList/updateFriendToConversationList", this.user);
        },
        onUpdateUserPortraitInfo(data) {
            //更新用户头像信息
            this.$store.commit("user/updateUser", {
                avatar: data.avatar,
                avatar_local: data.avatar_local,
            });
            this.$store.commit("conversationList/updateFriendToAttendeeList", {
                avatar: data.avatar,
                avatar_local: data.avatar_local,
                id: this.user.id,
                state: this.user.state,
                nickname: this.user.nickname,
            });
        },
        setConsultationImageList(is_succ, data) {
            //放置图像列表数据
            if (is_succ) {
                if (is_succ != "net_error") {
                    patientDesensitization(data.consultation_image_list);
                    parseImageListToLocal(data.consultation_image_list, "url");
                    this.$store.commit("consultationImageList/initConsultationImages", data);
                }
                this.$store.commit("loadingConfig/updateLoaded", {
                    key: "loadedFileList",
                    loaded: true,
                });
            } else {
                Toast("setConsultationImageList error");
            }
        },
        socketConnectSuccess(controller) {
            Logger.save({
                message:'socketConnectSuccess',
                eventType: `socket_event`,
                data: {
                    timestamp: new Date().toISOString(),
                    visibilityState: document.visibilityState,
                },
            });
            this.$store.commit("loadingConfig/updateLoaded", {
                key: "networkUnavailable",
                loaded: false,
            });
            if(this.resumeCheckTimeout){
                clearTimeout(this.resumeCheckTimeout);
                this.resumeCheckTimeout = null;
            }
            this.getAllTags();
            this.updateLiveCount();
            this.getDeviceNameById();
            this.getGroupSetList();
            this.getMultiCenterOptionList();
            this.getAiAnalyzeTypes();
            controller.emit("get_all_hospital_name", this.setAllHospital);
            controller.emit("get_user_info");
            controller.emit(
                "get_consultation_image_list",
                {
                    start: 0,
                    count: this.systemConfig.consultationImageShowNum,
                },
                this.setConsultationImageList
            );
            setTimeout(() => {
                resumeAllTasks();
            }, 2000);
            //通知Ulinker断开
            if (this.isUltraSoundMobile) {
                window.CWorkstationCommunicationMng.NotifyWebimPageState({ errorcode: 0, errormsg: "" });
                const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
                window.CWorkstationCommunicationMng.NotifyWebimSocketState({
                    errorcode: 0,
                    errormsg: "",
                });
            }
            this.getUnfinishedHomework(0);
            this.getUncorrectHomework(0);
            this.getCorrectedHomework(0);
        },
        socketError(e) {
           Logger.save({
                message:'socketError',
                eventType: `socket_event`,
                data: {e},
            });
            this.$store.commit("ultrasoundMachine/destroyMachine");
            this.$root.eventBus.$emit("exitMachinePage");
            this.$store.commit("loadingConfig/updateLoaded", {
                key: "networkUnavailable",
                loaded: true,
            });
            //重连需重新获取系统消息，需手动清除未处理系统消息
            this.$store.commit("notifications/clearFriendApply");
            this.$store.commit("notifications/clearGroupApply");
            //通知Ulinker断开

            if (this.isUltraSoundMobile) {
                const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
                window.CWorkstationCommunicationMng.NotifyWebimSocketState({
                    errorcode: -1,
                    errormsg: mobile_errors.CommonError.MAIN_SCREEN_SOCKET_DISCONNECT_ERROR,
                });
            }
            this.autoLogin((is_succ) => {
                if(!is_succ){
                    return
                }
                this.initNetworkData();
            });
        },
        socketReconnecting() {
            Logger.save({
                message:'socketReconnecting',
                eventType: `socket_event`,
                data: {
                    timestamp: new Date().toISOString(),
                    visibilityState: document.visibilityState
                },
            });
        },
        socketReconnect() {
            //重连需重新获取系统消息，需手动清除未处理系统消息
            // this.$store.commit("notifications/clearFriendApply");
            // this.$store.commit("notifications/clearGroupApply");
            Logger.save({
                message:'socketReconnect',
                eventType: `socket_event`,
                data: {
                    timestamp: new Date().toISOString(),
                    visibilityState: document.visibilityState
                },
            });

        },
        socketReconnectFail(e) {
            Logger.save({
                message:'socketReconnectFail',
                eventType: `socket_event`,
                data: {e},
            });
            this.$store.commit("ultrasoundMachine/destroyMachine");
            this.$root.eventBus.$emit("exitMachinePage");
            this.$store.commit("loadingConfig/updateLoaded", {
                key: "networkUnavailable",
                loaded: true,
            });
            //重连需重新获取系统消息，需手动清除未处理系统消息
            this.$store.commit("notifications/clearFriendApply");
            this.$store.commit("notifications/clearGroupApply");
            //通知Ulinker断开

            if (this.isUltraSoundMobile) {
                const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
                window.CWorkstationCommunicationMng.NotifyWebimSocketState({
                    errorcode: -1,
                    errormsg: mobile_errors.CommonError.MAIN_SCREEN_SOCKET_DISCONNECT_ERROR,
                });
            }
            this.autoLogin((is_succ) => {
                if(!is_succ){
                    return
                }
                this.initNetworkData();
            });
        },
        notifyException(data) {
            Tool.openMobileDialog({
                message: this.$t('exception_to_login_again'),
                showRejectButton: true,
                confirm: () => {
                    this.autoLogin((is_succ) => {
                        if(!is_succ){
                            return
                        }
                        this.initNetworkData();
                    });
                },
            });
        },
        socketDisconnect(e) {
            // 记录断开连接的详细信息
            Logger.save({
                message:'socketDisconnect',
                eventType: `socket_event`,
                data: {
                    reason: e,
                    timestamp: new Date().toISOString(),
                    visibilityState: document.visibilityState,
                    isUltraSoundMobile: this.isUltraSoundMobile
                },
            });

            this.$store.commit("ultrasoundMachine/destroyMachine");
            this.$root.eventBus.$emit("exitMachinePage");
            this.$store.commit("loadingConfig/updateLoaded", {
                key: "networkUnavailable",
                loaded: true,
            });
            //重连需重新获取系统消息，需手动清除未处理系统消息
            this.$store.commit("notifications/clearFriendApply");
            this.$store.commit("notifications/clearGroupApply");
            console.warn("socketDisconnect", e);
            //通知Ulinker断开

            if (this.isUltraSoundMobile) {
                const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
                window.CWorkstationCommunicationMng.NotifyWebimSocketState({
                    errorcode: -1,
                    errormsg: mobile_errors.CommonError.MAIN_SCREEN_SOCKET_DISCONNECT_ERROR,
                });
            }

            // 如果不是主动断开连接，则尝试重连
            // if (e !== "io client disconnect") {
            //     Logger.save({
            //         message:'socketDisconnect_autoReconnect',
            //         eventType: `socket_event`,
            //         data: { reason: e },
            //     });
            //     this.autoLogin((is_succ) => {
            //         if(!is_succ){
            //             return
            //         }
            //         this.initNetworkData();
            //     });
            // }
        },
        notifyAddFriend(friend) {
            this.$store.commit("friendList/addFriendList", friend);
            this.$store.commit("relationship/removeApplyingList", friend);
        },
        NotifyStartConversation(is_succ, conversation, start_type) {
            Logger.save({
                message:'NotifyStartConversation',
                eventType: `socket_event`,
                data: {conversation,is_succ,start_type},
            });
            if (is_succ) {
                //后台通知开启会话
                let cid = conversation.id;
                let existConversation = this.checkExitConversation(cid);
                if (existConversation) {
                    return;
                }
                let existChatListItem = this.checkExistChatListItem(cid);
                if (!existChatListItem) {
                    //不存在最近会话列表则加入
                    var chatItem = Object.assign({ cid: cid, message: {} }, conversation);
                    this.$store.commit("chatList/addChatList", chatItem);
                }
                conversation.start_type = start_type;
                conversation.socket = conversation.controller;
                delete conversation.controller;
                this.initConversationControllerEvent(conversation.socket, existChatListItem, () => {
                    this.$store.commit("conversationList/setConversation", conversation);
                    this.$store.commit("examList/initExamObj", cid);

                    if (0 == conversation.is_single_chat && conversation.service_type === 0) {
                        let groupTemp = {
                            id: conversation.id,
                            subject: conversation.subject,
                            is_single_chat: 0,
                        };
                        this.setDefaultImg([groupTemp]);
                        this.$store.commit("groupList/addGroup", groupTemp);
                    }

                    this.$root.eventBus.$emit("scrollChatWindow");
                    if (this.user.uid != conversation.creator_id) {
                        //非会话创建者，获取本地viewmode设置
                        let settingsObj = JSON.parse(
                            window.localStorage.getItem(`user_${this.user.uid}_viewmode`) || "{}"
                        );
                        let viewmode = settingsObj[`group_${cid}`];
                        if (viewmode != undefined) {
                            this.$store.commit("conversationList/updateViewMode", {
                                cid: conversation.id,
                                value: viewmode,
                            });
                        }
                    }

                    this.$root.eventBus.$emit("setPageType", cid);
                    if (this.$root.transmitQueue[cid]) {
                        //开启会话后有待发送的转发消息
                        this.sendTransmitMessage(cid);
                    }
                    if (this.$root.transferExamQueue[cid]) {
                        //开启会话后有待发送的一键转发文件
                        sendTransferExam(cid);
                    }
                    if (this.$root.transferLocalQueue[cid]) {
                        //开启会话后有带发送的本地文件
                        sendTransferLocal(cid);
                    }
                    if (this.$root.deleteQueue[cid]) {
                        //开启会话后有待删除的聊天消息
                        // this.deleteChatMessages();
                    }
                    if (conversation.is_single_chat == 0 && conversation.avatar === "") {
                        //判断是否有必要更新群头像
                        if (this.$root.updateGroupAvatarUserList[cid]) {
                            this.$root.updateGroupAvatarUserListcid = undefined;
                        }
                        this.$root.eventBus.$emit("createGroupAvatar", { conversation });
                    } else if (this.$root.updateGroupAvatarUserList[cid]) {
                        let item = this.$root.updateGroupAvatarUserList[cid];
                        if (item.id == conversation.id) {
                            this.$root.eventBus.$emit("createGroupAvatar", { conversation });
                            this.$root.updateGroupAvatarUserList[cid] = undefined;
                        }
                    }
                    // updateResoureceReview(cid);
                    this.getIworksList(conversation);
                    this.$root.eventBus.$emit("refreshConversationSuccessToChatWindow", cid);
                    if (start_type.type === this.systemConfig.start_type.KickoutAttendee && start_type.kickout_data) {
                        this.kickoutAttendeeHandle(start_type.kickout_data, conversation.socket);
                    }
                    if (this.$checkPermission({
                        conversationPermissionKey:'member.get_apply_count'
                    },{conversationId:conversation.id})) {
                        this.getApplyCount(conversation.id);
                    }
                    this.getConverSationGalleryData(conversation.socket, existChatListItem);
                    this.getUnfinishedHomework(conversation.id);
                    this.getUncorrectHomework(conversation.id);
                });
            } else {
                this.$root.eventBus.$emit("createConversationFail");
            }
        },
        notifyLoginAnother() {
            Logger.save({
                message:'notifyLoginAnother',
                eventType: `socket_event`,
                data: {},
            });
            destroyAllConference();
            backToIndex();
            window.localStorage.setItem("password", "");
            window.localStorage.setItem("loginToken", "");
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            setTimeout(() => {
                this.clearAndDirectToLogin();
                    Tool.openMobileDialog({
                    message: this.$t('login_another'),
                    closeOnPopstate: false,
                });
            }, 1000);
        },
        notifyUserDestroy() {
            Logger.save({
                message:'notifyUserDestroy',
                eventType: `socket_event`,
                data: {},
            });
            destroyAllConference();
            backToIndex();
            window.localStorage.setItem("account", "");
            window.localStorage.setItem("password", "");
            window.localStorage.setItem("loginToken", "");
            window.localStorage.setItem("uid", "");
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            setTimeout(() => {
                Tool.openMobileDialog({
                message: this.$t('account_destroy_tip'),
                closeOnPopstate: false,
            });
                this.clearAndDirectToLogin();
            }, 1000);
        },
        checkExistChatListItem(cid) {
            let exit = false;
            for (let chat of this.chatList) {
                if (chat.type == 3) {
                    for (let member of chat.list) {
                        if (member.cid == cid) {
                            exit = true;
                            break;
                        }
                    }
                } else if (chat.cid == cid) {
                    exit = true;
                    break;
                }
            }
            return exit;
        },
        getConverSationGalleryData(controller, exist) {
            return new Promise((resolve, reject) => {
                let conversationGalleryData = this.$store.state.conversationList[controller.cid].galleryObj;
                if (conversationGalleryData.hasOwnProperty("cid")) {
                    resolve(conversationGalleryData.gallery_list);
                } else {
                    window.main_screen.conversation_list[controller.cid].getResourceList(
                        {
                            limit: this.systemConfig.consultationImageShowNum,
                            type: "all",
                        },
                        (res) => {
                            if (res.error_code === 0) {
                                const data = {
                                    cid: controller.cid,
                                    gallery_list: res.data,
                                };
                                this.setGalleryList(data, exist);
                                resolve(res.data);
                            } else {
                                // Toast('getResourceList error 2')
                                console.error("getResourceList error 2");
                                reject(false);
                            }
                        }
                    );
                }
            });
        },
        initConversationControllerEvent(controller, exist, callback) {
            var that = this;
            controller.on("gateway_connect", function (e) {
                callback && callback();
            });
            controller.on("joined_exam_consultations", function (is_succ, data) {
                if (is_succ) {
                    that.$store.commit("conversationList/setJoinedExamConsultations", {
                        cid: controller.cid,
                        list: data,
                    });
                }
            });
            controller.on("gallery_messages_detail_info", function (is_succ, data) {
                that.setGalleryMessageDetail(is_succ, {
                    cid: controller.cid,
                    list: data.list,
                    iworks_protocol_list: data.iworks_protocol_list,
                });
            });
            controller.on("history_chat_messages", async (is_succ, data, scroll) => {
                that.setHistoryChatMessage(is_succ, data, scroll, controller.cid);
            });
            controller.on("other_say", function (messageList) {
                if (messageList && messageList.length > 0) {
                    that.setSayChatMessage(messageList, controller.cid);
                    that.debounceSortChatList();
                }
            });
            controller.on("update_messages", function (messageList) {
                that.updateChatMessage(messageList, controller.cid);
            });
            controller.on("update_file_transmit_progress", function (data) {
                data.cid = controller.cid;
                that.updateUploadProgress(data);
            });
            controller.on("update_conversation_attendee_state", function (data) {
                //更新群成员信息
                that.$store.commit("conversationList/updateFriendToAttendeeList", data);
            });
            controller.on("notify_add_attendee", function (data) {
                that.setAddAttendee(data);
                const conversation = that.conversationList[data.cid];
            });
            controller.on("notify_delete_attendee", function (data) {
                that.kickoutAttendeeHandle(data, controller);
            });
            controller.on("notify_edit_subject", function (is_succ, data) {
                that.setNewSubject(is_succ, data, controller.cid);
            });
            controller.on("notify_add_tag", function (data) {
                that.$store.commit("gallery/addTagList", data);
            });
            controller.on("notify_del_tag", function (data) {
                that.$store.commit("gallery/deleteTagList", data);
            });
            controller.on("notify_add_comment", function (data) {
                that.$store.commit("gallery/addCommentToList", data);
            });
            controller.on("notify_edit_public", function (is_succ, data) {
                if (is_succ) {
                    data.cid = controller.cid;
                    that.$store.commit("conversationList/updateIsPublic", data);

                    if (window.vm.$store.state.systemConfig.groupPublicState.SemiPublic != data.is_public) {
                        that.$store.commit("conversationList/updateAttendeeTempToFormal", {
                            cid: data.cid,
                            uid: that.user.id,
                        });
                    }
                }
            });
            controller.on("notify_edit_view_mode", function (is_succ, data) {
                if (is_succ) {
                    let settingsObj = JSON.parse(window.localStorage.getItem(`user_${that.user.uid}_viewmode`) || "{}");
                    let viewmode = settingsObj[`group_${controller.cid}`];
                    if (viewmode == undefined) {
                        data.cid = controller.cid;
                        data.value = data.view_mode;
                        that.$store.commit("conversationList/updateViewMode", data);
                    }
                }
            });
            controller.on("notify_edit_record_mode", function (is_succ, data) {
                if (is_succ) {
                    if (Number(window.vm.$root.currentLiveCid) !== Number(controller.cid)) {
                        //直播中不接受变更的录制内容
                        data.cid = controller.cid;
                        data.record_mode = data.record_mode;
                        that.$store.commit("conversationList/updateIsLiveRecord", data);
                    }
                }
            });
            controller.on("notify_set_admin", function (is_succ, data) {
                if (is_succ) {
                    data.cid = controller.cid;
                    that.$store.commit("conversationList/updateGroupOwnerId", data);
                }
            });
            controller.on("notify_edit_announcement", function (is_succ, data) {
                console.log("[event] notify_edit_announcement", is_succ, data);
                data.cid = controller.cid;
                that.$store.commit("conversationList/updateAnnounce", data);
            });
            controller.on("notify_delete_chat_messages", function (is_succ, list) {
                if (is_succ) {
                    for (let i in list) {
                        let item = list[i];
                        that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                            cid: controller.cid,
                            gmsg_id_list: item.gmsg_id_list,
                        });
                        that.$root.eventBus.$emit('notifyDeleteChatMessages',{
                            cid: controller.cid,
                            gmsg_id_list:item.gmsg_id_list
                        })
                        let resource_id_list = item.resource_id_list;
                        if (resource_id_list) {
                            for (let i in resource_id_list) {
                                let resource_id = resource_id_list[i];
                                that.$store.commit("conversationList/deleteFileToConversation", {
                                    cid: controller.cid,
                                    resource_id: resource_id,
                                });
                                that.$store.commit("consultationImageList/deleteFileToConsultationImages", {
                                    cid: controller.cid,
                                    resource_id: resource_id,
                                });
                                that.$root.eventBus.$emit("deleteFileToExamList", {
                                    cid: controller.cid,
                                    resource_id: resource_id,
                                });
                                that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                                    resource_id,
                                    data: {
                                        state: 0, //被删除
                                    },
                                });
                            }
                        }
                    }
                }
            });
            controller.on("notify_withdraw_chat_message", function (is_succ, data) {
                if (is_succ) {
                    that.$store.commit("conversationList/withDrawChatMessagesByGmsgIdList", {
                        cid: controller.cid,
                        gmsg_id_list: data.gmsg_id_list,
                    });
                    that.$root.eventBus.$emit('notifyWithdrawChatMessage',{
                        cid: controller.cid,
                        gmsg_id_list:data.gmsg_id_list
                    })
                    let resource_id_list = data.resource_id_list;
                    if (resource_id_list) {
                        for (let i in resource_id_list) {
                            let resource_id = resource_id_list[i];
                            that.$store.commit("conversationList/deleteFileToConversation", {
                                cid: controller.cid,
                                resource_id: resource_id,
                            });
                            that.$store.commit("consultationImageList/deleteFileToConsultationImages", {
                                cid: controller.cid,
                                resource_id: resource_id,
                            });
                            that.$root.eventBus.$emit("deleteFileToExamList", {
                                cid: controller.cid,
                                resource_id: resource_id,
                            });
                            that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                                resource_id,
                                data: {
                                    state: 2, //被撤回
                                },
                            });
                        }
                    }
                }
            });
            //会话--分析结果
            controller.on("update_ai_analyze_report", (is_succ, data) => {
                data.cid = controller.cid;
                that.updateAiAnalyzeReport(is_succ, data);
            });
            controller.on("notify_add_conference_plan", (data) => {
                this.$store.commit("conversationList/addConferencePlan", data);
            });
            controller.on("notify_del_conference_plan", (data) => {
                this.$store.commit("conversationList/delConferencePlan", data);
            });
            // controller.on('notify_add_exam_consultation_comment',(is_succ,data)=>{
            //     this.$store.commit('examList/addExamComment',{
            //         cid:controller.cid,
            //         exam_id:data.exam_id,
            //         comment:data
            //     })
            // });
            controller.on("notify_set_attendee_preferences", (data) => {
                data.cid = controller.cid;
                this.$store.commit("conversationList/updateMuteToConversation", data);
                this.$store.commit("chatList/updateMuteToChatList", data);
            });

            controller.on("notify_delete_exams", function (is_succ, data) {});

            controller.on("notify_exception", function (data) {
                that.notifyException(data);
            });

            controller.on("notify_update_group_portrait", function (data) {
                console.log("notify_update_group_portrait", data);
                let avatar_local = data.avatar_local;
                that.$store.commit("chatList/updateChatAvatarLocalUrl", {
                    imgObj: {
                        cid: controller.cid,
                    },
                    avatar_local: avatar_local,
                    avatar: data.avatar,
                });
                that.$store.commit("groupList/updateAvatarToGroupList", {
                    cid: controller.cid,
                    avatar_local: avatar_local,
                    avatar: data.avatar,
                });
                that.$store.commit("conversationList/updateConversationAvatar", {
                    cid: controller.cid,
                    avatar_local: avatar_local,
                    avatar: data.avatar,
                });
                that.$store.commit("groupset/updateAvatarToGroupsetlist", {
                    cid: controller.cid,
                    avatar: data.avatar,
                });
            });

            controller.on("notify_update_resource_des", (data) => {
                console.log("updateResourceDes ", data);
                this.$store.commit("conversationList/updateResourceDes", data);
                this.$store.commit("consultationImageList/updateImageDes", data);
                this.$store.commit("userFavorites/updateFavorites", data);
            });
            //开启会话后，接受消息通知
            controller.on("receive_group_message", (data) => {
                console.log("receive_group_message,conversation", data);
                that.setSayChatMessageReceiveGroupMessage(data, true);
                if (
                    data.msg_type === that.systemConfig.msg_type.LIVE_INVITE ||
                    data.groupInfo.service_type === that.systemConfig.ServiceConfig.type.LiveBroadCast
                ) {
                    that.debounceUpdateLiveCount();
                }
                that.debounceSortChatList();
            });
            controller.on("attendeesUpdate", (data) => {
                this.$store.commit("conversationList/updateAttendeeRole", {
                    cid: controller.cid,
                    attendeeList: data,
                });
            });
            controller.on("notify_user_apply_join_group", (data) => {
                if (this.$checkPermission({
                    conversationPermissionKey:'member.get_apply_count'
                },{conversationId:controller.cid})) {
                    let applyCount = this.conversationList[controller.cid].applyCount;
                    this.$store.commit("conversationList/updateConversation", {
                        cid: controller.cid,
                        key: "applyCount",
                        value: applyCount + 1,
                    });
                }
            });
            controller.on("attendeesUpdateAliasName", (data) => {
                this.$store.commit("conversationList/updateAttendeeAliasName", {
                    uid: data.uid,
                    cid: controller.cid,
                    aliasName: data.alias_name,
                });
            });
            controller.on("resourceSetName", (data) => {
                that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                    resource_id: data.resource_id,
                    data: {
                        custom_file_name: data.custom_file_name,
                    },
                });
            });
        },
        setHistoryChatMessage(is_succ, data, scroll, cid) {
            if (is_succ) {
                patientDesensitization(data);
                parseImageListToLocal(data, "url");
                setExpirationResource(data, cid);
                setWithDrawData(data);
                for (let message of data) {
                    message.original_msg_body = message.msg_body
                    message.msg_body = this.parseMessageBody(message.msg_body)
                    message.patientInfo = transferPatientInfo(message);
                    message.sending = false;
                    message.downloading = false;
                    message.sendFail = message.sendFail || false;
                    if (message.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                        parseImageListToLocal(message.ai_analyze && message.ai_analyze.messages, "url");
                    }
                    if (message.protocol_guid) {
                        //消息存在iworks信息
                        setIworksInfoToMsg(message);
                    }
                }
                var type = "prepend";
                if (scroll && scroll.type == "bottom") {
                    type = "splice";
                }
                var is_localdb_msg = 0;
                if (scroll && scroll.type == "top_local") {
                    is_localdb_msg = 1;
                }
                let obj = {
                    list: data,
                    cid: cid,
                    type: type,
                    is_localdb_msg: is_localdb_msg,
                };

                if (data[0] && data[0].been_withdrawn == 2) {
                    // 被撤回的 更改msg_type
                    data[0].msg_type = this.systemConfig.msg_type.WITHDRAW;
                }
                this.$store.commit("chatList/addMessageNoSort", data[0]);
                this.$store.commit("conversationList/setChatMessage", obj);
                //聊天界面获取历史消息也走这个方法

                if (
                    0 < data.length &&
                    scroll &&
                    "bottom" == scroll.type &&
                    this.$store.state.conversationList &&
                    this.$store.state.conversationList[cid] &&
                    this.$store.state.conversationList[cid].start_type &&
                    this.systemConfig.start_type.NewChatMessage ==
                        this.$store.state.conversationList[cid].start_type.type
                ) {
                    this.$store.commit("chatList/addMessageNoSort", data[0]);
                }

                this.$root.eventBus.$emit("historyLoaded", obj);
                this.$store.commit("conversationList/updateMessageListIsLoaded", { cid, is_loaded_history_list: true });
            } else {
                if (window.main_screen.gateway.check) {
                    Toast("setHistoryChatMessage error");
                } else {
                    this.$root.eventBus.$emit("historyLoaded", { list: [], cid });
                    this.$store.commit("conversationList/updateMessageListIsLoaded", {
                        cid,
                        is_loaded_history_list: true,
                    });
                }
            }
        },
        setGalleryList(data, exist) {
            patientDesensitization(data.gallery_list);
            parseImageListToLocal(data.gallery_list, "url");
            if (data.gallery_list.length < this.systemConfig.consultationImageShowNum) {
                data.gallery_index = -1;
            }
            this.$store.commit("conversationList/initGalleryObj", data);
            // let conversation = this.$store.state.conversationList[data.cid];
            // if (!exist) {
            //     if (conversation.service_type === 104) {
            //         //不处理预约直播的群文件
            //         return;
            //     }
            //     //新入群时，将群内图片放入图像列表
            //     for (let message of data.gallery_list) {
            //         this.$store.commit("consultationImageList/addFileToConsultationImages", message);
            //     }
            // }
        },
        setGalleryMessageDetail(is_succ, data) {
            console.log("setGalleryMessageDetail----------------");
            if (is_succ) {
                //将评论信息、iworks协议放入画廊中
                for (let key in data.iworks_protocol_list) {
                    let protocol = data.iworks_protocol_list[key];
                    protocol.protocolTree = [this.setProtocolTree(protocol)];
                    protocol.viewList = this.setViewList(protocol.protocolTree[0], []);
                }
                this.$store.commit("gallery/setCommentToGallery", data);
            } else {
                Toast("setGalleryMessageDetail error");
            }
        },
        judgeIfNeedAddChatList(omessage, isTop = true) {
            if (!omessage) {
                return;
            }
            let cid = omessage.group_id || omessage.groupInfo.id;
            if (this.conversationList[cid] && !this.conversationList[cid].preferences.is_mute) {
                this.messageNotify(cid);
                this.notifying = true;
            }
            let is_exist_chat_List = this.checkExistChatListItem(cid);
            let message = {
                ...omessage,
                downloading: false,
                gmsg_id: omessage.gmsg_id,
                group_id: cid,
                msg_body: "",
                msg_type: omessage.msg_type,
                sendFail: false,
                send_ts: omessage.send_ts,
                sender_id: omessage.sender_id,
                sending: false,
                liveInfo: omessage.liveInfo,
                groupInfo: omessage.groupInfo,
                ai_result: omessage.ai_result,
                avatar: omessage.senderInfo && omessage.senderInfo.avatar,
                nickname: omessage.senderInfo && omessage.senderInfo.nickname,
                sex: omessage.senderInfo && omessage.senderInfo.sex,
                fid: omessage.senderInfo && omessage.senderInfo.id,
            };
            if (omessage.groupInfo.type === 2) {
                // 群聊
                message = { ...omessage.groupInfo, ...message };
            }
            parseImageListToLocal([message], "url");
            patientDesensitization([message]);
            if (!is_exist_chat_List) {
                //不存在最近会话列表则加入
                let is_single_chat = omessage.groupInfo.type == 1 ? 1 : 0;
                var chatItem = {
                    cid: cid,
                    is_single_chat,
                    subject: message.groupInfo.subject,
                    type: message.groupInfo.type,
                    state: 1,
                    avatar: is_single_chat ? message.avatar : message.groupInfo.avatar,
                    message: {},
                    service_type: message.groupInfo.service_type || 0,
                    nickname: message.nickname || "",
                };
                if (is_single_chat) {
                    chatItem.fid = message.fid;
                    chatItem.sex = message.sex;
                }
                if (isTop) {
                    this.$store.commit("chatList/addAndTopChat", chatItem);
                } else {
                    this.$store.commit("chatList/addChatList", chatItem);
                }
            } else {
                if (isTop) {
                    this.$store.commit("chatList/setTopChat", cid);
                }
            }
            return message;
        },
        PreHandleChatMessageByGroupMessage(omessage) {
            console.log("PreHandleChatMessageByGroupMessage", omessage);
            let message = this.judgeIfNeedAddChatList(omessage);
            let cid = message.group_id;
            message.original_msg_body = message.msg_body
            message.msg_body = this.parseMessageBody(message.msg_body);
            if (message.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                if (message.ai_analyze && message.ai_analyze.messages) {
                    parseImageListToLocal(message.ai_analyze.messages, "url");
                    let ignoreConsultationImages = false;
                    if (
                        this.conversationList[cid] &&
                        this.systemConfig.msg_type.AI_ANALYZE == this.conversationList[cid].service_type
                    ) {
                        //AI分析图片不放入总图像列表里
                        ignoreConsultationImages = true;
                    }
                    for (let item of message.ai_analyze.messages) {
                        let obj = Object.assign({}, item, true);
                        pushImageToList(obj, ignoreConsultationImages);
                    }
                }
            }
            if (message.msg_type == this.systemConfig.msg_type.IWORKS_PROTOCOL) {
                this.pushIworks(message, cid);
            }
            if (message.protocol_guid) {
                //消息存在iworks信息
                // if (message.iworks_protocol) {
                //     this.setIworksProtocol(message.iworks_protocol)
                // }
                // if (message.iworks_protocol_execution) {
                //     this.setIworksProtocol(message.iworks_protocol_execution)
                // }
                setIworksInfoToMsg(message);
            }
            this.$store.commit("chatList/addMessageNoSort", message);
            if (message.msg_type == this.systemConfig.msg_type.Text) {
                if (Array.isArray(message.mentionList)) {
                    if (message.mentionList.includes(this.user.uid) || message.mentionList.includes("all")) {
                        this.$store.commit("chatList/setMention", message);
                        this.messageNotify(cid);
                        this.notifying = true;
                    }
                }
            }
            if (!this.isResource(message.msg_type)) {
                this.$store.commit("chatList/addUnread", {
                    group_id: message.group_id,
                });
            }
            this.$root.eventBus.$emit("updateExamImageListIfNeed", message);
            pushImageToList(message);
            this.notifying = false;
            let obj = {
                list: [message],
                cid: cid,
                type: "append",
                is_localdb_msg: 0,
            };
            return obj;
        },
        setSayChatMessageReceiveGroupMessage(data, is_open_conversation) {
            if (is_open_conversation) {
                const res = this.PreHandleChatMessageByGroupMessage(data);
                if (this.conversationList.hasOwnProperty(data.group_id)) {
                    if(window.vm.$store.state.conversationList[data.group_id].is_loaded_history_list){
                        this.$store.commit("conversationList/setChatMessage", res);
                    }

                }
            } else {
                this.PreHandleChatMessageByGroupMessage(data);
            }
        },
        setSayChatMessage(omessageList, cid) {
            const messageList = this.checkMessageListDuplicate(omessageList, cid);
            // const messageList = omessageList;
            if (messageList.length) {
                if (this.conversationList[cid] && !this.conversationList[cid].preferences.is_mute) {
                    this.messageNotify(cid);
                    this.notifying = true;
                }
            }
            for (let message of messageList) {
                this.$store.commit("chatList/addUnread", {
                    group_id: cid,
                });
                message.original_msg_body = message.msg_body
                message.msg_body = this.parseMessageBody(message.msg_body);
                message.msg_body = message.msg_body.replace(/\n/g, "<br/>");
                message.sending = false;
                message.sendFail = false;
                message.downloading = false;
                parseImageListToLocal([message], "url");
                patientDesensitization([message]);
                message.patientInfo = transferPatientInfo(message);
                if (message.liveInfo) {
                    message.nickname = message.liveInfo.creator_name;
                }
                if (message.sender_nickname) {
                    message.nickname = message.sender_nickname.nickname;
                }
                const attendee = this.conversationList[cid]?.attendeeList['attendee_'+message.sender_id]
                if(attendee){
                    message.nickname = (attendee.alias_name ? attendee.alias_name : attendee.nickname) || message.nickname
                }
                message.nickname = (attendee.alias_name ? attendee.alias_name : attendee.nickname) || message.nickname
                if (message.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                    if (message.ai_analyze && message.ai_analyze.messages) {
                        parseImageListToLocal(message.ai_analyze.messages, "url");
                        let ignoreConsultationImages = false;
                        if (
                            this.conversationList[cid] &&
                            this.systemConfig.msg_type.AI_ANALYZE == this.conversationList[cid].service_type
                        ) {
                            //AI分析图片不放入总图像列表里
                            //AI分析图片放入总图像列表里
                            // ignoreConsultationImages=true
                        }
                        for (let item of message.ai_analyze.messages) {
                            let obj = Object.assign({}, item, true);
                            pushImageToList(obj, ignoreConsultationImages);
                        }
                    }
                }
                if (message.msg_type == this.systemConfig.msg_type.IWORKS_PROTOCOL) {
                    this.pushIworks(message, cid);
                }

                if (message.protocol_guid) {
                    //消息存在iworks信息
                    // if (message.iworks_protocol) {
                    //     this.setIworksProtocol(message.iworks_protocol)
                    // }
                    // if (message.iworks_protocol_execution) {
                    //     this.setIworksProtocol(message.iworks_protocol_execution)
                    // }
                    setIworksInfoToMsg(message);
                }
                this.$store.commit("chatList/addMessageNoSort", message);
                if (message.msg_type == this.systemConfig.msg_type.Text) {
                    if (Array.isArray(message.mentionList)) {
                        if (message.mentionList.includes(this.user.uid) || message.mentionList.includes("all")) {
                            this.$store.commit("chatList/setMention", message);
                            this.messageNotify(cid);
                            this.notifying = true;
                        }
                    }
                }
                this.$root.eventBus.$emit("updateExamImageListIfNeed", message);
                pushImageToList(message);
                // console.error(JSON.parse(JSON.stringify(message)),'message')
            }

            this.notifying = false;
            if(window.vm.$store.state.conversationList[cid].is_loaded_history_list){
                let obj = {
                    list: messageList,
                    cid: cid,
                    type: "append",
                    is_localdb_msg: 0,
                };
                console.log("############setChatMessage2");
                if (this.conversationList.hasOwnProperty(cid)) {
                    this.$store.commit("conversationList/setChatMessage", obj);
                }
            }


            // this.$root.eventBus.$emit('scrollChatWindow')
        },
        checkMessageListDuplicate(messageList, cid) {
            // if(messageList&&messageList.length>0){
            //     let list = []
            //     let lastMessage=this.$store.state.chatList.lastMessage[cid]
            //     messageList.forEach((item)=>{
            //         if(lastMessage&&lastMessage.gmsg_id){
            //             if(item.gmsg_id>lastMessage.gmsg_id){
            //                 list.push(item)
            //             }
            //         }else{
            //             let lastConversationList = this.$store.state.conversationList[cid]&&this.$store.state.conversationList[cid].chatMessageList
            //             let lastConversationListMessage = lastConversationList&&lastConversationList.length>0&&lastConversationList[lastConversationList.length-1]
            //             if(lastConversationList&&lastConversationList.length === 0){
            //                 list.push(item)
            //             }else if(lastConversationListMessage&&lastConversationListMessage.gmsg_id&&(item.gmsg_id>lastConversationListMessage.gmsg_id)) {
            //                 list.push(item)
            //             }
            //         }
            //     })
            //     return list
            // }
            if (!this.conversationList[cid]) {
                return messageList;
            }
            let chatMessageList = this.conversationList[cid].chatMessageList;
            let newMessageList = [];
            for (let message of messageList) {
                if (message.gmsg_id && message.gmsg_id != 0) {
                    let isDuplicate = chatMessageList.some((chatMessage) => message.gmsg_id == chatMessage.gmsg_id);
                    if (isDuplicate) {
                        console.log("say ignore message %%%%%%%%%%");
                        continue;
                    }
                }
                newMessageList.push(message);
            }
            return newMessageList;
        },
        updateChatMessage(messageList, cid) {
            for (let message of messageList) {
                parseImageListToLocal([message], "url");
                this.$store.commit("conversationList/updateChatMessage", message);
                this.$store.commit("conversationList/updateConversationImage", message);
                this.$store.commit("consultationImageList/updateConsultationImage", message);
                this.$root.eventBus.$emit("updateExamImageListIfNeed", message);
            }
        },
        autoLogin(func) {
            Logger.save({
                message:'auto login',
                eventType: `auto_login`,
                data: {},
            });
            var that = this;
            if (this.isAutoLogging) {
                console.log("repeat auto login reject");
                Logger.save({
                    message:'repeat auto login reject',
                    eventType: `auto_login`,
                    data: {},
                });
                func && func(false);
                return;
            }
            this.hasTriedAutoLoginAfterResume = true;
            if(this.resumeCheckTimeout){
                clearTimeout(this.resumeCheckTimeout);
                this.resumeCheckTimeout = null;
            }
            this.isAutoLogging = true;
            this.unBindControllerEvent();
            if (window.main_screen && window.main_screen.gateway) {
                window.main_screen.CloseSocket();
            }

            const storeToken = this.user.new_token;
            const loginToken = window.localStorage.getItem("loginToken") || storeToken || "";
            if (this.autoLoginTimeout) {
                clearTimeout(this.autoLoginTimeout);
                this.autoLoginTimeout = null;
            }
            if (loginToken === "") {
                this.clearAndDirectToLogin();
                func && func(false);
            } else {
                console.log("autoLogin--", new Date().toLocaleString());
                // let ajaxServer=this.systemConfig.server_type.protocol+this.systemConfig.server_type.host+this.systemConfig.server_type.port
                service
                    .loginByToken({
                        token: loginToken,
                        deviceInfo: {
                            device_id: this.deviceInfo.device_id,
                            client_type: this.systemConfig.clientType,
                        },
                    })
                    .then(
                        (res) => {
                            if (res.data.error_code === 0) {
                                const user = res.data.data;
                                this.$store.commit("loadingConfig/updateLoaded", {
                                    key: "networkUnavailable",
                                    loaded: false,
                                });
                                //获取localUrl需要
                                this.$store.commit("user/updateUser", {
                                    uid: user.uid,
                                });
                                // user.login_name = account;
                                handleAfterLogin(user);
                                func && func(true);
                            } else {
                                this.isAutoLogging = false;
                                if (res.data.key === "userTokenError" || res.data.key === "userChangePwdNeedLogin") {
                                    console.error("autologin",res.data.key)
                                    window.localStorage.setItem("loginToken", "");
                                    func && func(false);
                                    this.clearAndDirectToLogin();
                                } else if (res.data.key == "userOutOfTrail") {
                                    //试用期到跳转到输入推荐码界面
                                    window.localStorage.setItem("loginToken", "");
                                    func && func(false);
                                    this.clearAndDirectToLogin();
                                    setTimeout(() => {
                                        this.$router.push(
                                            `/login/referral_code?token=${loginToken}&isShowBack=1&isAutoLogin=1`
                                        );
                                    }, 300);
                                } else {
                                    if (!that.hasDisconnectTip) {
                                        Toast(this.$t(res.data.key));
                                        that.hasDisconnectTip = true;
                                        that.$store.commit("loadingConfig/updateLoaded", {
                                            key: "networkUnavailable",
                                            loaded: true,
                                        });
                                    }
                                    func && func(false);
                                    this.autoLoginTimeout = setTimeout(function () {
                                        that.autoLogin(func);
                                    }, 5000);
                                }
                                // this.$router.replace('/login')
                            }
                        },
                        (res) => {
                            this.isAutoLogging = false;
                            let message = res;
                            if (res.message == "Network Error") {
                                message = that.$t('network_error_tip');
                            }
                            if (!that.hasDisconnectTip) {
                                Toast(message);
                                that.hasDisconnectTip = true;
                                that.$store.commit("loadingConfig/updateLoaded", {
                                    key: "networkUnavailable",
                                    loaded: true,
                                });
                            }
                            func && func(false);
                            this.autoLoginTimeout = setTimeout(function () {
                                that.autoLogin(func);
                            }, 5000);
                            // this.$router.replace('/login')
                        }
                    );
            }
        },
        setAddAttendee(attendee) {
            this.$store.commit("conversationList/addAttendee", attendee);
        },

        setNewSubject(is_succ, subject, cid) {
            this.$store.commit("conversationList/updateSubjectToConversation", {
                subject: subject,
                cid: cid,
            });
            this.$store.commit("chatList/updateSubjectToChatList", {
                subject: subject,
                cid: cid,
            });
            this.$store.commit("groupList/updateSubjectToGroupList", {
                subject: subject,
                cid: cid,
            });
        },
        slideChange() {
            let index = this.$refs.mySwiper.swiper.activeIndex;
            /* eslint-disable  */
            switch (index) {
                case 0:
                    this.active = "chat";
                    break;
                case 1:
                    this.active = "contacts";
                    break;
                case 2:
                    this.active = "discover";
                    break;
                case 3:
                    this.active = "mine";
                    break;
            }
            /* eslint-disable  */
        },
        notifyDeviceEvent(data) {
            var that = this;
            var eventName = data.event_name;
            if (1 != data.isAnonymous) {
                data.isAnonymous = 0;
            }
            if (eventName == "notify_device_info") {
                if (-1 == data.device_id) {
                    // no matching device login
                    console.log("not find device");
                    //that.$store.commit('deviceCtrl/updateDeviceCtrl',{isConnecting: false, client_type: 0,first_init:true});
                    that.$store.commit("deviceCtrl/clearDeviceCtrl");
                    closeDeviceWindowIfNeed();
                } else {
                    that.$store.commit("deviceCtrl/updateDeviceCtrl", {
                        device_id: data.device_id,
                        isConnecting: true,
                        client_type: data.client_type,
                        name: data.name,
                        // cur_session_id: data.cur_session_id,
                        // cur_session_type: data.cur_session_type,
                        // isAnonymous: data.ftp_isAnonymous,
                        // ftp_account: data.ftp_account,
                        // ftp_password: data.ftp_password,
                        // ftp_path: data.ftp_path,
                        // ftp_port: data.ftp_port,
                    });
                }
            } else if (eventName == "notify_device_offline") {
                Toast(that.$t('device_offline'));
                //that.$store.commit('deviceCtrl/updateDeviceCtrl',{isConnecting: false, client_type: 0,first_init:true});
                that.$store.commit("deviceCtrl/deleteDeviceCtrl", data.device_id);
                if (0 == Tool.getHsonLength(that.$store.state.deviceCtrl)) {
                    closeDeviceWindowIfNeed();
                }
            } else if (eventName == "notify_update_device_cur_session") {
                if (data.error == 0) {
                    that.$store.commit("deviceCtrl/updateDeviceCtrl", {
                        device_id: data.device_id,
                        cur_session_id: data.cur_session_id,
                        cur_session_type: data.cur_session_type,
                    });
                    window.vm.$root.eventBus.$emit("notifyUpdateDeviceCurSession", data);
                } else {
                    Toast(that.$t('update_device_cur_session_fail'));
                }
            } else if (eventName == "notify_update_device_patient_info") {
                that.$store.commit("deviceCtrl/updateDeviceCtrl", {
                    device_id: data.device_id,
                    patient_id: data.patient_id,
                    patient_name: data.patient_name,
                });
                window.vm.$root.eventBus.$emit("notifyUpdateDevicePatientInfo", data);
            } else if (eventName == "notify_update_device_ftp_info") {
                var ftp_account = data.ftp_account ? data.ftp_account : "";
                var ftp_password = data.ftp_password ? data.ftp_password : "";
                var ftp_path = data.ftp_path ? data.ftp_path : "";
                var ftp_port = data.ftp_port ? data.ftp_port : "";
                var isAnonymous = data.isAnonymous ? data.isAnonymous : false;
                that.$store.commit("deviceCtrl/updateDeviceCtrl", {
                    device_id: data.device_id,
                    ftp_account: ftp_account,
                    ftp_password: ftp_password,
                    ftp_path: ftp_path,
                    ftp_port: ftp_port,
                    isAnonymous: isAnonymous,
                });
                window.vm.$root.eventBus.$emit("notifyUpdateDeviceFtpInfo", data);
            } else if (eventName == "notify_device_save_single_frame") {
                if (data.error == 0) {
                    Toast(that.$t('save_single_frame_succ'));
                } else {
                    Toast(that.$t('save_single_frame_fail') + ": " + data.error_info);
                }
                window.vm.$root.eventBus.$emit("notifyDeviceSaveSingleFrame", data);
            } else if (eventName == "notify_device_save_multi_frame") {
                window.vm.$root.eventBus.$emit("notifyDeviceSaveMultiFrame", data);
            } else if (eventName == "notify_device_shutdown") {
                if (data.error == 0) {
                    Toast(that.$t('shutdown_succ'));
                } else {
                    Toast(that.$t('shutdown_fail') + ": " + data.error_info);
                }
                window.vm.$root.eventBus.$emit("notifyDeviceShutdown", data);
            } else if (eventName == "notify_patient_file_list") {
                if (data.error == 0) {
                    for (let file of data.file_list) {
                        file.url = file.file_path;
                        file.loaded = true;
                        if (/\.mp4$/.test(file.file_path)) {
                            file.msg_type = 4;
                            file.mainVideoSrc = file.url;
                            file.realUrl = "static/resource/images/poster_video.png";
                        } else {
                            file.msg_type = 3;
                            file.realUrl = getLocalImgUrl(file.url);
                        }
                    }
                    window.vm.$store.commit("deviceCtrl/updateDeviceCtrl", {
                        device_id: data.device_id,
                        saved_list: data.file_list,
                    });
                }
            } else {
                console.log("[error] unknown event name: " + eventName);
            }
            window.vm.$root.eventBus.$emit("notifyDeviceEvent", data);
        },
        handleAutoJoinGroup() {
            var group = this.user.auto_join_group;
            var that = this;
            if (group) {
                window.main_screen.applyJoinGroup(
                    {
                        mark: "",
                        gid: group.cid,
                        inviterID: 0,
                        source: 1,
                    },
                    (res) => {
                        if (res.error_code == 0) {
                            if (group.more_details && group.more_details.join_check) {
                                Toast(this.$t('group_apply_success'));
                            } else {
                                let groupTemp = {
                                    id: group.cid,
                                    subject: group.subject,
                                    is_single_chat: 0,
                                };
                                that.setDefaultImg([groupTemp]);
                                that.$store.commit("groupList/addGroup", groupTemp);
                                that.$store.commit("user/updateUser", {
                                    auto_join_group: null,
                                });
                                Toast(this.$t('attendee_join_group_already') + "：" + group.subject);
                            }
                        }
                    }
                );
            }
        },
        updateAiAnalyzeReport(is_succ, data) {
            if (is_succ) {
                console.log("updateAiAnalyzeReport", data);
                if (data && data.type == this.$store.state.aiPresetData.typeIndex.breastSearch) {
                    if (data.report && data.report.list && data.report.list.length > 0) {
                        this.$store.commit("gallery/updateCommentObjByKey", {
                            keys: { showAISearchSuggest: true },
                            resource_id: data.resource_id,
                        });
                    } else {
                        this.$store.commit("gallery/updateCommentObjByKey", {
                            keys: { showAISearchSuggest: false },
                            resource_id: data.resource_id,
                        });
                    }
                } else {
                    this.updateReportToGallery(data);
                    this.$store.commit("conversationList/updateAiAnalyzeReport", data);
                }
            }
        },
        // updateReportToGallery(data) {
        //     let mark_list = data.report && data.report.mark_list;
        //     for (let key in mark_list) {
        //         let report = {
        //             resource_id: key,
        //             ai_analyze_report: {
        //                 detected_tumor: data.report.detected_tumor,
        //                 error: data.report.error,
        //                 summary: data.report.summary,
        //                 mark_list: {},
        //             },
        //         };
        //         report.ai_analyze_report.mark_list[key] = data.report.mark_list[key];
        //         this.$store.commit("gallery/updateAiReportToGallery", report);
        //     }
        // },
        async updateReportToGallery(data) {
            let mark_list = data.report && data.report.mark_list;
            let report = {
                ai_analyze_report: {
                    detected_tumor: data.report.detected_tumor,
                    error: data.report.error,
                    summary: data.report.summary,
                    mark_list: {},
                    clips: {},
                    ai_analyze_id: data.ai_analyze_id,
                    type: data.type,
                    status: 1,
                },
                group_id: data.group_id || data.cid,
                exam_id: data.exam_id,
                ai_analyze_id: data.ai_analyze_id,
            };
            if (mark_list) {
                for (let key in mark_list) {
                    let new_report = cloneDeep(report);
                    new_report.resource_id = key;
                    new_report.ai_analyze_report.mark_list[key] = data.report.mark_list[key];
                    let clips = data.report && data.report["clips"];
                    if (clips) {
                        new_report.ai_analyze_report["clips"][key] = clips[key];
                    }
                    this.$store.commit("gallery/updateAiReportToGallery", new_report);
                    this.$store.commit("examList/updateAiReportToExamList", new_report);
                }
            } else {
                let clips = data.report && data.report["clips"];
                if (clips) {
                    for (let key in clips) {
                        let new_report = cloneDeep(report);
                        new_report.resource_id = key;
                        new_report.ai_analyze_report["clips"][key] = clips[key];
                        // console.error('updateReportToGallery',new_report)
                        this.$store.commit("gallery/updateAiReportToGallery", new_report);
                        this.$store.commit("examList/updateAiReportToExamList", new_report);
                    }
                } else {
                    this.$store.commit("examList/updateAiReportToExamList", report);
                }
            }
        },

        addTagIfNeed(data) {
            //AI分析结果如果存在label自动打标签
            var that = this;
            let chatMessageList = this.conversationList[data.cid].chatMessageList;
            let images = [];
            let mark_list = data.report.mark_list;
            for (let i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message = chatMessageList[i];
                if (message.ai_analyze_id == data.ai_analyze_id) {
                    images = message.ai_analyze.messages;
                    break;
                }
            }
            if (!mark_list) {
                return;
            }
            for (let key in mark_list) {
                for (let mark_item of mark_list[key]) {
                    if (!mark_item.label) {
                        continue;
                    }
                    for (let image of images) {
                        if (key == image.resource_id) {
                            var controller = this.conversationList[data.cid].socket;
                            var sendDate = {
                                resource_id: image.resource_id,
                                img_id: image.img_id,
                                tag: mark_item.label,
                                sender_id: this.user.uid,
                            };
                            console.log("emit add_custom_tags");
                            controller.emit("add_custom_tags", sendDate, function (is_succ, data) {
                                if (is_succ) {
                                    if (data == "add_tag_duplication") {
                                        Toast(that.$t('tag_repeat_add'));
                                    } else {
                                        that.$store.commit("gallery/addTagList", data);
                                    }
                                } else {
                                    Toast("add_custom_tags error");
                                }
                            });
                        }
                    }
                }
            }
        },
        globalSwipeRight() {
            // this.$root.eventBus.$emit('chatWindowKeyboardChanged',{
            //     keyboard:0
            // })
            // this.back()
        },
        deleteGroupset() {
            this.doDeleteGroupset(this.operateChat);
        },
        isResource(type) {
            if (
                type == this.systemConfig.msg_type.Image ||
                type == this.systemConfig.msg_type.Frame ||
                type == this.systemConfig.msg_type.OBAI ||
                type == this.systemConfig.msg_type.Cine ||
                type == this.systemConfig.msg_type.Video ||
                type == this.systemConfig.msg_type.RealTimeVideoReview ||
                type == this.systemConfig.msg_type.VIDEO_CLIP
            ) {
                return true;
            } else {
                return false;
            }
        },
        messageNotify(cid) {
            if (this.notifying || window.livingStatus === 2) {
                //去除重复通知
                return;
            }
            let voice = 1;
            if (this.$route.params.cid == cid) {
                voice = 0;
            }
            this.clientNotice({
                voice: voice,
            });

            // window.CWorkstationCommunicationMng.clientNotice({
            //     voice:voice,
            //     shack:1,
            //     notice:1
            // })
        },
        playNotify() {
            if (this.appPlus) {
            }
        },
        clientNotice(json) {
            let that = this;

            if (json.voice && this.appPlus) {
                try {
                    window.CWorkstationCommunicationMng.deviceVibrate({ duration: "200" });
                } catch (error) {
                    Toast(error);
                }
                window.CWorkstationCommunicationMng.clientNotice();
                // this.playNotify()
            }
        },
        // deleteMyselfFromAttendees(conversation){
        //     const tempConversation = {} // 退群后的conversation
        //     let deleteAttendeeKey = 0 // 主动退群成员在attendeeList的索引
        //     for(const key in conversation) {
        //         tempConversation[key] = conversation[key]
        //     }
        //     let beforeAttendeeList = tempConversation.attendeeList
        //     for(const key in beforeAttendeeList) {
        //         if(beforeAttendeeList[key].userid === this.user.id) {
        //             delete beforeAttendeeList[key]
        //             break
        //         }
        //     }
        //     return tempConversation
        // },
        setAllHospital(is_succ, data) {
            if (is_succ) {
                this.$store.commit("dynamicGlobalParams/updateDynamicGlobalParams", {
                    hospitals: data,
                });
            }
        },
        getClipboardInfo() {
            Tool.createCWorkstationCommunicationMng({
                name: "getClipboard",
                emitName: 'NotifyClipboard',
                params:{},
                timeout:null
                }).then((json_str)=>{
                    let json = null;

                    // 安全地解析JSON
                    if (typeof json_str === 'string' && json_str) {
                        try {
                            json = JSON.parse(json_str);
                        } catch (error) {
                            // 如果JSON解析失败，说明剪切板内容是普通字符串，不是JSON格式
                            console.log('剪切板内容不是有效的JSON格式:', json_str);
                            return;
                        }
                    } else {
                        json = json_str;
                    }

                    if (typeof json === "object" && json !== null) {
                        let data = {};
                        Object.keys(json).forEach((item) => {
                            data[item] = decodeURIComponent(json[item]);
                        });
                        window.vm.$root.eventBus.$emit("executeByAction", data);
                        window.CWorkstationCommunicationMng.setClipboard({ str: "" }); // 存完马上清除剪切板
                    }

            });
        },
        checkPassword(user) {
            if (!user.is_password_privatized) {
                this.isShowSetPassword = true;
                this.presetPasswordType = 1;
            } else if (!user.is_enhance_password) {
                this.isShowSetPassword = true;
                this.presetPasswordType = 2;
            }
        },
        gotoModifyPassword() {
            this.isShowSetPassword = false;
            window.vm.$router.push(`/index/modify_password?force_enhance_password`);
        },
        skipModifyPassword() {
            this.isShowSetPassword = false;
        },
        updateLiveCount() {
            window.main_screen.getBroadcastStatusCount(null, (res) => {
                if (!res.error_code) {
                    this.$root.eventBus.$emit("refreshLiveManagementList");
                    this.$store.commit("notifications/updateLiveCount", res.data);
                }
            });
        },
        sendLiveTransmitMessage(data) {
            let queue = this.$root.transmitTempList;
            queue.forEach((item) => {
                let params = {
                    live_id: item.liveInfo.live_id,
                };
                if (data.from === "friend") {
                    params.rc_type = 1;
                    params.rc_id = data.uid;
                } else if (data.from === "group") {
                    params.rc_type = 2;
                    params.rc_id = data.cid;
                }
                window.main_screen.inviteOthersLive(params, (res) => {
                    if (!res.error_code) {
                        // this.liveHistoryList = res.data
                        Toast(this.$t('machine_transmit_tip'));
                    } else {
                        Toast(res.error_msg);
                    }
                });
            });
        },
        NotifyAgoraLiveStart(data) {
            this.$store.commit("liveConference/updateConferenceState", {
                cid: data.groupInfo.id,
                obj: {
                    conferenceState: 1,
                    senderUserId: data.host_uid,
                    last_message_ts: new Date().getTime(),
                },
            });
            // this.judgeIfNeedAddChatList(data,false)
            this.debounceSortChatList();
        },
        NotifyAgoraLiveStop(data) {
            this.$store.commit("liveConference/updateConferenceState", {
                cid: data.groupInfo.id,
                obj: {
                    conferenceState: 0,
                    senderUserId: 0,
                },
            });
        },
        preHandleRecordMode(gid, record_mode) {
            return new Promise((resolve, reject) => {
                let data = {
                    gid,
                    record_mode,
                };
                let timer = setTimeout(() => {
                    reject("preHandleRecordMode time out");
                }, 10000);
                this.conversationList[gid].socket.emit("edit_record_mode", data, (is_succ, data) => {
                    if (is_succ) {
                        //修改成功
                        this.$store.commit("conversationList/updateIsLiveRecord", {
                            cid: gid,
                            record_mode,
                        });
                        resolve(true);
                    } else {
                        //修改失败
                        reject(false);
                    }
                    clearTimeout(timer);
                    timer = null;
                });
            });
        },
        ifNeedAutoPushStream() {
            let isUltraSoundMobile = this.isUltraSoundMobile;
            console.log(window.localStorage.getItem(`isAutoPushStream_${this.user.uid}`), "ifNeedAutoPushStream");
            if (window.localStorage.getItem(`isAutoPushStream_${this.user.uid}`) === null && isUltraSoundMobile) {
                //u-liner设备如果没有设置自动推流 ，默认设置自动推流
                window.localStorage.setItem(`isAutoPushStream_${this.user.uid}`, 1);
            }
            let isAutoPushStream = !!Number(window.localStorage.getItem(`isAutoPushStream_${this.user.uid}`));
            let lastPushStreamCid = window.localStorage.getItem(`lastPushStreamCid_${this.user.uid}`);
            let isAutoPushRecord = !!Number(window.localStorage.getItem(`isAutoPushRecord_${this.user.uid}`));
            // if(isUltraSoundMobile){
            //     window.main_screen.CMonitorWallPush.controller.emit('startJoinRoomSilence')
            // }

            return {
                isAutoPushStream,
                lastPushStreamCid,
                isAutoPushRecord,
            };
        },
        testAutoPushStream() {
            this.autoPushStream();
        },
        autoPushStream() {
            let isUltraSoundMobile = this.isUltraSoundMobile;
            const { isAutoPushStream, lastPushStreamCid, isAutoPushRecord } = this.ifNeedAutoPushStream();
            const isAutoStreamInterrupted = this.$store.state.dynamicGlobalParams.isAutoStreamInterrupted;
            if (isAutoPushStream && lastPushStreamCid && isUltraSoundMobile && this.$route.name === 'index'&&this.active ==='chat'&&!isAutoStreamInterrupted) {
                this.openConversation(lastPushStreamCid, 13, async (is_success, data) => {
                    if (!is_success) {
                        return;
                    }
                    await this.preHandleRecordMode(lastPushStreamCid, isAutoPushRecord);
                    let isAIAnalyzeLive =
                        this.$store.state.conversationList[lastPushStreamCid].service_type ==
                        this.systemConfig.ServiceConfig.type.AiAnalyze;
                    console.log( "isAIAnalyzeLive",isAIAnalyzeLive);
                    if (isAIAnalyzeLive) {
                        return;
                    }
                    this.$router.push({
                        path: `/index/chat_window/${lastPushStreamCid}`,
                    });
                    setTimeout(() => {
                        this.$root.eventBus.$emit("chatWindowStartJoinRoom", { main: 1, aux: 1, isSender: 1 });
                        this.$store.commit("liveConference/updateConferenceValue", {
                            autoPushReady: true,
                        });
                    }, 1000);
                });
            } else {
                this.$store.commit("liveConference/updateConferenceValue", {
                    autoPushReady: true,
                });
            }
        },
        // NotifyInitNativeAgoraSdk(json){
        //     console.error('NotifyInitNativeAgoraSdk',json)
        //     if(!json.error_code){
        //         this.$store.commit('liveConference/updateConferenceValue',{
        //             nativeSdkReady:true,
        //         })
        //         this.ifNeedAutoPushStream()
        //     }
        // },
        //operatePushStreamByULinker({ConferenceID:4022,mainstreamType:'desktop',auxstreamType:'camera',action:'start'})
        async operatePushStreamByULinker(json_str) {
            let isUltraSoundMobile = this.isUltraSoundMobile;
            const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
            console.log("operatePushStreamByULinker:", json_str);
            //退出全屏
            window.CWorkstationCommunicationMng.setFullscreen({isFull:'false'});
            window.CWorkstationCommunicationMng.lockOrientation({action:'portrait-primary'});
            if (json_str && isUltraSoundMobile) {
                if (!this.pushStreamReady) {
                    window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                        errorcode: 2,
                        errormsg: mobile_errors.CommonError.JOIN_LIVE_ROOM_FAIL_ERROR,
                    });
                    return;
                }
                let cid = json_str.ConferenceID;
                let mainstreamType = json_str.mainstreamType.toLowerCase();
                let auxstreamType = json_str.auxstreamType.toLowerCase();
                let action = json_str.action;

                if (cid && mainstreamType && auxstreamType) {
                    let operateByULinker = {
                        mainstreamType,
                        auxstreamType: auxstreamType,
                        action,
                        cid,
                    };

                    //开始
                    if (action == "start") {
                        if (
                            window.vm.$root.currentLiveCid &&
                            window.vm.$root.currentLiveCid == cid &&
                            getLiveRoomObj(window.vm.$root.currentLiveCid) &&
                            window.livingStatus > 0
                        ) {
                            window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                errorcode: 0,
                                errormsg: "",
                            });
                            return;
                        }

                        if (
                            window.vm.$root.currentLiveCid &&
                            getLiveRoomObj(window.vm.$root.currentLiveCid) &&
                            window.livingStatus > 0
                        ) {
                            this.$store.commit("globalParams/updateGlobalParams", {
                                operateByULinker: operateByULinker,
                            });
                            // destroyAllConference();
                            let liveRoom = getLiveRoomObj(window.vm.$root.currentLiveCid);
                            if (liveRoom) {
                                liveRoom.LeaveChannelAux("sender");
                                liveRoom.data.joinedAux = false;
                            }
                            // await this.$router.back();
                            await Tool.backToRoute("/index");
                            this.$root.eventBus.$emit('toggleTab','chat')
                            this.openConversation(cid, 13, async (is_success, data) => {
                                if (!is_success) {
                                    this.$store.commit("globalParams/updateGlobalParams", {
                                        operateByULinker: {},
                                    });
                                    window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                        errorcode: 2,
                                        errormsg: mobile_errors.CommonError.OPEN_CONSERVATION_FAIL_ERROR,
                                    });
                                    return;
                                }

                                this.$store.commit("globalParams/updateGlobalParams", {
                                    operateByULinker: operateByULinker,
                                });
                                await Tool.backToRoute("/index");
                                this.$root.eventBus.$emit('toggleTab','chat')
                                setTimeout(() => {
                                    this.$router.push(`/index/chat_window/${cid}`);
                                }, 800);
                            });
                        } else {
                            this.$store.commit("globalParams/updateGlobalParams", {
                                operateByULinker: operateByULinker,
                            });
                            this.openConversation(cid, 13, async (is_success, data) => {
                                if (!is_success) {
                                    this.$store.commit("globalParams/updateGlobalParams", {
                                        operateByULinker: {},
                                    });
                                    window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                        errorcode: 2,
                                        errormsg: mobile_errors.CommonError.OPEN_CONSERVATION_FAIL_ERROR,
                                    });
                                    return;
                                }

                                await Tool.backToRoute("/index");
                                this.$root.eventBus.$emit('toggleTab','chat')
                                this.$store.commit("globalParams/updateGlobalParams", {
                                    operateByULinker: operateByULinker,
                                });
                                // await this.$router.back();
                                setTimeout(() => {
                                    this.$router.push(`/index/chat_window/${cid}`);
                                }, 800);
                            });
                        }
                    }
                    if (action == "update") {
                        let liveRoom = getLiveRoomObj();
                        if (!liveRoom) {
                            window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                errorcode: 2,
                                errormsg: mobile_errors.CommonError.LIVE_ROOM_IS_INVALID_ERROR,
                            });

                            return;
                        }
                        let liveRoomData = liveRoom.data || {};

                        if (mainstreamType == "desktop") {
                            if (liveRoomData.joinedMain && liveRoomData.currentMainUltrasyncId != this.user.uid) {
                                window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                    errorcode: 2,
                                    errormsg:
                                        mobile_errors.CommonError.PUSH_MAIN_STREAM_FAIL_NO_MAIN_CHANNEL_IN_USE_ERROR,
                                });
                                return;
                            }
                            this.$root.eventBus.$emit(
                                "chatWindowStartJoinRoom",
                                { main: 1, aux: 0, isSender: 0, videoSource: mainstreamType,
                                    joinRoomSuccessCallback: ()=>{
                                        window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                            errorcode: 0,
                                            errormsg: "",
                                        });
                                    },
                                    joinRoomFailedCallback: ()=>{
                                        window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                            errorcode: 2,
                                            errormsg: mobile_errors.CommonError.JOIN_LIVE_ROOM_FAIL_ERROR,
                                        });
                                    }
                                 }
                            );
                        }
                        if (mainstreamType == "doppler") {
                            if (liveRoomData.joinedMain && liveRoomData.currentMainUltrasyncId != this.user.uid) {
                                window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                    errorcode: 2,
                                    errormsg:
                                        mobile_errors.CommonError.PUSH_MAIN_STREAM_FAIL_NO_MAIN_CHANNEL_IN_USE_ERROR,
                                });
                                return;
                            }

                            this.$root.eventBus.$emit(
                                "chatWindowStartJoinRoom",
                                { main: 1, aux: 0, isSender: 0, videoSource: mainstreamType,
                                    joinRoomSuccessCallback: ()=>{
                                        window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                            errorcode: 0,
                                            errormsg: "",
                                        });
                                    },
                                    joinRoomFailedCallback: ()=>{
                                        window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                            errorcode: 2,
                                            errormsg: mobile_errors.CommonError.JOIN_LIVE_ROOM_FAIL_ERROR,
                                        });
                                    }
                                 }
                            );
                        }
                        if (mainstreamType == "none") {
                            if (
                                !liveRoomData.joinedMain ||
                                (liveRoomData.joinedMain && liveRoomData.currentMainUltrasyncId != this.user.uid)
                            ) {
                                window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                    errorcode: 0,
                                    errormsg: "",
                                });
                                return;
                            }
                            liveRoom.LeaveChannelMain();
                            // liveRoom.MuteLocalVideoStream({ uid: liveRoomData.localMainUid, isMute: true })
                        }
                        if (auxstreamType == "camera") {
                            let cv = this.$store.state.conversationList[cid] || {};
                            let isAIAnalyzeLive =
                                this.cv.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
                            if (liveRoom.joinedAux || isAIAnalyzeLive) {
                                window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                    errorcode: 0,
                                    errormsg: "",
                                });
                                return;
                            }
                            liveRoom.MuteLocalVideoStream({ uid: liveRoomData.localAuxUid, isMute: false });
                        }
                        if (auxstreamType == "none") {
                            if (!liveRoomData.joinedAux) {
                                window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                    errorcode: 0,
                                    errormsg: "",
                                });
                                return;
                            }
                            liveRoom.MuteLocalVideoStream({ uid: liveRoomData.localAuxUid, isMute: true });
                        }
                    }
                    //离开
                    if (action == "leave") {
                        let liveRoom = getLiveRoomObj(cid);
                        if (!liveRoom) {
                            window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                errorcode: 0,
                                errormsg: "",
                            });
                            return;
                        }
                        let cv = this.$store.state.conversationList[cid] || {};
                        if (cv.is_single_chat) {
                            liveRoom.LeaveChannelAux("sender");
                        } else {
                            liveRoom.LeaveChannelAux("normal");
                        }
                        // destroyAllConference()
                        // setTimeout(() => {
                        //     this.$router.back();
                        // }, 800);
                    }
                    //退出
                    if (action == "quit") {
                        let liveRoom = getLiveRoomObj(cid);

                        if (!liveRoom) {
                            window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                errorcode: 0,
                                errormsg: "",
                            });
                            return;
                        }
                        liveRoom.LeaveChannelAux("sender");
                        // destroyAllConference()
                        // setTimeout(() => {
                        //     this.$router.back();
                        // }, 800);
                    }
                }
            }
        },
        async handleAppHidden() {
            // 记录应用可见性状态变化
            Logger.save({
                message: 'app_visibility_change',
                eventType: 'app_lifecycle',
                data: {
                    visibilityState: document.visibilityState,
                    hidden: document.hidden,
                    timestamp: new Date().toISOString(),
                    socketConnected: window.main_screen?.gateway?.connected || false
                }
            });

            // 当应用从后台恢复到前台时，检查socket连接状态
            if (document.visibilityState === "visible") {
                // 清除之前的检查定时器
                if (this.resumeCheckTimeout) {
                    clearTimeout(this.resumeCheckTimeout);
                    this.resumeCheckTimeout = null;
                }

                // 重置自动登录尝试标记
                this.hasTriedAutoLoginAfterResume = false;

                // 延迟10秒检查网络状态，如果依然断网且未尝试过自动登录，则尝试自动登录
                this.resumeCheckTimeout = setTimeout(() => {
                    // 检查是否处于断网状态
                    const isNetworkUnavailable = this.$store.state.loadingConfig.networkUnavailable;
                    const isSocketDisconnected = !window.main_screen?.gateway?.connected;

                    Logger.save({
                        message: 'app_resume_network_check',
                        eventType: 'app_lifecycle',
                        data: {
                            isNetworkUnavailable,
                            isSocketDisconnected,
                            hasTriedAutoLoginAfterResume: this.hasTriedAutoLoginAfterResume,
                            timestamp: new Date().toISOString()
                        }
                    });

                    // 如果网络不可用或socket未连接，且从未尝试过自动登录
                    if ((isNetworkUnavailable || isSocketDisconnected) && !this.hasTriedAutoLoginAfterResume) {
                        this.hasTriedAutoLoginAfterResume = true;

                        Logger.save({
                            message: 'app_resume_auto_login_attempt',
                            eventType: 'app_lifecycle',
                            data: { timestamp: new Date().toISOString() }
                        });

                        this.autoLogin((is_succ) => {
                            Logger.save({
                                message: 'app_resume_auto_login_result',
                                eventType: 'app_lifecycle',
                                data: {
                                    success: is_succ,
                                    timestamp: new Date().toISOString()
                                }
                            });

                            if (is_succ) {
                                this.initNetworkData();
                            }
                        });
                    }
                }, 10000); // 10秒后检查
            } else {
                // 应用进入后台时，清除检查定时器
                if (this.resumeCheckTimeout) {
                    clearTimeout(this.resumeCheckTimeout);
                    this.resumeCheckTimeout = null;
                }
            }
        },
        quickLaunchLive() {
            this.$store.commit("dynamicGlobalParams/setAutoStreamInterrupted", true);
            this.isShowQuickLaunchModel = true;
        },
        openConversationFromIndexByUserId(id, callback) {
            this.openConversationByUserId(id, callback);
        },
        getDeviceNameById() {
            if (!this.isUltraSoundMobile) {
                return;
            }
            return new Promise((resolve, reject) => {
                const params = {
                    deviceId: this.deviceInfo.device_id,
                };
                window.main_screen.getDeviceNameById(params, (res) => {
                    if (res.error_code === 0) {
                        this.$store.commit("device/updateDeviceInfo", { device_name: res.data.name });
                        resolve(true);
                    } else {
                        reject(res.error_msg);
                    }
                });
            });
        },
        closeChatWindowFromIndex(callback) {
            callback && callback(true);
        },
        getAllTags() {
            let loginToken = window.localStorage.getItem("loginToken") || (this.user && this.user.new_token) || "";
            if (loginToken) {
                service.getAllTags().then((res) => {
                    if (res.data.error_code == 0) {
                        this.$store.commit("gallery/addTagTopInfo", res.data.data);
                    }
                });
            }
        },
        getAiAnalyzeTypes() {
            service.getAiAnalyzeTypes().then((res) => {
                if (res.data.error_code == 0) {
                    this.$store.commit("aiPresetData/updateAiPresetData", res.data.data);
                }
            });
        },
        NotifyUpdateLiveRecord(data) {
            this.$store.commit("conversationList/updateChatMessageLiveRecordData", {
                group_id: data.gid,
                resource_id: data.resource_id,
                live_record_data: data.resource.more_details,
                coverUrl: data.coverUrl,
            });
            this.$store.commit("consultationImageList/updateConsultationLiveRecordData", {
                group_id: data.gid,
                resource_id: data.resource_id,
                live_record_data: data.resource.more_details,
                coverUrl: data.coverUrl,
            });
        },
        clearAndDirectToLogin() {
            this.unBindControllerEvent()
            if (window.main_screen && window.main_screen.gateway) {
                window.main_screen.CloseSocket();
            }
            this.clearAllStore();
            this.$router.replace(`/login`);
        },
        clearAndDirectToULinkerConsultation() {
            this.unBindControllerEvent()
            if (window.main_screen && window.main_screen.gateway) {
                window.main_screen.CloseSocket();
            }
            this.clearAllStore();
            Tool.setConsultationMode(1)
            this.$router.replace(`/ulinker_consultation`);
        },
        clearAllStore() {
            //退出登录或切换账号需要清理所有store，未实现完整
            this.$store.commit("user/updateUser", { new_token: "" });

            // 清理权限系统（保留区域权限）
            try {
                this.$permission.logoutCleanup();
                console.log('移动端主页权限系统退出登录清理完成');
            } catch (error) {
                console.error('移动端主页权限系统退出登录清理失败:', error);
            }

            this.$store.commit("chatList/clearChatList");
            this.$store.commit("friendList/clearFriendList");
            this.$store.commit("consultationImageList/clearConsultationImages");
            this.$store.commit("relationship/clearRelationship");

            // 清除keep-alive缓存
            this.$root.eventBus.$emit('clearKeepAliveCache');
        },
        NotifyCloudLoginOrLogout(data) {
            const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
            const storeToken = this.user.new_token;
            const loginToken = window.localStorage.getItem("loginToken") || storeToken || "";
            const action = data.action || "";
            const ulinkerToken = data.ulinkerToken;

            if (!ulinkerToken || action == "Logout") {
                destroyAllConference();
                this.clearAllStore();
                window.CWorkstationCommunicationMng.ULinkerConnectionResult({
                    errorcode: 0,
                    errormsg: "",
                    action: "Logout",
                });
                setTimeout(function () {
                    this.$router.replace(`/login`);
                }, 800);
                return;
            }

            const newUserId = ulinkerToken.split(":")[0];
            if (loginToken && loginToken.split(":")[0] == newUserId) {
                if (Tool.checkMainScreenConnected()) {
                    window.CWorkstationCommunicationMng.ULinkerConnectionResult({
                        errorcode: 0,
                        errormsg: "",
                        action: "Login",
                    });
                } else {
                    window.CWorkstationCommunicationMng.ULinkerConnectionResult({
                        errorcode: 1,
                        errormsg: mobile_errors.CommonError.MAIN_SCREEN_SOCKET_DISCONNECT_ERROR,
                        action: "Login",
                    });
                }
            } else {
                //切换用户
                destroyAllConference();
                Tool.backToRoute("/index");
                this.$root.eventBus.$emit('toggleTab','chat')
                console.log("change user");
                setTimeout(function () {
                    this.$store.commit("user/updateUser", { id: newUserId });
                    window.localStorage.setItem("loginToken", data.ulinkerToken);
                    this.autoLogin((success) => {
                        console.error("success:", success);
                        if (!success) {
                            window.CWorkstationCommunicationMng.ULinkerConnectionResult({
                                errorcode: 1,
                                errormsg: mobile_errors.CommonError.LOGIN_TOKEN_IS_INVALID_ERROR,
                                action: "Login",
                            });
                            this.clearAllStore();
                        } else {
                            //this.clearAllStore();
                            this.initPage();
                            this.initNetworkData();
                            window.CWorkstationCommunicationMng.ULinkerConnectionResult({
                                errorcode: 0,
                                errormsg: "",
                                action: "Login",
                            });
                        }
                    });
                }, 500);
            }
        },
        kickoutAttendeeHandle(data, controller) {
            console.error("kickoutAttendeeHandle", 111, data, controller);
            //收到某人退群的消息
            const that = this;
            let deleteGroupConnent = function (data) {
                that.$store.commit("chatList/deleteChatList", { cid: data.cid });
                that.$store.commit("groupList/deleteGroupList", { cid: data.cid });
                that.$store.commit("conversationList/deleteConversationList", { cid: data.cid });
                that.$store.commit("notifications/deleteGroupApplyByCid", { cid: data.cid });
                //删除群相关的图像
                that.$store.commit("consultationImageList/deleteConsultationImageListByGroupID", { cid: data.cid });
                that.$store.commit("gallery/deleteGalleryListByGroupID", { cid: data.cid });
                // closeChatWindowIfNeed();
                closeChatWindowForce();
            };
            if (data.uid == that.user.id) {
                //自己退群
                let message = "";

                if (data.initiator_uid == that.user.id) {
                    //自己主动退群
                    message = that.$t('user_exit_group_succ');
                    const conversation = that.conversationList[data.cid];
                    if (!conversation) {
                        return;
                    }
                    if (conversation.attendeeList && conversation.attendeeList[`attendee_${that.user.id}`]) {
                        conversation.attendeeList[`attendee_${that.user.id}`].attendeeState = 0;
                    }
                    // let afterDeleteConv = that.deleteMyselfFromAttendees(conversation)
                    that.$root.eventBus.$emit("createGroupAvatar", {
                        conversation: conversation,
                        cb: (controller) => {
                            console.log("Callback createGroupAvatar");
                            controller.emit("response_delete_attendee", function (is_succ, info) {
                                console.log("Callback response_delete_attendee");
                            });
                        },
                    });

                    deleteGroupConnent(data);
                } else {
                    //自己被动退群
                    message = data.initiator_nickname + " " + that.$t('user_passive_exit_group') + " " + data.subject;

                    controller.emit("response_delete_attendee", function (is_succ, info) {
                        console.log("Callback response_delete_attendee");
                    });
                    deleteGroupConnent(data);
                }
                Toast(message);

                //给服务器发送消息response_delete_attendee，通知断开参与者(已被放置到图片设置成功的回调中)
                // controller.emit("response_delete_attendee", function(is_succ, info){
                //     console.log("Callback response_delete_attendee");
                // });
            } else {
                //他人退群
                that.$store.commit("conversationList/deleteAttendee", data);
                that.$store.commit("notifications/deleteGroupApplyByUidAndCid", { uid: data.uid, cid: data.cid });
            }
        },
        sortChatList() {
            this.$store.commit("chatList/sortChatList");
        },
        unBindControllerEvent() {
            if (window.main_screen && window.main_screen.controller) {
                window.main_screen.controller.off("gateway_connect");
                window.main_screen.controller.off("gateway_error");
                window.main_screen.controller.off("gateway_reconnecting");
                window.main_screen.controller.off("gateway_reconnect_fail");
                window.main_screen.controller.off("gateway_reconnect");
                window.main_screen.controller.off("gateway_disconnect");
                window.main_screen.controller.off("notify_login_another");
                window.main_screen.controller.off("notify_user_destroy");
                window.main_screen.controller.off("notify.user.device.sync.live");
                window.main_screen.controller.off("pumch.qclive");
            }
        },
        checkExitConversation(cid) {
            if (
                window.main_screen.conversation_list.hasOwnProperty(cid) &&
                window.main_screen.conversation_list[cid].gateway.check&&
                window.vm.$store.state.conversationList[cid].socket
            ) {
                return true;
            } else {
                return false;
            }
        },
        handleMenuItemClick() {
            this.popupMore = false;
        },
        getApplyCount(cid) {
            window.main_screen.conversation_list[cid].getApplyCount({}, (res) => {
                if (res.error_code === 0) {
                    this.$store.commit("conversationList/updateConversation", {
                        cid: cid,
                        key: "applyCount",
                        value: res.data,
                    });
                }
            });
        },
        getUnfinishedHomework(cid) {
            service
                .getIncompleteList({
                    gid: cid,
                    page: 1,
                    pageSize: 1,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const homework = res.data.data.data.shift();
                        if (homework) {
                            if (cid === 0) {
                                this.$store.commit("homework/updateHomework", {
                                    globalUnfinish: 1,
                                });
                            } else {
                                let obj = {};
                                obj[cid] = homework;
                                this.$store.commit("homework/updateUnfinish", obj);
                            }
                        } else {
                            if (cid === 0) {
                                this.$store.commit("homework/updateHomework", {
                                    globalUnfinish: 0,
                                });
                            } else {
                                let obj = {};
                                obj[cid] = undefined;
                                this.$store.commit("homework/updateUnfinish", obj);
                            }
                        }
                    }
                });
        },
        getUncorrectHomework(cid) {
            service.getUncorrectedList({
                    gid: cid,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        const map = res.data.data;
                        if (Object.keys(map).length !== 0) {
                            if (cid === 0) {
                                this.$store.commit("homework/updateHomework", {
                                    globalUnCorrect: map,
                                });
                            } else {
                                let obj = {};
                                obj[cid] = map;
                                this.$store.commit("homework/updateUncorrected", obj);
                            }
                        } else {
                            if (cid === 0) {
                                this.$store.commit("homework/updateHomework", {
                                    globalUnCorrect: undefined,
                                });
                            } else {
                                let obj = {};
                                obj[cid] = undefined;
                                this.$store.commit("homework/updateUncorrected", obj);
                            }
                        }
                    }
                });
        },
        getCorrectedHomework(cid){
            service.getFinishedList({
                gid:cid,
                page:1,
                pageSize:30,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    const homeworkList = res.data.data.data;
                    // 计算status=3（已批改，未阅）的作业数量
                    const correctedCount = homeworkList.filter(item => item.status === 3).length;

                    if (correctedCount > 0) {
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalCorrected: correctedCount
                            });
                        } else {
                            let obj = {}
                            obj[cid] = homeworkList.filter(item => item.status === 3)
                            this.$store.commit("homework/updateCorrected", obj);
                        }
                    } else {
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalCorrected: 0
                            });
                        } else {
                            let obj = {}
                            obj[cid] = undefined
                            this.$store.commit("homework/updateCorrected", obj);
                        }
                    }
                }
            })
        },
        NotifyUpdateAnnouncement(data) {
            if (data.switch) {
                this.$store.commit("globalParams/updateGlobalParams", {
                    closedNotifyBar: false,
                    announcementContent: data.content,
                });
            } else {
                this.$store.commit("globalParams/updateGlobalParams", {
                    closedNotifyBar: true,
                    announcementContent: "",
                });
            }
        },
        getGroupSetList() {
            window.main_screen.getGroupsetList({}, (data) => {
                console.log("getGroupsetList", data);
                if (data.error_code == 0) {
                    let list = data.data;
                    list.forEach((item) => {
                        item.type = this.systemConfig.ConversationConfig.type.GroupSet;
                    });
                    this.$store.commit("groupset/initGroupsetList", data.data);
                }
            });
            this.getManagerGroupsetList();
        },
        getManagerGroupsetList() {
            window.main_screen.getManagerGroupsetList({}, (data) => {
                console.log(data, "getManagerGroupsetList");
                if (data.error_code == 0) {
                    let list = data.data;
                    list.forEach((item) => {
                        item.type = this.systemConfig.ConversationConfig.type.GroupSet;
                    });
                    this.$store.commit("groupset/initGroupsetManagerList", data.data);
                }
            });
        },
        observeImageLoad() {
            const fallbackImageUrl = "static/resource/images/slt_err.png";
            Tool.observeImageLoad(fallbackImageUrl);
        },
        getMultiCenterOptionList() {
            multiCenterService.getMultiCenterAllOptions().then((res) => {
                if (res.data.error_code == 0) {
                    this.$store.commit("multicenter/updateMCOptionList", res.data.data);
                }
            });
        },
        checkWebimPageState() {
            window.CWorkstationCommunicationMng.NotifyWebimPageState({ errorcode: 0, errormsg: "" });
        },
        startLiveToULinker(data){
            const cid = data.group_id
            // if(data.deviceType !== this.systemConfig.client_type['UltraSoundMobile']){
            //     return
            // }
            if (window.vm.$root.currentLiveCid ||window.livingStatus > 0) {
                return;
            }
            const forceBackIndexAndePushStream = async()=>{
                const isMatched = await Tool.backToRoute(`/index`);
                if(!isMatched){
                    this.$router.replace('/index')
                }
                this.$root.eventBus.$emit('toggleTab','chat')
                setTimeout(() => {
                    this.$router.push(`/index/chat_window/${cid}`);
                    setTimeout(()=>{
                        this.$root.eventBus.$emit("chatWindowStartJoinRoom", { main: 1, aux: 1, isSender: 1 });
                    },1000)
                }, 800);
            }
            this.openConversation(cid, 13, async (is_success, data) => {
                if (!is_success) {
                    return;
                }
                if(this.$route.path.includes(`/index/chat_window/${cid}`)){
                    const isMatched = await Tool.backToRoute(`/index/chat_window/${cid}`);
                    if(isMatched){
                        setTimeout(()=>{
                            this.$root.eventBus.$emit("chatWindowStartJoinRoom", { main: 1, aux: 1, isSender: 1 });
                        },1000)
                    }else{
                        forceBackIndexAndePushStream()
                    }
                }else{
                    forceBackIndexAndePushStream()
                }

            });
        },
        saveBindDeviceInfoToStorage(data) {
            const deviceInfo = {
                browserId: data.browserId,
                deviceId: data.deviceId
            };

            // 从localStorage获取之前存储的数据
            const storageDeviceInfo = localStorage.getItem('bindDeviceInfoFromBrowser');

            // 如果本地存储中有数据且是有效的数组
            let deviceInfoArray = [];
            if (storageDeviceInfo) {
                try {
                    deviceInfoArray = JSON.parse(storageDeviceInfo);
                } catch (e) {
                    console.error('Failed to parse stored device info:', e);
                }
            }

            // 检查是否有相同的browserId，如果没有重复则添加
            const isExist = deviceInfoArray.some(item => item.browserId === deviceInfo.browserId);
            if (!isExist) {
                deviceInfoArray.push(deviceInfo);
                // 将更新后的数据存回localStorage
                localStorage.setItem('bindDeviceInfoFromBrowser', JSON.stringify(deviceInfoArray));
            }

            console.error(localStorage.getItem('bindDeviceInfoFromBrowser'))
        },
        getBindDeviceInfoFromStorage() {
            // 从localStorage获取之前存储的数据
            const storageDeviceInfo = localStorage.getItem('bindDeviceInfoFromBrowser');

            // 如果本地存储中有数据且是有效的数组
            if (storageDeviceInfo) {
                try {
                    // 解析存储的数据
                    const deviceInfoArray = JSON.parse(storageDeviceInfo);

                    // 确保解析后的数据是一个数组
                    if (Array.isArray(deviceInfoArray)) {
                        return deviceInfoArray;
                    } else {
                        console.warn('Stored device info is not an array.');
                        return [];
                    }
                } catch (e) {
                    console.error('Failed to parse stored device info:', e);
                    return [];
                }
            } else {
                // 如果没有数据，则返回空数组
                return [];
            }
        },

                // 检查并自动设置隐私协议状态
        checkAndSetPrivacyAgreement() {
            const serverType = localStorage.getItem('serverType') || '云++';
            const privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy') || "{}");
            const privacy_version = this.$store.state.systemConfig.envConfig &&
                                  this.$store.state.systemConfig.envConfig.privacy_agreement_version;

            // 获取用户已同意的版本号
            const agreedVersion = privacyStatus[serverType];

            // 检查是否没有同意记录或记录为空（表示曾经撤销过）
            const hasNoAgreement = !agreedVersion || agreedVersion === '' || agreedVersion === 0;

            if (hasNoAgreement && privacy_version) {
                console.log('检测到用户没有隐私协议同意记录，自动设置为已同意状态');

                // 自动设置为已同意
                privacyStatus[serverType] = privacy_version;
                localStorage.setItem('isAgreePrivacyPolicy', JSON.stringify(privacyStatus));

                // 向客户端通知隐私协议状态
                if (window.CWorkstationCommunicationMng && window.CWorkstationCommunicationMng.setPrivacyPolicyStatus) {
                    window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                        status: 1,
                        version: privacy_version
                    });
                }

                console.log('隐私协议状态已自动设置为已同意，版本号:', privacy_version);
            } else if (!privacy_version) {
                console.log('等待获取隐私协议版本号...');
            } else {
                console.log('用户已有隐私协议同意记录，版本号:', agreedVersion);
            }
        },

    },
};
</script>
<style lang="scss">
.index_page {
    height: 100%;
    background-color: #222;
    position: absolute;
    width: 100%;
    top: 0;
    .main_container {
        width: 100%;
        height: 100%;
        background: #f4f4f4;
        transform: translateZ(0);
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        overflow: hidden;
        user-select: none;
        -webkit-user-select: none;
        display: flex;
        flex-direction: column;
        .mr-nav-bar {
            .more {
                position: absolute;
                right: 0;
                width: 2rem;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .index-popup-1 {
                position: absolute;
                left: auto;
                top: 2.2rem;
                right: 0.2rem;
                min-width: 8rem;
                max-width: 14rem;
                border-radius: 8px;
                transform: translate(0, 0);
                color: #333;
                text-align: left;
                padding: 0.2rem 0.5rem;
                font-weight: normal;
                z-index: 1001;
                background-color: #fff;
                font-size: 0.8rem;
                p {
                    line-height: 2.4;
                    display: block;
                    border-bottom: 1px solid #ddd;
                }
                p:last-child {
                    border: none;
                }
                &::before {
                    width: 0;
                    height: 0;
                    border-bottom: 0.5rem solid #fff;
                    border-left: 0.5rem solid transparent;
                    border-right: 0.5rem solid transparent;
                    content: "";
                    position: absolute;
                    top: -0.5rem;
                    right: 0.4rem;
                }
            }
        }
        .van-notice-bar {
            flex: none;
        }
        .index_header {
            .groupset_title {
                padding: 0px 2.4rem;
            }
            .app_title_CN {
                font-size: 1.6rem;
                margin-left: 2.4rem;
                margin-top: 0.5rem;
            }
            .app_title_CE,
            .app_title_EN {
                font-size: 1.2rem;
            }
            .left_item {
                position: absolute;
                width: 1.75rem;
                height: 100%;
                right: 1.75rem;
                top: 0;
                .svg_icon_search {
                    position: absolute;
                    right: 0.4rem;
                    width: 0.95rem;
                    height: 0.95rem;
                    top: 1rem;
                    fill: #fff;
                }
            }
        }
        .page-tab-container {
            width: 100%;
            user-select: none;
            flex: 1;
            overflow: auto;
            .left_rainbow {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                width: 0.3rem;
                z-index: 2;
                .block1 {
                    height: 20%;
                    background-color: #ff6759;
                }
                .block2 {
                    height: 20%;
                    background-color: #02c49e;
                }
                .block3 {
                    height: 20%;
                    background-color: #ffb144;
                }
                .block4 {
                    height: 20%;
                    background-color: #56c7fd;
                }
                .block5 {
                    height: 20%;
                    background-color: #737fde;
                }
            }

            .page-tabbar-tab-container {
                height: 100%;
                padding-left: 0.2rem;
                -webkit-overflow-scrolling: touch;
                .swiper-slide {
                    overflow: auto;
                }
                & > div {
                    height: 100%;
                }
            }
        }
        .nav {
            height: 3.4rem;
            box-sizing: border-box;
            background-color: #fff;
            user-select: none;
            -webkit-user-select: none;
            box-shadow: 0px -0.1rem 0.2rem rgba(0, 0, 0, 0.2);
            z-index: 2;
            width: 100%;
            display: flex;
            bottom: 0;
            .nav_item {
                flex: 1;
                text-align: center;
                font-size: 0.5rem;
                color: #a7bcba;
                position: relative;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                & > i {
                    fill: #a7bcba;
                    width: 1.25rem;
                    height: 1.25rem;
                    display: inline-block;
                    // margin-top: 0.5rem;
                    margin-bottom: 0.3rem;
                }
                .svg_chat,
                .svg_friend,
                .svg_library,
                .svg_mine {
                    fill: #a7bcba;
                    font-size: 1.2rem;
                }
                .svg_chat_active,
                .svg_friend_active,
                .svg_library_active,
                .svg_mine_active {
                    font-size: 1.2rem;
                    fill: #00c59d;
                }
                &.active {
                    color: #00c59d;
                }
                .total_unread {
                    position: absolute;
                    background-color: #ff675c;
                    color: #fff;
                    border-radius: 50%;
                    font-size: 0.5rem;
                    width: 0.95rem;
                    height: 0.95rem;
                    display: inline-block;
                    line-height: 0.95rem;
                    text-align: center;
                    font-style: normal;
                    top: 0.3rem;
                    left: calc(50% + 0.3rem);
                    .svg_ellipsis {
                        font-size: 0.8rem;
                        width: 100%;
                        height: 100%;
                        color: #fff;
                    }
                }
                .has_apply,
                .has_new_msg {
                    position: absolute;
                    background-color: #ff675c;
                    border-radius: 50%;
                    width: 0.45rem;
                    height: 0.45rem;
                    top: 0.3rem;
                    left: calc(50% + 0.5rem);
                }
            }
            .middle_nav_bg {
                width: 100%;
                height: 100%;
                position: absolute;
                bottom: 0;
                background: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                .middle_nav_shadow {
                    width: 3.6rem;
                    height: 3.6rem;
                    position: absolute;
                    bottom: 1rem;
                    background: #fff;
                    border-radius: 50%;
                    box-shadow: 0px -0.1rem 0.2rem rgb(0 0 0 / 20%);
                    z-index: -1;
                }
            }
            .middle_nav_button {
                width: 3.6rem;
                height: 3.6rem;
                border-radius: 50%;
                top: -1.1rem;
                position: absolute;
                display: flex;
                justify-content: center;
                align-items: center;
                .img_middle_nav {
                    width: 95%;
                    height: 95%;
                    border-radius: 50%;
                }
            }
        }
        // .share_operate{
        //     position:fixed;
        //     bottom:5rem;
        //     left:50%;
        //     box-sizing:border-box;
        //     transform:translateX(-50%);
        //     background-color: rgba(255,255,255,.9);
        //     color: #007aff;
        //     padding: .3rem .5rem;
        //     border-radius: .8rem;
        //     display:flex;
        //     z-index: 2;
        //     border:1px solid #eee;
        //     user-select: none;
        //     -webkit-user-select:none;
        //     i{
        //         font-size: 1rem;
        //         margin: 0 .5rem;
        //     }
        //     span{
        //         min-width:5rem;
        //         max-width:10rem;
        //         text-align:center;
        //         font-size:.8rem;
        //         margin-top:.1rem;
        //         white-space: nowrap;
        //         text-overflow: ellipsis;
        //         overflow: hidden;
        //     }
        // }
        .global_swipe_right {
            position: absolute;
            left: 0;
            top: 2.2rem;
            bottom: 0;
            width: 0.5rem;
            z-index: 9999;
        }
        .full_modal {
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 0.5;
            background: #000;
            z-index: 1000;
        }
        .skip_btn {
            margin-top: -0.5rem;
        }
    }
}
</style>
