<template>
    <div style="height: 100%; overflow: hidden; display: flex" class="chatMessageList" v-loading="openingHomework">
        <span style="display: none"></span>
        <!-- 这个不能去掉 某些IOS机器 不加会导致 按钮不显示 ，具体原因未知-->
        <div v-if="!is_loaded_history_list" class="skeleton">
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
            <van-skeleton title avatar :row="3" />
        </div>
        <div class="message_list_container" ref="message_container" @scroll="scrollDebounce">
            <van-pull-refresh
                ref="loadmore"
                v-show="showMessageList"
                :pulling-text="$t('top_pull_text')"
                :loosing-text="$t('bottom_drop_text')"
                :loading-text="$t('bottom_loading_text')"
                v-model="topLoading"
                @refresh="loadTopHistory"
            >
                <van-list v-model="bottomLoading" :finished="bottomAllLoaded" @load="loadBottomHistory" offset="150">
                    <div
                        v-for="(message, index) of messageList"
                        :ref="'message_item_' + index"
                        :key="`${message.tmp_gmsg_id}_${message.gmsg_id}`"
                        :data-gmsg-id="message.gmsg_id"
                    >
                        <div v-if="getSendTimeTip(index) != ''" class="notify_wrapper">
                            <div class="send_time_tip">
                                {{ getSendTimeTip(index, 2) }}
                            </div>
                        </div>
                        <div v-if="message.msg_type == systemConfig.msg_type.SYS_JOIN_ATTENDEE" class="notify_wrapper">
                            <div
                                v-if="message.attendee_changed_info.attendee_join_type == 1"
                                class="system_notify longwrap"
                            >
                                {{ message.attendee_changed_info.nickname }}
                                {{ $t('search_join_tip') }}
                            </div>
                            <div
                                v-if="message.attendee_changed_info.attendee_join_type == 2"
                                class="system_notify longwrap"
                            >
                                <span @click.stop="openVisitingCard(message, 6)">{{
                                    conversation.attendeeList &&
                                    conversation.attendeeList["attendee_" + message.attendee_changed_info.inviter_id]
                                        ? conversation.attendeeList[
                                              "attendee_" + message.attendee_changed_info.inviter_id
                                          ].nickname
                                        : "."
                                }}</span>
                                {{ $t('invited_join_group_tip1') }}
                                <span @click.stop="openVisitingCard(message, 5)">{{
                                    message.attendee_changed_info.nickname
                                }}</span>
                                {{ $t('invited_join_group_tip2') }}
                            </div>
                            <div
                                v-if="message.attendee_changed_info.attendee_join_type == 3"
                                class="system_notify longwrap"
                            >
                                <span @click.stop="openVisitingCard(message, 5)">{{
                                    message.attendee_changed_info.nickname
                                }}</span>
                                {{ $t('scaned_tip') }}
                                <span @click.stop="openVisitingCard(message, 6)">{{
                                    conversation.attendeeList &&
                                    conversation.attendeeList["attendee_" + message.attendee_changed_info.inviter_id]
                                        ? conversation.attendeeList[
                                              "attendee_" + message.attendee_changed_info.inviter_id
                                          ].nickname
                                        : "."
                                }}</span>
                                {{ $t('join_group_by_qrcode_tips') }}
                            </div>
                            <div
                                v-if="message.attendee_changed_info.attendee_join_type == 4"
                                class="system_notify longwrap"
                            >
                                <span @click.stop="openVisitingCard(message, 5)">{{
                                    message.attendee_changed_info.nickname
                                }}</span>
                                {{ $t('wechat_invite_prefix') }}
                                <span @click.stop="openVisitingCard(message, 6)">{{
                                    conversation.attendeeList &&
                                    conversation.attendeeList["attendee_" + message.attendee_changed_info.inviter_id]
                                        ? conversation.attendeeList[
                                              "attendee_" + message.attendee_changed_info.inviter_id
                                          ].nickname
                                        : "."
                                }}</span>
                                {{ $t('wechat_invite_affix') }}
                            </div>
                        </div>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.SYS_KICKOUT_ATTENDEE"
                            class="notify_wrapper"
                        >
                            <div
                                v-if="
                                    message.attendee_changed_info &&
                                    message.attendee_changed_info.attendee_detach_type == 1
                                "
                                class="system_notify longwrap"
                            >
                                {{ message.attendee_changed_info.nickname }}
                                {{ $t('active_exit_group_tip') }}
                            </div>
                            <div
                                v-if="
                                    message.attendee_changed_info &&
                                    message.attendee_changed_info.attendee_detach_type == 2
                                "
                                class="system_notify longwrap"
                            >
                                <span @click.stop="openVisitingCard(message, 6)">{{
                                    conversation.attendeeList &&
                                    conversation.attendeeList["attendee_" + message.attendee_changed_info.inviter_id]
                                        ? conversation.attendeeList[
                                              "attendee_" + message.attendee_changed_info.inviter_id
                                          ].nickname
                                        : "."
                                }}</span>
                                {{ $t('remove_group_tip1') }}
                                <span @click.stop="openVisitingCard(message, 5)">{{
                                    message.attendee_changed_info.nickname
                                }}</span>
                                {{ $t('remove_group_tip2') }}
                            </div>
                        </div>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.SYS_START_RT_VOICE"
                            class="notify_wrapper"
                        >
                            <div class="system_notify longwrap">
                                {{ message.nickname }}
                                {{ $t('request_voice_system_tip') }}
                            </div>
                        </div>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.SYS_STOP_RT_VOICE"
                            class="notify_wrapper"
                        >
                            <div class="system_notify longwrap">
                                {{ message.nickname }}
                                {{ $t('close_voice_system_tip') }}
                            </div>
                        </div>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION"
                            class="notify_wrapper"
                        >
                            <div class="system_notify longwrap">
                                {{ message.nickname }}
                                {{ $t('request_realtime_video_tip') }}
                            </div>
                        </div>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION"
                            class="notify_wrapper"
                        >
                            <div class="system_notify longwrap">
                                {{ message.nickname }}
                                {{ $t('close_realtime_video_tip') }}
                                <span @click="openLiveDetailDialog(message)">{{ $t('view_details') }}</span>
                            </div>
                        </div>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.MULTICENTER_REJECT"
                            class="notify_wrapper"
                        >
                            <div class="system_notify longwrap">
                                {{ $t('multicenter_reject_tip') }}
                            </div>
                        </div>
                        <!--systemConfig.msg_type.WITHDRAW 撤回-->
                        <div v-else-if="message.msg_type == systemConfig.msg_type.WITHDRAW" class="notify_wrapper">
                            <div class="system_notify longwrap" v-if="message.sender_id === user.uid">
                                {{ $t('revocation_message_by_self')
                                }}<span
                                    v-show="
                                        (isShowReEdit(message) ||
                                            presentTime - new Date(message.send_ts).getTime() <= 180000) &&
                                        message.msg_body
                                    "
                                    @click="reEditMessage(message)"
                                    :class="['re_edit_wrapper', `re_edit_${message.gmsg_id}`]"
                                    >{{ $t('re_edit') }}</span
                                >
                            </div>
                            <div class="system_notify longwrap" v-else>
                                {{ message.nickname }}
                                {{ $t('revocation_message_by_other') }}
                            </div>
                        </div>
                        <v-touch
                            v-else-if="
                                message.msg_type == systemConfig.msg_type.AI_ANALYZE && message.sender_id != aiAnalyzeId
                            "
                            :key="'AI_ANALYZE_' + message.gmsg_id"
                            v-popover.top="{
                                name: 'menu',
                                chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                callback: () => {
                                    showTooltipsMenu(message);
                                },
                            }"
                            class="system_message"
                        >
                            <div
                                class="ai_images_wrap"
                                v-if="message.ai_analyze && message.ai_analyze.messages"
                                @click="openAnalyzeGallery(message, 0)"
                            >
                                <template v-if="!isAllAiMessageDelete(message)">
                                    <common-image :fileItem="message.ai_analyze.messages[0]"></common-image>
                                    <i v-if="message.ai_analyze.messages.length > 1" class="iconfont icon-images"></i>
                                </template>
                                <div v-else class="error_img_msg">
                                    <div class="message_image_content">
                                        <img src="static/resource/images/default.png" />
                                    </div>
                                    <div class="delete_resource_tips">
                                        <p>{{ $t('ref_res_expired') }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="ai_message_right">
                                <p>
                                    {{ message.nickname }}
                                    {{ $t('share_analyze_result') }}
                                </p>
                                <div
                                    v-if="message.ai_analyze && message.ai_analyze.report.error == undefined"
                                    class="tip"
                                >
                                    {{ $t('analyzing') }}
                                </div>
                                <div v-else-if="message.ai_analyze && message.ai_analyze.report.error == 0" class="tip">
                                    {{ $t('analyze_result_tip') }}
                                    <template v-if="message.ai_analyze.type == aiPresetData.typeIndex.breast">
                                        <div v-if="message.ai_analyze.report.tumor_probability">
                                            <p>
                                                {{ $t('bengin_probability')
                                                }}{{
                                                    (message.ai_analyze.report.tumor_probability[0] * 100).toFixed(2) +
                                                    "%"
                                                }}
                                            </p>
                                            <p>
                                                {{ $t('malignant_probability')
                                                }}{{
                                                    (message.ai_analyze.report.tumor_probability[1] * 100).toFixed(2) +
                                                    "%"
                                                }}
                                            </p>
                                        </div>
                                        <p v-if="!message.ai_analyze.report.detected_tumor">{{ $t('no_ai_result') }}</p>
                                        <p v-if="message.ai_analyze.report.mark_list">{{ $t('see_trace_tip') }}</p>
                                    </template>
                                    <template v-else>
                                        <div v-if="message.ai_analyze.status">
                                            <p>{{ $t('obstetric_qc.nalysis_completed') }}</p>
                                        </div>
                                        <div v-else>
                                            <p>{{ $t('no_ai_result') }}</p>
                                        </div>
                                    </template>
                                </div>
                                <div v-else class="analyze_tip">
                                    {{ $t('analyze_result_tip') }}
                                    <p>{{ $t('no_ai_result') }}</p>
                                    <!-- <p>
                                        {{ $t('analyze_fail_tip') }}:
                                        {{ message.ai_analyze && message.ai_analyze.report.error_code }}
                                    </p> -->
                                </div>
                                <p class="right_tip">{{ $t('from_ai_analyze') }}</p>
                            </div>
                        </v-touch>
                        <div
                            v-else-if="message.msg_type == systemConfig.msg_type.SYS_CONFERENCE_PLAN"
                            class="notify_wrapper"
                        >
                            <div
                                v-if="message.tip_type == systemConfig.conference_plan_tip_type.New"
                                class="system_notify longwrap"
                            >
                                {{ message.nickname }}
                                {{ $t('reserved_conference_tip')
                                }}{{ message.conference_plan && message.conference_plan.subject }}
                            </div>
                            <div
                                v-else-if="message.tip_type == systemConfig.conference_plan_tip_type.Prepare"
                                class="system_notify longwrap"
                            >
                                {{ $t('conference_begin_tip')
                                }}{{ message.conference_plan && message.conference_plan.subject }}
                            </div>
                            <div
                                v-else-if="message.tip_type == systemConfig.conference_plan_tip_type.Cancel"
                                class="system_notify longwrap"
                            >
                                {{ message.nickname }}
                                {{ $t('delete_conference_tip')
                                }}{{ message.conference_plan && message.conference_plan.subject }}
                            </div>
                        </div>
                        <div
                            v-else
                            class="message_item clearfix"
                            :class="{ self_chat: message.sender_id == user.uid }"
                            :ref="'message-' + message.gmsg_id"
                        >
                            <div class="message_item_box">
                                <v-touch @press="pressAvatar(message)" class="avatar_wraper">
                                    <mr-avatar
                                        :url="getLocalAvatar(message)"
                                        :origin_url="message.avatar"
                                        :showOnlineState="false"
                                        :key="message.avatar"
                                        @click.native.stop="openVisitingCard(message, 1)"
                                    ></mr-avatar>
                                </v-touch>
                                <div class="message_item_wrapper">
                                    <div class="message_item_name longwrap" v-if="!conversation.is_single_chat">
                                        {{ attendeeMap[message.sender_id] || message.nickname }}
                                    </div>
                                    <div
                                        v-if="message.sending"
                                        v-loading="message.sending"
                                        class="sending_spinner"
                                        :key="message.gmsg_id || message.tmp_gmsg_id"
                                    ></div>
                                    <!-- 文本消息内容 -->
                                    <div
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                        v-if="message.msg_type == systemConfig.msg_type.Text"
                                        v-html="message.msg_body"
                                        class="message_item_content text_message"
                                        :style="textStyle(message.msg_body)"
                                        @click="clickTextMsg($event, message)"
                                        :key="'Text_' + message.gmsg_id"
                                    ></div>
                                    <template
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.Image ||
                                            message.msg_type == systemConfig.msg_type.OBAI ||
                                            message.msg_type == systemConfig.msg_type.Frame
                                        "
                                    >
                                        <div
                                            v-if="getResourceTempState(message.resource_id) === 1"
                                            class="message_item_content no_padding"
                                            :class="[
                                                { iworks_msg: message.protocol_view_name },
                                                { img_msg: !message.protocol_view_name },
                                            ]"
                                        >
                                            <div
                                                v-popover.top="{
                                                    name: 'menu',
                                                    chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                                    callback: () => {
                                                        showTooltipsMenu(message);
                                                    },
                                                }"
                                                @click="clickImageHandle(message, index)"
                                                :key="'image_' + message.gmsg_id"
                                                class="message_image_content"
                                            >
                                                <common-image :fileItem="message"></common-image>
                                            </div>
                                            <i
                                                class="iconfont icon-comment1"
                                                v-show="
                                                    getCommentNum(
                                                        gallery.commentObj[message.resource_id] &&
                                                            gallery.commentObj[message.resource_id].comment_list
                                                    )
                                                "
                                            >
                                                <span>{{
                                                    getCommentNum(
                                                        gallery.commentObj[message.resource_id] &&
                                                            gallery.commentObj[message.resource_id].comment_list
                                                    )
                                                }}</span>
                                            </i>

                                            <div
                                                class="uploading_wrapper2"
                                                v-if="message.uploading"
                                                @click.stop="handleUploadStatus(message)"
                                            >
                                                <template v-if="message.uploadId">
                                                    <van-circle
                                                        v-model="message.percent"
                                                        :rate="message.percent"
                                                        :text="!isSupportOssPause ? `${message.percent}%` : ''"
                                                        size="4rem"
                                                        layer-color="#fff"
                                                        :stroke-width="80"
                                                    />
                                                    <van-icon
                                                        name="upgrade"
                                                        size="32"
                                                        v-if="message.pauseUpload && !message.uploadFail"
                                                    />
                                                    <van-icon
                                                        name="pause-circle-o"
                                                        size="32"
                                                        v-if="
                                                            !message.pauseUpload &&
                                                            !message.uploadFail &&
                                                            isSupportOssPause
                                                        "
                                                    />
                                                    <van-icon name="warning-o" color="red" v-if="message.uploadFail" />
                                                    <van-icon
                                                        name="close"
                                                        size="1rem"
                                                        color="red"
                                                        @click="handleCancelUpload(message)"
                                                    />
                                                </template>
                                                <template v-else>
                                                    <img src="static/resource/images/loading.gif" />
                                                </template>
                                            </div>
                                            <van-checkbox-group v-if="multi_select_mode" v-model="multiImageList">
                                                <van-checkbox :name="message" checked-color="#00c59d"></van-checkbox>
                                            </van-checkbox-group>
                                            <div
                                                @click.stop="showProtocol(message)"
                                                v-if="message.protocol_name && message.protocol_view_name"
                                                class="iworks_protocol"
                                            >
                                                <p class="iworks">
                                                    <i class="iconfont svg_icon_works icon-piece2-cop"></i>
                                                    <span>iWorks：{{ message.protocol_name }}</span>
                                                </p>
                                                <p class="line"></p>
                                                <p class="section">
                                                    {{ $t('view_txt') }}：{{ message.protocol_view_name }}
                                                </p>
                                            </div>
                                        </div>
                                        <div v-else class="error_img_msg">
                                            <div class="message_image_content">
                                                <img src="static/resource/images/default.png" />
                                            </div>
                                            <div class="comment_right">
                                                <p>{{ $t('ref_res_expired') }}</p>
                                            </div>
                                        </div>
                                    </template>
                                    <template
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.Video ||
                                            message.msg_type == systemConfig.msg_type.Cine
                                        "
                                    >
                                        <div
                                            v-if="getResourceTempState(message.resource_id) === 1"
                                            class="message_item_content video_msg no_padding"
                                            :class="{ is_iworks: message.protocol_name && message.protocol_view_name }"
                                        >
                                            <i
                                                class="iconfont icon-comment1"
                                                v-show="
                                                    gallery.commentObj[message.resource_id] &&
                                                    gallery.commentObj[message.resource_id].comment_list.length
                                                "
                                            >
                                                <span>{{
                                                    gallery.commentObj[message.resource_id] &&
                                                    gallery.commentObj[message.resource_id].comment_list.length
                                                }}</span>
                                            </i>
                                            <div
                                                @click="clickImageHandle(message, index)"
                                                v-popover.top="{
                                                    name: 'menu',
                                                    chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                                    callback: () => {
                                                        showTooltipsMenu(message);
                                                    },
                                                }"
                                                :key="'video_' + message.gmsg_id"
                                                class="message_image_content"
                                            >
                                                <common-image :fileItem="message" size="large"></common-image>
                                                <!-- <span v-if="resourceUnreviewList.image_list[message.resource_id]" class="unread_tip"></span> -->
                                            </div>
                                            <van-checkbox-group v-if="multi_select_mode" v-model="multiImageList">
                                                <van-checkbox :name="message" checked-color="#00c59d"></van-checkbox>
                                            </van-checkbox-group>
                                            <div
                                                class="uploading_wrapper2"
                                                v-if="message.uploading"
                                                @click="handleUploadStatus(message)"
                                            >
                                                <template v-if="message.uploadId">
                                                    <van-circle
                                                        v-model="message.percent"
                                                        :rate="message.percent"
                                                        :text="!isSupportOssPause ? `${message.percent}%` : ''"
                                                        size="4rem"
                                                        layer-color="#fff"
                                                        :stroke-width="80"
                                                    />
                                                    <van-icon
                                                        name="upgrade"
                                                        size="32"
                                                        v-if="message.pauseUpload && !message.uploadFail"
                                                    />
                                                    <van-icon
                                                        name="pause-circle-o"
                                                        size="32"
                                                        v-if="
                                                            !message.pauseUpload &&
                                                            !message.uploadFail &&
                                                            isSupportOssPause
                                                        "
                                                    />
                                                    <van-icon name="warning-o" color="red" v-if="message.uploadFail" />
                                                    <van-icon
                                                        name="close"
                                                        size="1rem"
                                                        color="red"
                                                        @click="handleCancelUpload(message)"
                                                    />
                                                </template>
                                                <template v-else>
                                                    <img src="static/resource/images/loading.gif" />
                                                </template>
                                            </div>
                                            <div
                                                v-if="message.protocol_name && message.protocol_view_name"
                                                class="iworks_protocol"
                                            >
                                                <p class="iworks">
                                                    <i class="iconfont svg_icon_works icon-piece2-copy"></i>
                                                    <span>iWorks：{{ message.protocol_name }}</span>
                                                </p>
                                                <p class="line"></p>
                                                <p class="section">
                                                    {{ $t('view_txt') }}：{{ message.protocol_view_name }}
                                                </p>
                                            </div>
                                        </div>
                                        <div v-else class="error_img_msg">
                                            <div class="message_image_content">
                                                <img src="static/resource/images/default.png" />
                                            </div>
                                            <div class="comment_right">
                                                <p>{{ $t('ref_res_expired') }}</p>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="message.msg_type == systemConfig.msg_type.RealTimeVideoReview">
                                        <div
                                            v-if="getResourceTempState(message.resource_id) === 1"
                                            :key="'RealTimeVideoReview_' + message.gmsg_id"
                                            v-popover.top="{
                                                name: 'menu',
                                                chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                                callback: () => {
                                                    showTooltipsMenu(message);
                                                },
                                            }"
                                            class="message_item_content no_padding"
                                        >
                                            <ReviewMsg
                                                :message="message"
                                                :ref="`reviewItem${message.gmsg_id}`"
                                                @openGallery="openGallery(message, index)"
                                            ></ReviewMsg>
                                        </div>
                                        <div v-else class="error_img_msg">
                                            <div class="message_image_content">
                                                <img src="static/resource/images/default.png" />
                                            </div>
                                            <div class="comment_right">
                                                <p>{{ $t('ref_res_expired') }}</p>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="message.msg_type == systemConfig.msg_type.VIDEO_CLIP">
                                        <div
                                            v-if="getResourceTempState(message.resource_id) === 1"
                                            :key="'VIDEO_CLIP_' + message.gmsg_id"
                                            v-popover.top="{
                                                name: 'menu',
                                                chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                                callback: () => {
                                                    showTooltipsMenu(message);
                                                },
                                            }"
                                            class="message_item_content no_padding"
                                        >
                                            <VideoClipMsg
                                                :message="message"
                                                :ref="`videoClipItem${message.gmsg_id}`"
                                                @openGallery="openGallery(message, index)"
                                            ></VideoClipMsg>
                                        </div>
                                        <div v-else class="error_img_msg">
                                            <div class="message_image_content">
                                                <img src="static/resource/images/default.png" />
                                            </div>
                                            <div class="comment_right">
                                                <p>{{ $t('ref_res_expired') }}</p>
                                            </div>
                                        </div>
                                    </template>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.EXAM_IMAGES"
                                        class="message_item_content exam_msg no_padding"
                                        :key="'EXAM_IMAGES_' + message.gmsg_id"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <exam-msg
                                            :message="message"
                                            :isReload="isReload"
                                            @showExamMsgMenu="showExamMsgMenu"
                                        ></exam-msg>
                                    </div>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.Sound"
                                        class="message_item_content sound_msg"
                                        :class="{ playing: message.gmsg_id == soundingMsgId }"
                                        @click="playSound(message.gmsg_id)"
                                        :style="getSoundStyle(message)"
                                        :key="'Sound_' + message.gmsg_id"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <img
                                            v-if="message.uploading || message.downloading"
                                            src="static/resource/images/loading.gif"
                                        />
                                        <span class="text" v-else> {{ Math.ceil(message.duration / 1000) }}" </span>
                                        <span class="sound_img" v-if="message.sender_id == user.uid"></span>
                                        <span class="sound_img" v-else></span>
                                        <i class="unread_tip" v-if="false"></i>
                                        <!--<audio :src='getSoundSource(message.url)' ></audio>-->
                                    </div>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.File"
                                        class="message_item_content file_msg"
                                        @click.stop="downLoadFileByDefaultBrowser(message)"
                                        :key="'File_' + message.gmsg_id"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <div :class="['file_type_icon', FileType(message)]">
                                            {{ message.show_file_type }}
                                        </div>
                                        <div class="text_wrap">
                                            <p class="file_name">{{ message.file_name }}</p>
                                            <span class="file_size" v-if="message.resource_file_size">{{
                                                formatSize(message.resource_file_size)
                                            }}</span>
                                            <span class="expired_status" v-if="checkFileExpired(message)">{{
                                                $t('group_apply_expire')
                                            }}</span>
                                            <span class="file_expires_time" v-if="message.resource_expired_at"
                                                >{{ $t('expiration_date') }}：{{
                                                    formatTime(message.resource_expired_at)
                                                }}</span
                                            >
                                        </div>
                                        <div
                                            class="uploading_wrapper2"
                                            v-if="message.uploading"
                                            @click="handleUploadStatus(message)"
                                        >
                                            <template v-if="message.uploadId">
                                                <van-circle
                                                    v-model="message.percent"
                                                    :rate="message.percent"
                                                    :text="!isSupportOssPause ? `${message.percent}%` : ''"
                                                    size="4rem"
                                                    layer-color="#fff"
                                                    :stroke-width="80"
                                                />
                                                <van-icon
                                                    name="upgrade"
                                                    size="32"
                                                    v-if="message.pauseUpload && !message.uploadFail"
                                                />
                                                <van-icon
                                                    name="pause-circle-o"
                                                    size="32"
                                                    v-if="
                                                        !message.pauseUpload && !message.uploadFail && isSupportOssPause
                                                    "
                                                />
                                                <van-icon name="warning-o" color="red" v-if="message.uploadFail" />
                                                <van-icon
                                                    name="close"
                                                    size="1rem"
                                                    color="red"
                                                    @click="handleCancelUpload(message)"
                                                />
                                            </template>
                                            <template v-else>
                                                <img src="static/resource/images/loading.gif" />
                                            </template>
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.IWORKS_PROTOCOL"
                                        class="message_item_content iworks_protocol_msg"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <div class="iworks_icon">
                                            <i class="iconfont svg_icon_iworks icon-piece2-copy"></i>
                                            <span>iWorks</span>
                                        </div>
                                        <div class="text_wrap">
                                            <p>{{ getIworksProtocolInfo(message, "name") }}</p>
                                            <div class="iworks-fields">
                                                <div class="iworks-field">
                                                    {{ getIworksProtocolInfo(message, "Description") }}
                                                </div>
                                                <div class="iworks-field">
                                                    {{ getIworksProtocolInfo(message, "Author") }}
                                                </div>
                                                <div class="iworks-field">
                                                    {{ getIworksProtocolInfo(message, "Version") }}
                                                </div>
                                                <div class="iworks-field">
                                                    {{ getIworksProtocolInfo(message, "CreateDate") }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION
                                        "
                                        class="message_item_content"
                                    >
                                        {{ $t('request_realtime_video') }}
                                    </div>
                                    <div
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION
                                        "
                                        class="message_item_content"
                                    >
                                        {{ $t('close_realtime_video') }}
                                    </div>
                                    <div
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.COMMENT ||
                                            message.msg_type == systemConfig.msg_type.TAG
                                        "
                                        class="message_item_content no_padding clearfix"
                                        :class="message.comment_id ? 'iworks_comment' : 'iworks_tag'"
                                        @click="view_comment(message, index)"
                                        :key="'COMMENT_' + message.gmsg_id"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <div class="message_image_content">
                                            <template
                                                v-if="message.img_type_ex == systemConfig.msg_type.RealTimeVideoReview"
                                            >
                                                <template v-if="message.coverUrl">
                                                    <img
                                                        :src="limitImageSize(message.coverUrl, 300)"
                                                        alt=""
                                                        srcset=""
                                                    />
                                                    <i class="iconfont svg_icon_play icon-videoplay"></i>
                                                </template>
                                                <img src="static/resource/images/realtime_background.png" v-else />
                                                <div class="diff_time">{{ formatDurationTime(message.duration) }}</div>
                                                <div class="review_item_read_subject">
                                                    {{ getRecordSubject(message) }}
                                                </div>
                                                <div class="review_item_read_time">
                                                    {{ formatTime(message.start_ts) }}
                                                </div>
                                            </template>
                                            <template
                                                v-else-if="message.img_type_ex == systemConfig.msg_type.VIDEO_CLIP"
                                            >
                                                <template v-if="message.coverUrl">
                                                    <img
                                                        :src="limitImageSize(message.coverUrl, 300)"
                                                        alt=""
                                                        srcset=""
                                                    />
                                                    <i class="iconfont svg_icon_play icon-videoplay"></i>
                                                </template>
                                                <img src="static/resource/images/realtime_background.png" v-else />
                                                <div class="diff_time">{{ formatDurationTime(message.duration) }}</div>
                                                <div class="review_item_read_subject">
                                                    {{ getClipSubject(message) }}
                                                </div>
                                                <div class="review_item_read_time">
                                                    {{ formatTime(message.start_ts) }}
                                                </div>
                                            </template>
                                            <template v-else>
                                                <common-image :fileItem="message"></common-image>
                                                <p class="patient_info" v-if="message.patientInfo">
                                                    <span
                                                        v-if="
                                                            message.patientInfo && message.patientInfo.patient_name_str
                                                        "
                                                        >{{ $t('exam_patient_text')
                                                        }}{{ message.patientInfo.patient_name_str }}</span
                                                    >
                                                    <span>{{ message.patientInfo.patient_age_str }}</span>
                                                    <span>{{ message.patientInfo.patient_sex_str }}</span>
                                                </p>
                                            </template>
                                        </div>

                                        <div
                                            class="comment_right"
                                            v-if="message.msg_type == systemConfig.msg_type.COMMENT"
                                        >
                                            <span>{{ $t('add_comment_msg_text') }}</span>
                                            <p>
                                                <span class="commentor">{{ message.nickname }}</span>
                                                <span>{{ message.comment }}</span>
                                            </p>
                                        </div>
                                        <div
                                            class="comment_right"
                                            v-else-if="
                                                message.msg_type == systemConfig.msg_type.TAG && message.action == 1
                                            "
                                        >
                                            <span>{{ $t('add_tag_msg_text') }}</span>
                                            <p>
                                                <span class="wrap">{{ message.tags }}</span>
                                            </p>
                                        </div>
                                        <div
                                            class="comment_right"
                                            v-else-if="
                                                message.msg_type == systemConfig.msg_type.TAG && message.action == 2
                                            "
                                        >
                                            <span>{{ $t('delete_tag_msg_text') }}</span>
                                            <p>
                                                <span class="wrap">{{ message.tags }}</span>
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.EXPIRATION_RES"
                                        class="error_img_msg"
                                    >
                                        <div class="message_image_content">
                                            <img src="static/resource/images/default.png" />
                                        </div>
                                        <div class="comment_right">
                                            <p>{{ $t('ref_res_expired') }}</p>
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.AI_ANALYZE"
                                        :key="'AI_ANALYZE_' + message.gmsg_id"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                        class="message_item_content"
                                    >
                                        <div
                                            class="ai_images_wrap"
                                            v-if="message.ai_analyze && message.ai_analyze.messages"
                                            @click="openAnalyzeGallery(message, 0)"
                                        >
                                            <template v-if="!isAllAiMessageDelete(message)">
                                                <common-image :fileItem="message.ai_analyze.messages[0]"></common-image>
                                                <i
                                                    v-if="message.ai_analyze.messages.length > 1"
                                                    class="iconfont icon-images"
                                                ></i>
                                            </template>
                                            <div v-else class="error_img_msg">
                                                <div class="message_image_content">
                                                    <img src="static/resource/images/default.png" />
                                                </div>
                                                <div class="delete_resource_tips">
                                                    <p>{{ $t('ref_res_expired') }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="message.ai_analyze && message.ai_analyze.report.error == undefined">
                                            {{ $t('analyzing') }}
                                        </div>
                                        <div
                                            v-else-if="message.ai_analyze && message.ai_analyze.report.error == 0"
                                            class="comment_right"
                                        >
                                            {{ $t('analyze_result_tip') }}
                                            <template v-if="message.ai_analyze.type == aiPresetData.typeIndex.breast">
                                                <div v-if="message.ai_analyze.report.tumor_probability">
                                                    <p>
                                                        {{ $t('bengin_probability')
                                                        }}{{
                                                            (
                                                                message.ai_analyze.report.tumor_probability[0] * 100
                                                            ).toFixed(2) + "%"
                                                        }}
                                                    </p>
                                                    <p>
                                                        {{ $t('malignant_probability')
                                                        }}{{
                                                            (
                                                                message.ai_analyze.report.tumor_probability[1] * 100
                                                            ).toFixed(2) + "%"
                                                        }}
                                                    </p>
                                                </div>
                                                <p v-if="!message.ai_analyze.report.detected_tumor">
                                                    {{ $t('no_ai_result') }}
                                                </p>
                                                <p v-if="message.ai_analyze.report.mark_list" class="tip">
                                                    {{ $t('see_trace_tip') }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <div v-if="message.ai_analyze.status">
                                                    <p>{{ $t('obstetric_qc.nalysis_completed') }}---</p>
                                                </div>
                                                <div v-else>
                                                    <p>{{ $t('no_ai_result') }}</p>
                                                </div>
                                            </template>
                                        </div>
                                        <div v-else class="analyze_tip">
                                            {{ $t('analyze_result_tip') }}
                                            <p>{{ $t('no_ai_result') }}</p>
                                            <!-- <p>
                                            {{ $t('analyze_fail_tip') }}:
                                            {{ message.ai_analyze && message.ai_analyze.report.error_code }}
                                        </p> -->
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="message.msg_type == systemConfig.msg_type.IWORKS_SCORE"
                                        class="message_item_content"
                                    >
                                        <div
                                            class="ai_images_wrap"
                                            v-if="message.ai_result && message.ai_result.imgUrl"
                                            @click="openIworksGallery(message)"
                                        >
                                            <img
                                                class="message_image comment_thumbnail"
                                                :src="limitImageSize(message.ai_result.imgUrl, 300)"
                                            />
                                        </div>
                                        <div v-if="message.ai_result && !message.ai_result.isError">
                                            <p class="analyze_tip">
                                                {{ $t('iworks_score_label') }}{{ message.ai_result.result[0].score }}
                                            </p>
                                        </div>
                                        <div v-else>
                                            <p class="analyze_tip">{{ $t('iworks_fail_label') }}</p>
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.LIVE_INVITE && message.liveInfo
                                        "
                                        class="message_item_content live_image_box"
                                        :key="'LIVE_INVITE_' + message.gmsg_id"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <div class="card-image">
                                            <img
                                                :src="limitImageSize(message.liveInfo.cover_image, 300)"
                                                v-if="message.liveInfo.cover_image"
                                            />
                                            <img src="static/resource/images/live_cover.png" v-else />
                                        </div>
                                        <div class="card-content">
                                            <div class="card-title">{{ message.liveInfo.topic }}</div>
                                            <div class="card-desc">{{ message.liveInfo.description }}</div>
                                        </div>
                                        <div class="card-date">
                                            <div class="card-date-text">
                                                <i class="el-icon-date"></i>{{ formatTime(message.liveInfo.start_ts) }}
                                            </div>
                                            <div class="card-date-text">
                                                <i class="el-icon-date"></i>{{ formatTime(message.liveInfo.end_ts) }}
                                            </div>
                                        </div>
                                        <div class="card-info">
                                            <span class="textEllipsis"
                                                >{{ $t('moderator') }}:{{ message.liveInfo.creator_name }}</span
                                            >
                                        </div>
                                        <div class="card-tips">
                                            <p
                                                class="card-tips-status0"
                                                v-if="
                                                    message.liveInfo.status === systemConfig.liveManagement.waiting &&
                                                    !message.liveInfo.is_will_start
                                                "
                                            >
                                                {{ $t('waiting') }}
                                            </p>
                                            <p
                                                class="card-tips-status1"
                                                v-if="
                                                    message.liveInfo.status === systemConfig.liveManagement.waiting &&
                                                    message.liveInfo.is_will_start
                                                "
                                            >
                                                {{ $t('begin_in_minute') }}
                                            </p>
                                            <p
                                                class="card-tips-status2"
                                                v-if="message.liveInfo.status === systemConfig.liveManagement.starting"
                                            >
                                                {{ $t('live_broadcasting') }}
                                            </p>
                                            <p
                                                class="card-tips-status3"
                                                v-if="message.liveInfo.status === systemConfig.liveManagement.end"
                                            >
                                                {{ $t('live_broadcast_end') }}
                                            </p>
                                            <p
                                                class="card-tips-status3"
                                                v-if="message.liveInfo.status === systemConfig.liveManagement.cancel"
                                            >
                                                {{ $t('live_broadcast_cancel') }}
                                            </p>
                                            <p
                                                class="card-tips-link"
                                                @click="quickEntry(message)"
                                                v-if="message.liveInfo.status !== systemConfig.liveManagement.cancel"
                                            >
                                                {{ $t('quick_entry') }} >>
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        v-else-if="
                                            message.msg_type == systemConfig.msg_type.HOMEWORK_DETAIL ||
                                            message.msg_type == systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL
                                        "
                                        class="message_item_content homework_detail_msg"
                                        @click="clickCloudExam(message)"
                                        v-popover.top="{
                                            name: 'menu',
                                            chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                            callback: () => {
                                                showTooltipsMenu(message);
                                            },
                                        }"
                                    >
                                        <div class="homework_detail">
                                            <div class="homework_title">
                                                <img src="static/resource/images/homework_title.png" alt="" srcset="" />
                                                <p>{{ $t('cloud_exam') }}</p>
                                            </div>
                                            <div
                                                class="homework_wraper"
                                                v-if="
                                                    (message.assignmentInfo && message.assignmentInfo.paperInfo) ||
                                                    message.paperInfo
                                                "
                                            >
                                                <div class="homework_left">
                                                    <div
                                                        v-if="message.msg_type == systemConfig.msg_type.HOMEWORK_DETAIL"
                                                        class="sub_title"
                                                    >
                                                        {{ message.assignmentInfo.paperInfo.title }}
                                                    </div>
                                                    <div v-else class="sub_title">{{ message.paperInfo.title }}</div>
                                                    <div
                                                        v-if="message.msg_type == systemConfig.msg_type.HOMEWORK_DETAIL"
                                                        class="content"
                                                    >
                                                        <div>
                                                            {{ $t('author') }}：{{
                                                                message.assignmentInfo.paperInfo.author
                                                            }}
                                                        </div>
                                                        <div class="">
                                                            {{ $t('deadline') }}：{{
                                                                message.assignmentInfo.dueTime | showData
                                                            }}
                                                        </div>
                                                    </div>
                                                    <div v-else class="content">
                                                        {{ $t('author') }}：{{ message.paperInfo.author }}
                                                    </div>
                                                </div>
                                                <div class="homework_right">
                                                    <template
                                                        v-if="
                                                            message.msg_type == systemConfig.msg_type.HOMEWORK_DETAIL &&
                                                            message.assignmentInfo &&
                                                            message.assignmentInfo.paperInfo &&
                                                            message.assignmentInfo.paperInfo.contentType
                                                        "
                                                    >
                                                        <img
                                                            class="title_icon"
                                                            :src="`static/resource/images/homework_type${message.assignmentInfo.paperInfo.contentType}.png`"
                                                        />
                                                        <p>
                                                            {{
                                                                $t(
                                                                    "homework_type" +
                                                                        message.assignmentInfo.paperInfo.contentType
                                                                )
                                                            }}
                                                        </p>
                                                    </template>
                                                    <template
                                                        v-else-if="
                                                            message.msg_type ==
                                                                systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL &&
                                                            message.paperInfo &&
                                                            message.paperInfo.contentType
                                                        "
                                                    >
                                                        <img
                                                            class="title_icon"
                                                            :src="`static/resource/images/homework_type${message.paperInfo.contentType}.png`"
                                                        />
                                                        <p>
                                                            {{ $t("homework_type" + message.paperInfo.contentType) }}
                                                        </p>
                                                    </template>
                                                    <template v-else>
                                                        <img
                                                            class="title_icon"
                                                            src="static/resource/images/homework_type5.png"
                                                        />
                                                        <p>{{ $t('homework_type5') }}</p>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="message_item_content">
                                        {{ $t('unsupport_msg_type') }}：{{ message.msg_type }}
                                    </div>
                                </div>
                                <span
                                    class="sending_spinner"
                                    v-if="message.sending"
                                    :key="message.gmsg_id || message.tmp_gmsg_id"
                                >
                                    <div
                                        class="van-spinner-snake"
                                        style="
                                            border-top-color: rgb(0, 197, 157);
                                            border-left-color: rgb(0, 197, 157);
                                            border-bottom-color: rgb(0, 197, 157);
                                        "
                                    ></div>
                                </span>
                                <i
                                    v-if="message.sendFail"
                                    @click="resend(index)"
                                    class="send_fail icon iconfont icon-warning-o"
                                ></i>
                            </div>

                            <!-- 添加引用消息显示部分 - 显示在文本内容之后 -->
                            <div
                                v-if="message.msg_type == systemConfig.msg_type.Text && message.quote_message"
                                class="quote-message"
                                :class="{
                                    'quote-right': message.sender_id == user.uid,
                                    'quote-left': message.sender_id != user.uid,
                                }"
                                v-popover.top="{
                                    name: 'menu',
                                    chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                                    callback: () => {
                                        showQuoteMenu(message);
                                    },
                                }"
                            >
                                <div
                                    v-if="
                                        message.quote_message.hasOwnProperty('been_withdrawn') &&
                                        message.quote_message.been_withdrawn === 1
                                    "
                                    class="quote-content-message"
                                >
                                    {{ $t('quoted_content_has_been_deleted') }}
                                </div>
                                <div
                                    v-else-if="
                                        message.quote_message.hasOwnProperty('been_withdrawn') &&
                                        message.quote_message.been_withdrawn === 2
                                    "
                                    class="quote-content-message"
                                >
                                    {{ $t('quoted_content_has_been_withdrawn') }}
                                </div>
                                <template v-else>
                                    <div class="quote-sender">
                                        {{ message.quote_message.nickname || $t('user') }}&#160;:&#160;
                                    </div>
                                    <div
                                        v-if="
                                            [
                                                systemConfig.msg_type.Image,
                                                systemConfig.msg_type.Frame,
                                                systemConfig.msg_type.OBAI,
                                            ].includes(message.quote_message.msg_type)
                                        "
                                        class="quote-content-message image-content"
                                        @click.stop="openQuotedGallery(message.quote_message)"
                                    >
                                        <img v-if="message.quote_message.url" :src="message.quote_message.url" />
                                        <span>{{ $t('msg_type_image') }}</span>
                                    </div>
                                    <!-- 视频类型引用 -->
                                    <div
                                        v-else-if="
                                            [
                                                systemConfig.msg_type.Video,
                                                systemConfig.msg_type.Cine,
                                                systemConfig.msg_type.RealTimeVideoReview,
                                                systemConfig.msg_type.VIDEO_CLIP,
                                            ].includes(message.quote_message.msg_type)
                                        "
                                        class="quote-content-message video-content"
                                        @click.stop="openQuotedGallery(message.quote_message)"
                                    >
                                        <div class="video-thumbnail">
                                            <img
                                                v-if="message.quote_message.coverUrl || message.quote_message.url"
                                                :src="message.quote_message.coverUrl || message.quote_message.url"
                                            />
                                            <div class="video-play-icon">
                                                <i class="iconfont svg_icon_play icon-videoplay"></i>
                                            </div>
                                        </div>
                                        <span>{{ $t('msg_type_video') }}</span>
                                    </div>
                                    <!-- 文本类型引用 -->
                                    <div
                                        v-else-if="message.quote_message.msg_type == systemConfig.msg_type.Text"
                                        class="quote-content-message"
                                        v-html="message.quote_message.msg_body"
                                    ></div>
                                    <!-- 其他类型引用 -->
                                    <div v-else class="quote-content-message">
                                        {{ $t('quoted_content') }}
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div slot="top" class="van-loadmore-top" v-if="options.isLoadTop">
                        <span class="van-loadmore-text">{{ topText }}</span>
                    </div>
                    <div slot="bottom" class="van-loadmore-bottom" v-if="options.isLoadBottom">
                        <span class="van-loadmore-text">{{ bottomText }}</span>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>
        <audio :ref="soundPlayerName" :id="soundPlayerName" autoplay="autoplay" :src="voiceSrc"></audio>
        <div class="multi_select_foot" v-if="multi_select_mode">
            <span class="analyze" v-if="functionsStatus.breastAI" @click="multiAnalyze">{{
                $t('action_analyze_text')
            }}</span>
            <span class="analyze" @click="multiTransfer">{{ $t('transmit_title') }}</span>
            <span class="analyze" v-if="isShowWechat" @click="multiShare">{{ $t('share_wechat') }}</span>
            <span class="cancel" @click="cancelMultiSelect">{{ $t('cancel_btn') }}</span>
        </div>
        <MenusAction
            :currentFile="currentFile"
            ref="norMalMenusAction"
            @multiSelectImage="multiSelectImage"
            @locateOriginalMessage="locateOriginalMessage"
        ></MenusAction>
        <ReviewDetail
            :message="currentFile"
            ref="reviewDetail"
            v-model="isShowReviewDetail"
            :hasMoreDetail="hasMoreDetail"
        ></ReviewDetail>
    </div>
</template>
<script>
import base from "../lib/base";
import iworksTool from "../lib/iworksTool";
import send_message from "../lib/send_message.js";
import { Toast, PullRefresh, List, Checkbox, CheckboxGroup } from "vant";
import Tool from "@/common/tool.js";
import {
    getLocalImgUrl,
    openVisitingCard,
    downLoadImgToLocal,
    getShowDes,
    sendToAnalyze,
    getFullServerResourceUrl,
    formatDurationTime,
    getRecordSubject,
    generateGalleryFileId,
    getLocalAvatar,
    getClipSubject,
    checkResourceType,
    getMoreResourceList,
    formatAttendeeNicknameToMap,
    getResourceTempState,
} from "../lib/common_base";
import { Dialog, Icon, Skeleton, Circle } from "vant";
import ExamMsg from "../components/examMsg.vue";

import ReviewMsg from "./messageItem/reviewMsg.vue";
import VideoClipMsg from "./messageItem/videoClipMsg.vue";
import ReviewDetail from "./messageItem/reviewDetail.vue";
import MenusAction from "../components/menusAction.vue";
import moment from "moment";
import service from "../service/service.js";
import { CHAT_TYPE } from "../lib/constants";
export default {
    mixins: [base, send_message, iworksTool],
    name: "ChatMessageList",
    permission: true,
    props: {
        chatMessageList: {
            type: Array,
            default: () => {
                return [];
            },
        },
        options: {
            type: Object,
            default: () => {
                return {
                    isLoadTop: true, //是否启用头部下拉加载
                    isLoadBottom: false, //是否启用底部上拉加载
                };
            },
        },
        //检查到图片列表空，是否反复加载图片
        isReload: {
            type: Boolean,
            default: true,
        },
        //播放器id
        soundPlayerName: {
            type: String,
            default: "soundPlayer",
        },
        chatType: {
            type: Number,
            default: CHAT_TYPE.CHAT_WINDOW,
        },
    },
    components: {
        ExamMsg,
        [Dialog.Component.name]: Dialog.Component,
        ReviewMsg,
        VanSkeleton: Skeleton,
        VideoClipMsg,
        VanIcon: Icon,
        VanCircle: Circle,
        MenusAction,
        ReviewDetail,
        VanList: List,
        VanPullRefresh: PullRefresh,
        VanCheckbox: Checkbox,
        VanCheckboxGroup: CheckboxGroup,
    },
    data() {
        return {
            getResourceTempState,
            checkResourceType,
            getLocalAvatar,
            formatDurationTime,
            getRecordSubject,
            getClipSubject,
            formatDateTime: Tool.formatDateTime,
            scrollDebounce: Tool.debounce(this.scrollHandler, 300),
            getMoreResourceList,
            closeOnClickModal: false, //是否允许点击模态层关闭
            cancelText: "cancel",
            copyActions: [],
            actions: [],
            soundingMsgId: null,
            voiceSrc: "",
            soundingTimeout: 0,
            lastScrollHeight: 0,
            isScrollOnMiddle: false,
            loadingTopHistory: false,
            loadingBottomHistory: false,
            lastCid: null,
            multi_select_mode: false, //图片多选模式
            multiImageList: [],
            showMessageList: true,
            bottomAllLoaded: false,
            topLoading: false,
            bottomLoading: false,
            topText: "",
            topPullText: "",
            topDropText: "",
            topLoadingText: "",
            topStatus: "",
            bottomStatus: "",
            bottomText: "",
            bottomPullText: "",
            bottomDropText: "",
            bottomLoadingText: "",
            downloadFilesInfo: {},
            quickEntryCid: 0,
            presentTime: new Date().getTime(),
            renameDialog: false,
            renameResourceId: "0",
            currentFile: {},
            isShowReviewDetail: false,
            hasMoreDetail: false, //直播详情是否显示更多：主讲人，群主，管理员
            loadedChatMessageList: false,
            hasLoadedListCid: [],
            currentGalleryList: [],
            currentRate: 0,
            openingHomework: false,
        };
    },
    filters: {
        showData(ts) {
            return moment(ts).format("YYYY-MM-DD HH:mm");
        },
    },
    computed: {
        aiPresetData() {
            return this.$store.state.aiPresetData;
        },
        conversation() {
            return this.$store.state.conversationList[this.cid] || {};
        },
        is_loaded_history_list() {
            return this.conversation.is_loaded_history_list;
        },
        messageList() {
            this.checkMessageLoaded();
            return this.chatMessageList;
        },
        isSupportOssPause() {
            const isInternalNetworkEnv = this.systemConfig.serverInfo.network_environment === 1;
            return isInternalNetworkEnv ? false : true;
        },
        aiAnalyzeId() {
            let fid = 0;
            if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze) {
                fid = this.conversation.fid;
            }
            return fid;
        },
        groupPublicState() {
            return this.$store.state.systemConfig.groupPublicState;
        },
        downloadFiles() {
            return this.$store.state.dynamicGlobalParams.downloadFiles;
        },
        filePageData() {
            return this.$store.state.dynamicGlobalParams.filePageData;
        },
        attendeeMap() {
            return formatAttendeeNicknameToMap(this.conversation.attendeeList);
        },
        cid() {
            return this.$route.query.cid || this.$route.params.cid;
        },
        isShowReEdit() {
            return (item) => {
                if (this.presentTime - this.$root.withDrawTimeList[item.gmsg_id] <= 180000) {
                    return true;
                }
                return false;
            };
        },
    },
    watch: {
        topStatus(val) {
            //
            switch (val) {
            case "pull":
                this.topText = this.topPullText;
                break;
            case "drop":
                this.topText = this.topDropText;
                break;
            case "loading":
                this.topText = this.topLoadingText;
                break;
            }
        },
        bottomStatus(val) {
            switch (val) {
            case "pull":
                this.bottomText = this.bottomPullText;
                break;
            case "drop":
                this.bottomText = this.bottomDropText;
                break;
            case "loading":
                this.bottomText = this.bottomLoadingText;
                break;
            case "loadingEnd":
                this.bottomText = this.bottomNomoreText;
                break;
            }
        },
        multi_select_mode(val) {
            this.$parent.multi_select_mode = val;
        },
    },
    created() {},
    mounted() {
        this.initEventListener();
        this.timeinterval = setInterval(this.refreshTime, 1000);
        (this.topPullText = this.$t('top_pull_text')),
        (this.topDropText = this.$t('bottom_drop_text')),
        (this.topLoadingText = this.$t('bottom_loading_text'));
        (this.bottomPullText = this.$t('bottom_pull_text')),
        (this.bottomDropText = this.$t('bottom_drop_text')),
        (this.bottomLoadingText = this.$t('bottom_loading_text'));
        this.bottomNomoreText = this.$t('no_more_text');
        this.$root.eventBus
            .$off("homeworkTransmitCallback")
            .$on("homeworkTransmitCallback", this.homeworkTransmitCallback);
    },
    beforeDestroy() {
        clearInterval(this.timeinterval);
    },
    activated() {
        this.$nextTick(() => {
            this.initParams();
        });
    },
    deactivated() {
        this.loadingTopHistory = false;
        this.lastCid = this.cid;
        this.loadedChatMessageList = false;
        this.cancelMultiSelect();
    },
    methods: {
        refreshTime() {
            this.presentTime = new Date().getTime();
        },
        reEditMessage(message) {
            console.log(message);
            window.vm.$root.withDrawTimeList[message.gmsg_id] = new Date().getTime();
            this.$emit("reEditMessage", message.msg_body);
        },
        initParams() {
            this.isScrollOnMiddle = false;
            this.soundingMsgId = null;
        },
        initEventListener() {
            let that = this;
            let player_node = document.getElementById(this.soundPlayerName);
            player_node.addEventListener("ended", function () {
                console.log("play ended");
                //播放完语音停止gif
                that.soundingMsgId = null;
                //that.voiceSrc=''
                if (that.systemConfig.clientType == 4 || that.systemConfig.clientType == 8) {
                    window.CWorkstationCommunicationMng.StopPlayingAudio();
                }
            });
            player_node.addEventListener("pause", function () {
                console.log("play stop");
                //播放完语音停止gif
                //that.soundingMsgId = null;
                // that.voiceSrc=''
                if (that.systemConfig.clientType == 4 || that.systemConfig.clientType == 8) {
                    window.CWorkstationCommunicationMng.StopPlayingAudio();
                }
            });
        },
        stopOrEndSound() {
            let that = this;
            console.log("play ended or stop");
            //播放完语音停止gif
            that.soundingMsgId = null;
            //that.voiceSrc=''
            if (that.systemConfig.clientType == 4 || that.systemConfig.clientType == 8) {
                window.CWorkstationCommunicationMng.StopPlayingAudio();
            }
        },
        loadTopHistory() {
            if (!this.loadingTopHistory) {
                this.loadingTopHistory = true;
                this.handleTopChange("loading");
                this.$emit("loadTopHistory");
                let lastItem = this.$refs.message_container || {};
                this.lastScrollHeight = lastItem.scrollHeight;
            }
            if (this.topLoading) {
                this.topLoading = false;
            }
        },
        loadBottomHistory() {
            if (!this.$listeners.loadBottomHistory) {
                this.bottomAllLoaded = true;
                return;
            }
            if (!this.loadingBottomHistory) {
                this.$emit("loadBottomHistory");
                // let lastItem=this.$refs.message_container||{};
                // this.lastScrollHeight=lastItem.scrollHeight;
                this.loadingBottomHistory = true;
            }
            if (this.bottomLoading) {
                this.bottomLoading = false;
            }
        },
        getSendTimeTip(index, type = 1) {
            let list = this.messageList;
            if (!list || list.length === 0) {
                return;
            }
            if (!list[index].send_ts) {
                //发送的消息没有send_ts暂时不显示
                return "";
            }
            let currentTime = new Date(list[index].send_ts);
            if (index == 0) {
                return Tool.getShowTime(list[index], type);
            } else {
                if (list[index - 1] && list[index - 1].send_ts) {
                    let lastTime = new Date(list[index - 1].send_ts);
                    if (currentTime - lastTime > 3 * 60 * 1000) {
                        return Tool.getShowTime(list[index], type);
                    } else {
                        return "";
                    }
                } else {
                    return "";
                }
            }
        },
        matchAll(text, reg) {
            let res = [];
            let match;
            while ((match = reg.exec(text))) {
                res.push(match);
            }
            return res;
        },
        textStyle(text) {
            // 文本小于10或者单独只有表情包时
            let reg = new RegExp(/<img src=.*?>/, "ig");
            let spaceMatches = this.matchAll(text, /<br\/>/g);
            text = text.replace(/<br\/>/g, "");
            text = text.replace(reg, "11"); // 1个表情替换为2个字符
            let textLength = text.length;
            let computedTextLen = text.length;
            let spaceLength = 1;
            for (const item of spaceMatches) {
                if (!item.done) {
                    spaceLength += 1;
                }
            }
            // debugger
            if (textLength == 2) {
                return { height: "1.95rem", textAlign: "center" };
            } else if (textLength <= 10) {
                return { textAlign: "center" };
            } else if (textLength >= spaceLength - 5 && textLength <= spaceLength + 5) {
                return { textAlign: "center" };
            }
            return {};
        },
        scrollToBottom2(delay = 150) {
            setTimeout(() => {
                const container = document.querySelector(".message_list_container");
                if (!container) {
                    return;
                }
                const duration = 300;
                var start = container.scrollTop;
                var end = container.scrollHeight - container.clientHeight;
                var change = end - start;
                var currentTime = 0;
                var increment = 20;
                function animateScroll() {
                    currentTime += increment;
                    var val = Math.easeInOutQuad(currentTime, start, change, duration);
                    container.scrollTop = val;

                    if (currentTime < duration) {
                        requestAnimationFrame(animateScroll);
                    }
                }

                Math.easeInOutQuad = function (t, b, c, d) {
                    t /= d / 2;
                    if (t < 1) {
                        return (c / 2) * t * t + b;
                    }
                    t--;
                    return (-c / 2) * (t * (t - 2) - 1) + b;
                };
                animateScroll();
            }, delay);
        },
        // 保证滚动到底部：在首屏渲染或图片高度变动导致scrollHeight持续变化时，重复校正至底部，直到稳定
        scrollToBottomStable(delay = 150) {
            setTimeout(() => {
                const container = this.$refs.message_container;
                if (!container) {
                    return;
                }
                let lastHeight = -1;
                let stableCount = 0;
                const maxDurationMs = 1200;
                const stableFramesNeeded = 3; // 连续稳定帧数
                const startTime = Date.now();

                // 先直接滚动一次到底，降低首帧误差
                container.scrollTop = container.scrollHeight - container.clientHeight;

                // 使用ResizeObserver在高度变化时再拉到底（短期观察）
                let ro;
                if (typeof ResizeObserver !== "undefined") {
                    ro = new ResizeObserver(() => {
                        container.scrollTop = container.scrollHeight - container.clientHeight;
                    });
                    try {
                        ro.observe(container);
                    } catch (e) {
                        ro = null;
                    }
                }

                const tick = () => {
                    const end = container.scrollHeight - container.clientHeight;
                    if (container.scrollTop !== end) {
                        container.scrollTop = end;
                    }
                    const currentHeight = container.scrollHeight;
                    if (currentHeight === lastHeight) {
                        stableCount++;
                    } else {
                        stableCount = 0;
                        lastHeight = currentHeight;
                    }
                    const elapsed = Date.now() - startTime;
                    if (stableCount >= stableFramesNeeded || elapsed > maxDurationMs) {
                        if (ro) {
                            try {
                                ro.disconnect();
                            } catch (e) {}
                        }
                        return;
                    }
                    requestAnimationFrame(tick);
                };
                requestAnimationFrame(tick);
            }, delay);
        },
        scrollToBottom(delay = 150) {
            setTimeout(() => {
                let refs = this.$refs[`message_item_${this.messageList.length - 1}`] || [];
                let lastItem = refs[0];
                if (lastItem) {
                    lastItem.scrollIntoView && lastItem.scrollIntoView(true);
                }
            }, delay);
        },
        getSoundStyle(msg) {
            const total_length = 12.65; // 最大长度11rem
            const total_count = 60000; // 最长时常60s
            let width = total_length * (msg.duration / total_count);
            if (width <= 4.3) {
                //最小长度4.3rem
                width = 4.3;
            }
            return {
                width: width.toFixed(2) + "rem",
            };
        },
        playSound(gmsg_id) {
            var that = this;
            //解除播放完语音的timeout，防止频繁点击不同语音意外停止
            clearTimeout(this.soundingTimeout);
            let player_node = document.getElementById(this.soundPlayerName);
            player_node.volume = 1;

            if (this.soundingMsgId == gmsg_id) {
                //点击正在播放的语音
                this.soundingMsgId = null;
                if (this.systemConfig.clientType == 4 || this.systemConfig.clientType == 8) {
                    window.CWorkstationCommunicationMng.StopPlayingAudio();
                }
                player_node.pause();

                this.voiceSrc = "";
            } else {
                try {
                    this.soundingMsgId = gmsg_id;
                    let index = this.messageList.findIndex((item) => item.gmsg_id === gmsg_id);
                    let message = this.messageList[index];
                    message.isPlayed = true;
                    console.log("soundPlayer url Net: ", message.url);
                    let local_url = this.getSoundSource(message.url);
                    this.voiceSrc = local_url; //
                    console.log("soundPlayer url Local: ", local_url);
                    let player_node = document.getElementById(this.soundPlayerName);
                    this.$store.commit("conversationList/updateChatMessage", message);
                    player_node.onerror = function () {
                        message.downloading = true;
                        console.log("---------------------onerror soundPlayer-------- " + that.voiceSrc);
                        let time_begin = new Date();
                        downLoadImgToLocal(message, 13, index, function () {
                            message.downloading = false;
                            console.log("-------------------downLoadImgToLocal------------");
                            let time_end = new Date();
                            console.log("*****************", time_end - time_begin);
                            player_node.onerror = null;
                            if (that.soundingMsgId == gmsg_id) {
                                that.voiceSrc = local_url; //local_url
                                if (that.systemConfig.clientType == 4 || that.systemConfig.clientType == 8) {
                                    window.audio_player = player_node;
                                    window.CWorkstationCommunicationMng.PlayingAudio();
                                }
                                player_node.load();
                                player_node.play();
                            } else {
                                console.log("-----that.soundingMsgId != gmsg_id");
                            }
                        });
                        player_node.onerror = null;
                    };
                    if (this.systemConfig.clientType == 4 || this.systemConfig.clientType == 8) {
                        window.audio_player = player_node;
                        window.CWorkstationCommunicationMng.PlayingAudio();
                    }
                    player_node.load();
                    player_node.play();
                } catch (e) {
                    Toast(e);
                }
            }
        },
        getSoundSource(url) {
            /*let source=this.systemConfig.server_type.protocol+this.systemConfig.server_type.host+'/'+url;
            //预加载文件，苹果下不支持preload
            let sound=new Image();
            sound.onload=function(){
                console.log('preload sound source',source)
            }
            sound.src=source;*/
            return getLocalImgUrl(url);
        },
        listLoaded(list, direcation, status, cb) {
            console.log("listLoaded", list, direcation, status, cb);
            if (direcation === "bottom") {
                this.bottomLoading = false;
                this.loadingBottomHistory = false;
                if (status === "nomore") {
                    this.bottomAllLoaded = true;
                    setTimeout(() => {
                        this.handleBottomChange("loadingEnd");
                    }, 0);
                }
            } else {
                this.topLoading = false;
                this.loadingTopHistory = false;
                if (cb) {
                    cb();
                } else {
                    if (list.length > 0) {
                        this.$nextTick(() => {
                            let lastItem = this.$refs.message_container || {};
                            let scrollHeight = lastItem.scrollHeight;
                            lastItem.scrollTop = scrollHeight - this.lastScrollHeight;
                        });
                    }
                }
            }
        },
        scrollHandler() {
            console.log("scrollHandler");
            this.checkIsScrollOnMiddle();
            if (this.$parent.unreadNumber && this.$parent.unreadNumber > 0) {
                setTimeout(() => {
                    if (this.isScrollOnMiddle) {
                        this.$parent.unreadNumber = 0;
                    }
                });
            }
        },
        shouldScrollBottom(force = false, delay = 250) {
            //页面渲染好和来新消息时焦点在底部，则滚动
            if (!this.isScrollOnMiddle || force) {
                // 统一使用稳定滚动方案，避免元素异步渲染导致未真正到底部
                this.scrollToBottomStable(delay);

                return true;
            }
        },
        checkIsScrollOnMiddle() {
            var lastItem = this.$refs.message_container || {};
            let tag = false;
            let buttomDistance = lastItem.scrollHeight - (lastItem.scrollTop + lastItem.offsetHeight);
            console.log("buttomDistance", buttomDistance);
            //lastItem.scrollTop在华为某个机型有浮点数，使用数值区间容错
            if (isNaN(buttomDistance) || (buttomDistance > -5 && buttomDistance < 5)) {
                tag = false;
            } else {
                tag = true;
            }
            this.isScrollOnMiddle = tag;
            console.log("isScrollOnMiddle", tag);
            // return tag;
        },
        cancelMultiSelect() {
            this.multi_select_mode = false;
            this.multiImageList = [];
        },
        clickMultiImage(message, index) {
            let image = this.$refs["imageCheckItem_" + index][0];
            let cancelCheck = image.checked;
            console.log(cancelCheck);
            // image.click()
            if (!cancelCheck) {
                this.multiImageList.push(message);
            } else {
                this.multiImageList = this.multiImageList.filter((ele) => message.gmsg_id !== ele.gmsg_id);
            }
        },
        multiAnalyze() {
            let transmitTempList = [];
            for (let item of this.multiImageList) {
                transmitTempList.push(item);
            }
            this.$root.transmitTempList = transmitTempList;
            sendToAnalyze();
            this.cancelMultiSelect();
        },
        multiTransfer() {
            let transmitTempList = [];
            for (let item of this.multiImageList) {
                transmitTempList.push(item);
            }
            this.$root.transmitTempList = transmitTempList;
            this.$refs.norMalMenusAction.transmit();
            this.cancelMultiSelect();
        },
        multiShare() {
            let transmitTempList = [];
            for (let item of this.multiImageList) {
                transmitTempList.push(item);
            }
            this.$root.transmitTempList = transmitTempList;
            this.$root.eventBus.$emit("shareToWechatHandler", this.$root.transmitTempList);
            this.cancelMultiSelect();
        },
        renameSuccess() {},
        clickImageHandle: Tool.debounce(
            function (message, index) {
                console.log("clickImageHandle", message);
                if (!message.gmsg_id) {
                    //未发送成功不能打开
                    return;
                }
                if (this.multi_select_mode) {
                    this.clickMultiImage(message, index);
                } else {
                    this.openGallery(message, index);
                }
            },
            500,
            true
        ),
        openGalleryByReview({ message, index }) {
            this.openGallery(message, index);
        },
        async openGallery(msg, index) {
            //打开画廊
            if (!Tool.checkConversationConnect(this.cid)) {
                Toast(this.$t('network_error_tip'));
                return;
            }
            if (Number(window.vm.$root.currentLiveCid) === Number(this.cid)) {
                Toast(this.$t('playing_video_tip'));
                return;
            }
            if (this.$parent.$refs.message_text) {
                this.$parent.$refs.message_text.blur();
            }
            if (!window.main_screen.gateway.check) {
                if (this.checkResourceType(msg) === "video" || this.checkResourceType(msg) === "review_video") {
                    Toast(this.$t('no_net_no_open_video'));
                    return;
                }
            }

            const list = this.getChatMessageResourceList();
            if (list.length === 0) {
                Toast(this.$t('resource_being_generated'));
                return;
            } else if (list.length < 10 && this.currentGalleryList.length < 10) {
                const galleryList = this.conversation.galleryObj.gallery_list;
                if (galleryList && galleryList.length > 0) {
                    this.currentGalleryList = this.deduplicateArrayByResourceId(list.concat(galleryList));
                } else {
                    const moreResourceList = await this.getMoreResourceList(
                        list[list.length - 1].resource_id,
                        this.cid
                    );
                    this.currentGalleryList = this.deduplicateArrayByResourceId(list.concat(moreResourceList));
                }
            } else {
                this.currentGalleryList = this.deduplicateArrayByResourceId(list.concat(this.currentGalleryList));
            }
            let imgIndex = -1;
            for (let i = 0; i < this.currentGalleryList.length; i++) {
                let image = this.currentGalleryList[i];
                if (msg.resource_id == image.resource_id) {
                    imgIndex = i;
                    break;
                }
            }
            this.$store.commit("gallery/setGallery", {
                list: this.currentGalleryList,
                index: imgIndex,
                cid: Number(this.cid),
                fileId: generateGalleryFileId(msg),
                loadMore: true,
                loadMoreCallback: async () => {
                    const moreResourceList = await this.getMoreResourceList(
                        this.currentGalleryList[this.currentGalleryList.length - 1].resource_id,
                        this.cid
                    );
                    this.currentGalleryList = this.deduplicateArrayByResourceId(
                        this.currentGalleryList.concat(moreResourceList)
                    );
                    let loadMore = false;

                    if (moreResourceList.length >= this.systemConfig.consultationImageShowNum) {
                        loadMore = true;
                    }
                    this.$store.commit("gallery/setGallery", {
                        list: this.currentGalleryList,
                        loadMore,
                    });
                },
            });
            this.$nextTick(() => {
                this.$router.push({
                    path: `${this.$route.path}/gallery`,
                    query: this.$route.query,
                });
            });
        },
        getChatMessageResourceList() {
            let list = [];
            let resource_list = [];
            const msg_type = this.systemConfig.msg_type;
            const unLoadResourceTypeArr = [
                msg_type.Sound,
                msg_type.WITHDRAW,
                msg_type.EXAM_IMAGES,
                msg_type.File,
                msg_type.AI_ANALYZE,
                msg_type.IWORKS_PROTOCOL,
            ];
            for (var i = this.chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历
                let message = this.chatMessageList[i];
                if (unLoadResourceTypeArr.includes(message.msg_type)) {
                    continue;
                }
                if (message.resource_id && !resource_list.includes(message.resource_id)) {
                    list.push(message);
                    resource_list.push(message.resource_id);
                }

                if (
                    message.ai_analyze &&
                    message.ai_analyze.messages &&
                    message.ai_analyze.messages[0] &&
                    message.ai_analyze.messages[0].resource_id &&
                    !resource_list.includes(message.ai_analyze.messages[0].resource_id)
                ) {
                    list.push(message.ai_analyze.messages[0]);
                    resource_list.push(message.ai_analyze.messages[0].resource_id);
                }
            }
            console.log(resource_list, JSON.parse(JSON.stringify(list)), this.chatMessageList);
            return list;
        },
        deduplicateArrayByResourceId(array) {
            const seen = new Set();
            const deduplicatedArray = [];

            for (let i = 0; i < array.length; i++) {
                const item = array[i];
                const resourceId = item.resource_id;

                if (!seen.has(resourceId)) {
                    seen.add(resourceId);
                    deduplicatedArray.push(item);
                }
            }

            return deduplicatedArray;
        },
        openAnalyzeGallery(msg, index) {
            console.log(msg, index);
            if (msg.ai_analyze.report.error == undefined) {
                return;
            }
            let imagesList = msg.ai_analyze.messages.reduce((h, v) => {
                if (
                    v.msg_type != this.systemConfig.msg_type.EXPIRATION_RES &&
                    this.getResourceTempState(v.resource_id) === 1
                ) {
                    h.push(v);
                }
                return h;
            }, []);
            for (let image of imagesList) {
                image.group_id = this.cid;
                if (msg.ai_analyze.report.mark_list) {
                    image.mark_list = msg.ai_analyze.report.mark_list[image.resource_id] || [];
                } else {
                    image.mark_list = [];
                }
                image.summary = msg.ai_analyze.report.summary;
            }

            if (this.isAllAiMessageDelete(msg) || imagesList.length < 1) {
                return;
            }
            this.$store.commit("gallery/setGallery", {
                list: imagesList,
                index: index,
            });
            this.$nextTick(() => {
                this.$router.push(`${this.$route.fullPath}/ai_gallery`);
            });
        },
        openIworksGallery(message) {
            const image = {
                msg_type: 1,
                url: message.ai_result.imgUrl,
                thumb: "",
            };
            this.$store.commit("gallery/setGallery", {
                list: [image],
                index: 0,
            });
            this.$nextTick(() => {
                this.$router.push(`${this.$route.fullPath}/ai_gallery`);
            });
        },
        FileType(message) {
            if (!message.file_name) {
                return;
            }
            let file_type = message.file_name.replace(/.+\./, "").toLowerCase();
            const re = new RegExp(file_type, "ig");
            if (re.test("doc docx docm dot dotm dotx")) {
                message.show_file_type = "W";
                return "word";
            } else if (re.test("ppt pptx pptm")) {
                message.show_file_type = "PPT";
                return "powerpoint";
            } else if (re.test("pdf")) {
                message.show_file_type = "PDF";
                return "pdf";
            } else if (re.test("xlsx xlsm xlsb xls")) {
                message.show_file_type = "X";
                return "excel";
            } else if (re.test("zip")) {
                message.show_file_type = "ZIP";
                return "zip";
            } else if (
                re.test(
                    "ape au avi bat bin bmp cab chm css dll eml f4v fla flac flv gif hpl html ini jepg jpg mid midi mkv mov mp3 mp4 mpeg mpg png psd pst rtf swf temp tif tiff txt url wav wma wmv xls xlsx"
                )
            ) {
                message.show_file_type = file_type.toUpperCase();
                return file_type;
            } else {
                message.show_file_type = "?";
                return "unkown";
            }
        },
        downloadFileType(message) {
            let file_type = message.file_name.replace(/.+\./, "").toLowerCase();
            const re = new RegExp(file_type, "ig");
            if (re.test("doc docx docm dot dotm dotx")) {
                return "bg_word";
            } else if (re.test("ppt pptx pptm")) {
                return "bg_powerpoint";
            } else if (re.test("pdf")) {
                return "bg_pdf";
            } else if (re.test("xlsx xlsm xlsb xls")) {
                return "bg_excel";
            } else if (re.test("zip")) {
                message.show_file_type = "ZIP";
                return "bg_zip";
            } else if (
                re.test(
                    "ape au avi bat bin bmp cab chm css dll eml f4v fla flac flv gif hpl html ini jepg jpg mid midi mkv mov mp3 mp4 mpeg mpg png psd pst rtf swf temp tif tiff txt url wav wma wmv xls xlsx"
                )
            ) {
                message.show_file_type = file_type.toUpperCase();
                return `bg_${file_type}`;
            } else {
                message.show_file_type = "?";
                return "bg_unkown";
            }
        },
        showProtocol(msg) {
            let nodes = msg.protocol_view_guid;
            this.$router.push(this.$route.fullPath + "/protocol_tree/" + msg.protocol_execution_guid + "/" + nodes);
        },
        view_comment(msg, index) {
            //type:查看类型1，评论2，标签
            var type = 1;
            if (msg.msg_type == this.systemConfig.msg_type.COMMENT) {
                type = 1;
            } else {
                type = 2;
            }
            this.openGallery(msg, index);
            setTimeout(() => {
                this.$root.eventBus.$emit("openCommentModal", {
                    type: type,
                    message: msg,
                });
            }, 300);
            // Toast(this.$t('undeveloped_txt'))
        },
        pressAvatar(msg) {
            if (!this.$parent.hasOwnProperty("messageText")) {
                return;
            }
            if (this.conversation.is_single_chat || msg.sender_id == this.user.uid) {
                return;
            }
            // this.$refs.message_text.focus()
            this.$parent.messageText = this.$parent.messageText + "@" + msg.nickname + " ";
        },
        handleTopChange(status) {
            this.topStatus = status;
        },
        handleBottomChange(status) {
            this.bottomStatus = status;
        },
        getCommentNum(commentList = []) {
            let num = 0;
            commentList.forEach((element) => {
                if (element.status !== 0) {
                    if (!element.is_private || (element.is_private && this.user.uid === element.author_id)) {
                        num++;
                    }
                }
            });
            return num;
        },
        downLoadFileByDefaultBrowser(message) {
            // 处理点击事件
            if (message.uploading) {
                return;
            }
            if (this.checkFileExpired(message)) {
                return;
            }
            let url = message.url;
            Tool.openMobileDialog({
                message: `<p>${this.$t('jump_external_browser_tips')}</p><p>${encodeURI(url)}</p>`,
                showRejectButton: true,
                confirm: () => {
                    Tool.openLinkByDefaultBrowser(encodeURI(url));
                },
                confirmButtonText: this.$t('go_download'),
            });
        },
        downLoadFile(message) {
            //console.error('message:',message)
            // 如果重复点击下载，直接跳转到下载界面
            if (message.uploading) {
                return;
            }
            if (this.downloadFiles.Queue[0] && this.downloadFiles.Queue[0].file_id == message.file_id) {
                this.$router.push(`/index/chat_window/${this.cid}/download/${message.file_id}`);
                return;
            }
            let that = this;
            if (!this.downloadFilesInfo[message.file_id]) {
                this.downloadFilesInfo[message.file_id] = { progress: 0, status: 0 };
                this.downloadFilesInfo = Object.assign({}, this.downloadFilesInfo);
            }
            let { protocol, host, port } = this.systemConfig.server_type;
            // 文件资源路径
            let file_address = getFullServerResourceUrl(message.url).full_url;
            if (!file_address) {
                Toast(this.$t('save_null_fail_tip'));
                return;
            }
            // 拼接临时下载文件路径
            var sd_path_cell1 =
                "_downloads/" +
                this.systemConfig.server_type.host +
                "/" +
                this.$store.state.user.uid +
                "/files/" +
                message.file_id +
                "/" +
                message.file_name;
            // 将本地路径转换成平台绝对路径
            var sd_path_cell2 = "";
            let url = encodeURI(file_address);

            // console.error('****************chatMessageItem-downLoadFile',that)
            if (true) {
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "convertLocalFileSystemURL",
                        emitName: "NotifyConvertedLocalFileSystemURL",
                        params: { url: sd_path_cell1 },
                        timeout: null,
                    }).then((res) => {
                        sd_path_cell2 = res.url;
                        // 判断文件是否已经存在
                        try {
                            Tool.createCWorkstationCommunicationMng({
                                name: "resolveLocalFileSystemURL",
                                emitName: "NotifyResolvedLocalFileSystemURL",
                                params: { action: "checkExist", local_url: sd_path_cell2 },
                                timeout: null,
                            }).then((res) => {
                                if (res.error_code == "0") {
                                    //存在
                                    let openPath = res.local_url;
                                    // 兼容ios下文件打开路径问题，只需要文件的绝对路径，不需要整个沙盒路径
                                    // if (that.osName=='ios') {
                                    //     openPath = sd_path_cell1;
                                    // }
                                    // 文件打开
                                    try {
                                        Tool.createCWorkstationCommunicationMng({
                                            name: "openFile",
                                            emitName: "NotifyOpenFile",
                                            params: { url: openPath, options: {} },
                                            timeout: null,
                                        }).then((res) => {
                                            if (res.error_code == "1") {
                                                Toast(that.$t('tip_file_open_fail'));
                                            }
                                        });
                                    } catch (error) {
                                        Toast(error);
                                    }
                                } else {
                                    //不存在
                                    // 文件不存在，判断临时文件是否存在
                                    sd_path_cell2 = sd_path_cell2 + ".im_tmp";
                                    let task = {
                                        file_id: message.file_id,
                                        file_name: message.file_name,
                                        show_file_type: message.show_file_type,
                                        url: url,
                                        path: sd_path_cell2,
                                    };
                                    let currentTask = {
                                        file_id: message.file_id,
                                    };
                                    let filePageData = {
                                        file_id: message.file_id,
                                        file_name: message.file_name,
                                        file_size: 0,
                                        status: 0,
                                        downloaded_size: 0,
                                        full_path: "",
                                        show_file_type: message.show_file_type,
                                    };
                                    if (that.downloadFiles.Queue.length == 0) {
                                        that.$store.commit("dynamicGlobalParams/addTask", task);
                                        that.$store.commit("dynamicGlobalParams/setCurrentTask", currentTask);
                                        that.$store.commit("dynamicGlobalParams/setFilePageData", filePageData);
                                    }
                                    if (that.downloadFiles.currentTask.file_id != message.file_id) {
                                        Toast(that.$t('wait_download_file'));
                                        that.$store.commit("dynamicGlobalParams/addTask", task);
                                        return;
                                    }
                                    // 判断临时文件是否存在，存在删除再下载；不存在直接下载
                                    try {
                                        Tool.createCWorkstationCommunicationMng({
                                            name: "resolveLocalFileSystemURL",
                                            emitName: "NotifyResolvedLocalFileSystemURL",
                                            params: { action: "remove", local_url: sd_path_cell2 },
                                            timeout: null,
                                        }).then((res) => {
                                            if (res.error_code == "0") {
                                                that.$router.push(
                                                    `/index/chat_window/${that.cid}/download/${message.file_id}`
                                                );
                                                setTimeout(() => {
                                                    that.downLoadFile_start(url, sd_path_cell2);
                                                }, 0);
                                            } else {
                                                Toast(that.$t('tip_file_download_fail'));
                                                console.log("the temp file can not remove");
                                            }
                                        });
                                    } catch (error) {
                                        Toast(error);
                                    }
                                }
                            });
                        } catch (error) {
                            Toast(error);
                        }
                    });
                } catch (error) {
                    Toast(error);
                }
            }
        },
        downLoadFile_start(url, path) {
            let that = this;
            if (true) {
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "createDownload",
                        emitName: "NotifyCreateDownload",
                        params: {
                            url: url,
                            options: {
                                filename: path,
                                timeout: "5",
                                retry: "1",
                                retryInterval: "1",
                            },
                        },
                        timeout: null,
                    }).then((downloadRes) => {
                        that.$root.eventBus.$off("NotifyCreateDownloadProgress");
                        if (downloadRes.error_code == "0") {
                            var pathArr = path.split("/");
                            var fileName = pathArr.pop();
                            fileName = fileName.replace(/\.im_tmp/, "");
                            var filePath = pathArr.join("/") + "/";
                            // console.error('****************chatMessageItem-downLoadFile_start',that)
                            try {
                                Tool.createCWorkstationCommunicationMng({
                                    name: "resolveLocalFileSystemURL",
                                    emitName: "NotifyResolvedLocalFileSystemURL",
                                    params: {
                                        action: "moveTo",
                                        local_url: path,
                                        remote_url: filePath + "/" + fileName,
                                    },
                                    timeout: null,
                                }).then((res) => {
                                    if (res.error_code == "0") {
                                        that.downloadFilesInfo[that.downloadFiles.currentTask.file_id].status = 2;
                                        that.$store.commit("dynamicGlobalParams/deleteTask");
                                        that.$store.commit("dynamicGlobalParams/setFileStatus", 1);
                                        let openPath = res.local_url;
                                        // 兼容ios下文件打开路径问题，只需要文件的绝对路径，不需要整个沙盒路径
                                        // if (that.osName=='ios') {
                                        //     let docIndex = path.indexOf('downloads');
                                        //     let tempIndex = path.indexOf('.im_tmp')
                                        //     openPath = '_'+path.substring(docIndex,tempIndex);
                                        // }
                                        that.$store.commit("dynamicGlobalParams/setFileFullPath", `${openPath}`);

                                        if (that.downloadFiles.Queue.length > 0) {
                                            let task = that.downloadFiles.Queue[0];
                                            let filePageData = {
                                                file_id: task.file_id,
                                                file_name: task.file_name,
                                                file_size: 0,
                                                status: 0,
                                                downloaded_size: 0,
                                                full_path: "",
                                                show_file_type: task.show_file_type,
                                            };
                                            that.$store.commit("dynamicGlobalParams/setFilePageData", filePageData);
                                            that.$store.commit("dynamicGlobalParams/setCurrentTask", {
                                                file_id: task.file_id,
                                            });
                                            that.downLoadFile_start(task.url, task.path);
                                        }
                                        // 当前路由停留在聊天界面，打开文件
                                        if (
                                            that.$route.params.file_id &&
                                            that.$route.params.file_id == that.downloadFiles.currentTask.file_id
                                        ) {
                                            // 打开文件
                                            try {
                                                Tool.createCWorkstationCommunicationMng({
                                                    name: "openFile",
                                                    emitName: "NotifyOpenFile",
                                                    params: { url: openPath, options: {} },
                                                    timeout: null,
                                                }).then((res) => {
                                                    if (res.error_code == "1") {
                                                        Toast(this.$t('tip_file_open_fail'));
                                                    }
                                                });
                                            } catch (error) {
                                                Toast(error);
                                            }
                                        }
                                    } else {
                                        Toast(this.$t('tip_file_download_fail'));
                                        console.log("the temp file can not remove");
                                    }
                                });
                            } catch (error) {
                                Toast(error);
                            }
                        } else {
                            that.downloadFilesInfo[that.downloadFiles.currentTask.file_id].status = 6;
                            that.$store.commit("dynamicGlobalParams/deleteTask");
                            that.$store.commit("dynamicGlobalParams/setFileStatus", 6);
                            Toast(that.$t('tip_file_download_fail'));
                            console.log("Download failed:", status);
                        }
                    });
                    that.$root.eventBus
                        .$off("NotifyCreateDownloadProgress")
                        .$on("NotifyCreateDownloadProgress", Tool.throttle(that.throttleStateChangedNative, 100));
                } catch (error) {
                    Toast(error);
                }
            }
        },

        throttleStateChangedNative(res) {
            console.log("listener state");
            if (res.state == 3) {
                if (this.filePageData.file_size == 0) {
                    this.downloadFilesInfo[this.downloadFiles.currentTask.file_id].status = 1;
                    this.$store.commit("dynamicGlobalParams/setFileSize", res.totalSize);
                }
                this.downloadFilesInfo[this.downloadFiles.currentTask.file_id].progress =
                    Math.ceil((res.downloadedSize / res.totalSize) * 100) * 3.6;
                this.$store.commit("dynamicGlobalParams/setFileDownloadedSize", res.downloadedSize);
                console.log(res.downloadedSize + " / " + res.totalSize);
            }
        },
        getProgress(file, type) {
            let progress = file.progress;
            let status = file.status;
            let result = 0;
            if (type == 0) {
                if (progress < 180) {
                    result = progress;
                } else {
                    result = 180;
                }
            } else if (type == 1) {
                if (progress > 180) {
                    result = progress - 180;
                }
                if (status == 2) {
                    result = 180;
                    Toast(this.$t('tip_file_download_success'));
                }
            }
            return result;
        },
        openVisitingCard(messages, type) {
            console.log(messages, type);
            if (window.vm.$root.currentLiveCid) {
                return;
            }
            openVisitingCard(messages, type);
        },
        downLoadImgToLocal(img, type, index, callback) {
            downLoadImgToLocal(img, type, index, callback);
        },
        getShowDes(des) {
            return getShowDes(des);
        },
        quickEntry(item) {
            if (this.$root.currentLiveCid) {
                //正在直播中 不允许使用快速入口
                Toast(this.$t('live_not_allow_operation'));
                return;
            }
            window.main_screen.getLiveInfoById({ live_id: item.liveInfo.live_id }, (liveRes) => {
                if (
                    liveRes.data.status === this.systemConfig.liveManagement.cancel &&
                    this.user.uid !== liveRes.data.creator_id
                ) {
                    Toast(this.$t('live_invite_status3').replace("{1}", ""));
                    return;
                }
                window.main_screen.checkJoinLiveStatus({ live_id: item.liveInfo.live_id }, async (res) => {
                    if (!res.error_code) {
                        if (!res.data.enterAuth) {
                            Toast(this.$t('no_permission_to_open_room'));
                            return;
                        }
                        if (!res.data.groupEnterStatus) {
                            //不在群内
                            await this.requestAddLiveGroup(item); // 先加入直播间，再进入会话
                        }
                        this.openConversation(item.liveInfo.group_id, 13, () => {
                            this.$router.push(
                                `/index/live_management/my_live/chat_window/${item.liveInfo.group_id}?live_id=${item.liveInfo.live_id}`
                            );
                            if (
                                liveRes.data.status === this.systemConfig.liveManagement.waiting ||
                                liveRes.data.status === this.systemConfig.liveManagement.starting
                            ) {
                                setTimeout(() => {
                                    this.$root.eventBus.$emit("receiveConference");
                                }, 1500);
                            }
                        });
                    } else {
                        Toast(res.error_msg);
                    }
                });
            });
        },
        requestAddLiveGroup(item) {
            return new Promise((resolve, reject) => {
                let gid = item.liveInfo.group_id;
                window.main_screen.applyJoinGroup(
                    {
                        mark: "",
                        gid: gid,
                        inviterID: 0,
                        source: 1,
                    },
                    (res) => {
                        if (res.error_code == 0) {
                            resolve(true);
                        } else {
                            reject(false);
                        }
                    }
                );
            });
        },
        openExamImages(message, index) {
            //直播中不打开病例
            if (Number(window.vm.$root.currentLiveCid) === Number(this.cid)) {
                Toast(this.$t('playing_video_tip'));
                return;
            }
            if (this.multi_select_mode) {
                this.clickMultiImage(message, index);
            } else {
                this.$router.push(`/index/chat_window/${this.cid}/exam_image_list/${message.gmsg_id}`);
            }
        },
        openLiveDetailDialog(message) {
            if (Number(window.vm.$root.currentLiveCid) === Number(this.cid)) {
                Toast(this.$t('playing_video_tip'));
                return;
            }
            this.$set(this, "currentFile", message);
            this.$nextTick(() => {
                this.hasMoreDetail =
                    this.$checkPermission(
                        { conversationPermissionKey: "conference.live_detail" },
                        {
                            conversationId: this.cid,
                            message,
                        }
                    ) ||
                    this.conversation.service_type === this.systemConfig.user_service_type.LiveBroadcast;
                this.isShowReviewDetail = true;
            });
        },
        checkMessageLoaded() {
            if (this.loadedChatMessageList) {
                return;
            } else {
                if (this.chatMessageList.length > 0) {
                    this.loadedChatMessageList = true;
                }
            }
        },
        clickTextMsg(event) {
            if (event.target.classList.contains("openLinkByDefaultBrowser")) {
                // 处理点击事件
                let url = event.target.dataset.url;
                Tool.openLinkByDefaultBrowser(url);
            }
        },
        formatSize(size) {
            return Tool.formatSize(size);
        },
        showExamMsgMenu(data) {
            const { msg, from } = data;
            this.showTooltipsMenu(msg, from);
        },
        showTooltipsMenu(msg, from = "chatComponent") {
            if (msg.is_default_image || !msg.gmsg_id) {
                return false;
            }
            this.$set(this, "currentFile", msg);
            this.$refs.norMalMenusAction.showTooltipsMenu(msg, from);
        },
        multiSelectImage() {
            let msg = this.$root.transmitTempList.pop();
            this.multi_select_mode = true;
            this.$nextTick(() => {
                this.multiImageList.push(msg);
            });
        },
        showLog(data) {
            console.log(data, 222);
        },
        homeworkTransmitCallback(data) {
            const { _id } = this.currentFile.assignmentInfo;
            service
                .transmitHomework({
                    gid: data.cid,
                    assignmentID: _id,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        Toast(this.$t('operate_success'));
                    }
                });
        },
        clickCloudExam(msg) {
            if (Number(window.vm.$root.currentLiveCid) === Number(this.cid)) {
                Toast(this.$t('playing_video_tip'));
                return;
            }
            if (msg.msg_type == this.systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL) {
                this.$router.push({
                    path: `${this.$route.path}/cloud_exam/exam/1/${msg.paperInfo._id}`,
                    query: this.$route.query,
                });
                return;
            }
            this.openingHomework = true;
            service
                .getanswerSheetByAssignmentID({
                    assignmentID: msg.assignmentInfo._id,
                })
                .then((res) => {
                    this.openingHomework = false;
                    if (res.data.error_code === 0) {
                        const answerSheet = res.data.data.answerSheet;
                        const assignmentInfo = res.data.data.assignmentInfo;

                        if (answerSheet) {
                            // 存在数据，是考生
                            if (answerSheet.status === 3 || answerSheet.status === 4) {
                                if (answerSheet.assignmentInfo.resultCanChecked) {
                                    // 已批改能查看详情
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/exam/4/${answerSheet._id}`,
                                        query: this.$route.query,
                                    });
                                } else {
                                    Toast(this.$t('homework_cancheck_tip')); //该作业设置不允许查看详情，无法查看。
                                }
                            } else if (answerSheet.status === 2) {
                                Toast(this.$t('homework_correcting_tip')); //作业正在批改中，无法查看
                            } else if (answerSheet.status === 1 || answerSheet.status === 0) {
                                if (answerSheet.dueTime > Date.now()) {
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/exam/2/${answerSheet._id}`,
                                        query: this.$route.query,
                                    });
                                } else {
                                    Toast(this.$t('homework_overdue_tip')); //作业已超过截止时间，无法查看
                                }
                            } else {
                                Toast(this.$t('userNoAuth'));
                            }
                        } else {
                            // 不存在数据，判断是否老师或布置者
                            const teachers = msg.assignmentInfo.teachers;
                            const uid = msg.assignmentInfo.uid;
                            // this.openingHomework = true;
                            if (teachers.indexOf(this.user.id) > -1 || uid === this.user.id) {
                                // 直接使用getanswerSheetByAssignmentID返回的assignmentInfo
                                this.$store.commit("homework/setCurrentPaper", assignmentInfo);
                                if (teachers.indexOf(this.user.id) > -1) {
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/correcting_exam/2`,
                                        query: this.$route.query,
                                    });
                                } else {
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/correcting_exam/1`,
                                        query: this.$route.query,
                                    });
                                }
                            } else {
                                Toast(this.$t('error.paperAssignmentHasBeenRevoked')); //无答题卡、本人不是老师和布置者，显示作业已被废弃（对方查看）
                            }
                        }
                    }
                });
        },
        isAllAiMessageDelete(msg) {
            if (msg.ai_analyze && msg.ai_analyze.messages) {
                let delete_resources = msg.ai_analyze.messages.reduce((h, v) => {
                    if (
                        v.resource_deleted ||
                        v.msg_type == this.systemConfig.msg_type.EXPIRATION_RES ||
                        !(this.getResourceTempState(v.resource_id) === 1)
                    ) {
                        h.push(v);
                    }
                    return h;
                }, []);
                return msg.ai_analyze.messages.length == delete_resources.length;
            } else {
                return false;
            }
        },
        /**
         * 打开引用的图片或视频资源
         * @param {Object} quote_message - 被引用的消息对象
         */
        openQuotedGallery(quote_message) {
            if (!Tool.checkConversationConnect(this.cid)) {
                Toast(this.$t('network_error_tip'));
                return;
            }
            if (Number(window.vm.$root.currentLiveCid) === Number(this.cid)) {
                Toast(this.$t('playing_video_tip'));
                return;
            }
            if (this.$parent.$refs.message_text) {
                this.$parent.$refs.message_text.blur();
            }
            if (!window.main_screen.gateway.check) {
                if (
                    this.checkResourceType(quote_message) === "video" ||
                    this.checkResourceType(quote_message) === "review_video"
                ) {
                    Toast(this.$t('no_net_no_open_video'));
                    return;
                }
            }

            // 只打开单个引用的文件
            this.currentGalleryList = [quote_message];
            this.$store.commit("gallery/setGallery", {
                list: this.currentGalleryList,
                index: 0,
                cid: Number(this.cid),
                fileId: generateGalleryFileId(quote_message),
                loadMore: false,
            });
            this.$nextTick(() => {
                this.$router.push({
                    path: `${this.$route.path}/gallery`,
                    query: this.$route.query,
                });
            });
        },
        // 显示引用消息的菜单
        showQuoteMenu(message) {
            // 如果消息不包含引用消息，返回
            if (!message || !message.quote_message || this.chatType === CHAT_TYPE.CHAT_HISTORY) {
                return;
            }
            this.$set(this, "currentFile", message);
            this.$refs.norMalMenusAction.showQuoteMessageMenu(message, "quoteMenusAction");
        },

        // 定位到原始消息
        locateOriginalMessage(currentMessage) {
            if (!currentMessage.quote_message) {
                return;
            }
            const quote_message = currentMessage.quote_message;
            const gmsg_id = quote_message.gmsg_id;
            const positionToGmsgId = () => {
                const messageElement = this.$refs[`message-${gmsg_id}`];
                if (messageElement && messageElement[0]) {
                    messageElement[0].scrollIntoView({ behavior: "auto", block: "center" });
                    messageElement[0].classList.add("highlight");
                    // 动画结束后移除高亮类
                    setTimeout(() => {
                        messageElement[0].classList.remove("highlight");
                    }, 1500);
                } else {
                    Toast(this.$t('unable_locate_original_message'));
                }
            };
            const isFind = this.chatMessageList.find((item) => item.gmsg_id === gmsg_id);
            if (isFind) {
                positionToGmsgId();
            } else {
                window.main_screen.conversation_list[this.cid].getCountBetweenMsgId(
                    {
                        start: gmsg_id,
                        end: currentMessage.gmsg_id,
                    },
                    async (res) => {
                        if (res.error_code === 0) {
                            const count = res.data[0].count;
                            if (typeof count === "number") {
                                let distance = 0;
                                for (let i = 0; i < this.chatMessageList.length; i++) {
                                    if (this.chatMessageList[i].gmsg_id === currentMessage.gmsg_id) {
                                        distance = i;
                                        break;
                                    }
                                }
                                console.log(count, distance);
                                if (count > distance) {
                                    if (count - distance > 100) {
                                        Toast(this.$t('data_too_old_to_locate'));
                                        return;
                                    }
                                    // 如果消息数量大于当前距离，说明需要加载更多历史消息
                                    this.$emit(
                                        "getTargetCountList",
                                        {
                                            count: count - distance + 10,
                                        },
                                        (res) => {
                                            setTimeout(() => {
                                                const isFind = this.chatMessageList.find(
                                                    (item) => item.gmsg_id === gmsg_id
                                                );
                                                if (isFind) {
                                                    positionToGmsgId();
                                                } else {
                                                    Toast(this.$t('unable_locate_original_message'));
                                                }
                                            }, 0);
                                        }
                                    );
                                } else {
                                    //count小，说明之前内部遍历的时候，找不到，可能是被删除或者被撤回了
                                    Toast(this.$t('unable_locate_original_message'));
                                }
                            }
                        }
                    }
                );
            }
        },
    },
};
</script>
<style scoped lang="scss">
.message_list_container {
    flex: 1;
    overflow: auto;
    padding: 0 0.8rem;
    position: relative;
    -webkit-overflow-scrolling: touch;
    height: 100%;
    z-index: 0;
    .van-pull-refresh {
        min-height: 100%;
    }
    img {
        //兼容IOS长按会导致图片被拖出来问题
        pointer-events: none;
        -webkit-user-select: none; /*禁用手机浏览器的用户选择功能 */
        -moz-user-select: none;
    }
    .notify_wrapper {
        display: flex;
        justify-content: center;
        text-align: center;
        user-select: text;
        -webkit-user-select: text;
        line-height: 1;
        .system_notify,
        .send_time_tip {
            //                     padding: 5px 10px;
            margin-top: 0.75rem;
            display: inline-block;
            max-width: 100%;
            box-sizing: border-box;
            color: rgb(170, 180, 185);
            font-size: 0.55rem;
            border-radius: 0.1rem;
            white-space: break-spaces;
            overflow: visible;
            &.send_time_tip {
                //                         margin:0.3rem 0;
            }
            & > span {
                color: rgb(86, 200, 255);
            }
            & > .re_edit_wrapper {
                padding-left: 10px;
                display: inline;
            }
        }
        .send_time_tip {
            padding: 0 10px;
            line-height: 1;
            margin: 0;
            margin-top: 1.25rem;
        }
    }
    .system_message {
        display: flex;
        margin: 0 auto;
        max-width: 80%;
        background: #00c59d;
        color: #fff;
        padding: 0.4rem;
        border-radius: 0.2rem;
        position: relative;
        margin-top: 1.25rem;
        .ai_images_wrap {
            width: 7rem;
            height: 5rem;
            background: #333 !important;
            overflow: hidden;
            position: relative;
            border-radius: 0;
            img {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
            .icon-images {
                position: absolute;
                right: 0.4rem;
                bottom: 0;
                font-size: 1.2rem;
            }
        }
        .ai_message_right {
            flex: 1;
            font-size: 0.8rem;
            padding-left: 0.4rem;
            padding-bottom: 0.8rem;
            .nickname {
                color: #0066ff;
            }
            .tip {
                margin-top: 0.5rem;
            }
            .right_tip {
                text-align: right;
                font-size: 0.6rem;
                position: absolute;
                right: 0.4rem;
                bottom: 0.4rem;
            }
        }
    }
    .message_item {
        user-select: none;
        -webkit-user-select: none;
        margin-top: 1.25rem;
        transform: translate3d(0, 0, 0);
        .message_item_box {
            overflow: hidden;
        }
        .avatar_wraper {
            float: left;
            user-select: none;
            -webkit-user-select: none;
            overflow: hidden;
        }

        .message_item_wrapper {
            padding-left: 0.8rem;
            line-height: 1.4;
            float: left;
            max-width: 13.2rem;
            .message_item_name {
                line-height: 1;
                vertical-align: top;
                font-size: 0.55rem;
                color: #666;
                padding-bottom: 0.4rem;
                user-select: none;
                -webkit-user-select: none;
            }
            .stop_sound {
                display: flex;
                .stop {
                    margin-right: 0.4rem;
                    display: inline-block;
                    width: 1.3rem;
                    height: 1.3rem;
                }
                span {
                    margin: 0 0.2rem;
                }
            }
            .message_item_content {
                background-color: #f0f5f9;
                max-width: 12.65rem;
                box-sizing: border-box;
                color: rgb(26, 26, 26);
                display: inline-block;
                padding: 0.45rem 0.75rem;
                font-size: 0.75rem;
                text-align: left;
                word-break: break-all;
                position: relative;
                border-radius: 0.75rem;
                border: #aaa;
                user-select: none;
                -webkit-user-select: none;
                &.live_image_box {
                    position: relative;
                    overflow: hidden;
                    border-radius: 0.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.08);
                    padding: 0.8rem;
                    width: auto;
                    max-width: 13.2rem;
                    background: #f0f5f9 !important;
                    color: #1a1a1a !important;
                    .card-image {
                        width: 100%;
                        display: flex;
                        justify-content: center;
                        position: relative;
                        background: #fff;
                        max-height: 10rem;
                        overflow: hidden;
                        img {
                            display: block;
                            width: 100%;
                            height: auto;
                            object-fit: contain;
                        }
                    }
                    .card-date {
                        font-size: 0.7rem;
                        margin-bottom: 0.5rem;
                        color: rgba(0, 0, 0, 0.6);
                    }
                    .card-info {
                        display: flex;
                        justify-content: space-between;
                        font-size: 0.7rem;
                        margin-bottom: 0.5rem;
                        color: rgba(0, 0, 0, 0.87);
                    }
                    .card-content {
                        padding: 0.5rem 0;
                        .card-title {
                            font-size: 0.85rem;
                            font-weight: 500;
                            line-height: 1.3;
                            margin-bottom: 0.25rem;
                        }
                        .card-desc {
                            font-size: 0.75rem;
                            line-height: 1.4;
                            color: rgba(0, 0, 0, 0.6);
                        }
                    }
                    .card-tips {
                        display: flex;
                        font-size: 12px;
                        justify-content: space-between;
                        margin-top: 0.5rem;
                        font-size: 0.7rem;
                        .card-tips-link {
                            color: #007bff;
                            text-transform: uppercase;
                            font-weight: 500;
                            padding: 0.25rem 0;
                            cursor: pointer;
                        }
                        .card-tips-status0 {
                            color: #0c7bea;
                        }
                        .card-tips-status1 {
                            color: #c4a20a;
                        }
                        .card-tips-status2 {
                            color: #3dc40a;
                        }
                        .card-tips-status3 {
                            color: #fb1212;
                        }
                    }
                }
                .message_image_content {
                    width: 7rem;
                    height: 5rem;
                    background: #f4f4f4;
                    overflow: hidden;
                    position: relative;
                    border-radius: 0;
                    .review_text {
                        color: yellow;
                        font-size: 1rem;
                        width: 100%;
                        height: 100%;
                        background: #333;
                        text-align: center;
                        position: relative;
                        span {
                            position: absolute;
                            left: 50%;
                            top: 10%;
                            transform: translateX(-50%);
                        }
                    }
                    .svg_icon_play {
                        font-size: 3.5em;
                        color: #fff;
                        position: absolute;
                        // width: 2.55rem;
                        // height: 2.55rem;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                    .diff_time {
                        position: absolute;
                        bottom: 0.55rem;
                        color: #fff;
                        font-size: rgba(255, 177, 66);
                        color: rgba(255, 177, 66);
                        right: 0.5rem;
                        bottom: 0.5rem;
                        font-size: 0.5rem;
                        font-weight: 800;
                    }
                    .review_item_read_subject {
                        font-size: 0.6rem;
                        color: #fff;
                        word-break: break-all;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        position: absolute;
                        top: 0;
                        left: 0.2rem;
                        line-height: 2;
                    }
                    .review_item_read_time {
                        font-size: 0.5rem;
                        color: #fff;
                        word-break: break-all;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        position: absolute;
                        left: 0.2rem;
                        bottom: 0.5rem;
                        line-height: 1;
                    }
                }
                img {
                    height: 0.9rem;
                    vertical-align: middle;
                }
                .message_image {
                    height: auto;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50.2%, -50%);
                }
                .icon-comment1 {
                    position: absolute;
                    top: 0.2rem;
                    left: 0.3rem;
                    z-index: 2;
                    color: #f00;
                    font-size: 1rem;
                    line-height: 1rem;
                    color: #56c7fd;
                    span {
                        color: #fff;
                        position: absolute;
                        font-size: 0.6rem;
                        top: 0.1rem;
                        left: 50%;
                        transform: translate(-50%, 0);
                        line-height: 0.6rem;
                        white-space: nowrap;
                    }
                }
                .unread_tip {
                    position: absolute;
                    right: 0.5rem;
                    top: 0.5rem;
                    border-radius: 50%;
                    background-color: rgb(255, 103, 92);
                    width: 0.25rem;
                    height: 0.25rem;
                    z-index: 2;
                }
                .uploading_wrapper2 {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .van-circle {
                        width: 60px !important;
                        height: 60px !important;
                    }
                    .van-icon-upgrade,
                    .van-icon-pause-circle-o,
                    .van-icon-warning-o {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 1.5rem;
                    }
                    .van-icon-upgrade,
                    .van-icon-pause-circle-o {
                        color: #1989fa;
                    }
                    .van-icon-close {
                        position: absolute;
                        top: 0.5rem;
                        right: 0.5rem;
                    }
                }
                .uploading_wrapper {
                    width: 100%;
                    height: 0.2rem;
                    border: 1px solid #00c59d;
                    border-radius: 0.2rem;
                    position: relative;
                    margin-top: 0.3rem;
                    .cancel_upload {
                        transform: rotate(45deg);
                        display: block;
                        position: absolute;
                        right: -1rem;
                        top: -0.4rem;
                        font-size: 0.7rem;
                        color: #00c59d;
                    }
                    .upload_progress {
                        height: 100%;
                        background-color: #00c59d;
                    }
                }
                .view_comment_btn {
                    color: #0080ff;
                }
                .comment_thumbnail {
                    max-height: none;
                    display: block;
                    & + .icon-videofill {
                        position: absolute;
                        font-size: 2rem;
                        left: 2.4rem;
                        color: rgba(255, 255, 255, 0.7);
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }
                .comment_right {
                    background-color: rgb(241, 245, 248);
                    border-radius: 0 0 0.75rem 0.75rem;
                    p {
                        margin-top: 0.3rem;
                        & > span {
                            line-height: 0.9rem;
                        }
                    }
                    .tip {
                        text-align: right;
                        font-size: 0.7rem;
                    }
                }
                .ai_images_wrap {
                    width: 7rem;
                    height: 5rem;
                    background: #333 !important;
                    overflow: hidden;
                    position: relative;
                    border-radius: 0;
                    .icon-images {
                        position: absolute;
                        right: 0.4rem;
                        bottom: 0;
                        font-size: 1.2rem;
                    }
                }
                .iworks_protocol {
                    line-height: 1;
                    .checkout {
                        font-size: 0.8rem;
                        margin-top: 0.3rem;
                    }
                }
                .analyze_tip {
                    margin: 0.2rem 0;
                }
            }
            .iworks_msg {
                width: 12.65rem; // 466px
                line-height: 1;
                .message_image_content {
                    width: 100%;
                    height: 8.65rem;
                    border-radius: 0.75rem 0.75rem 0 0 !important;
                    img {
                        width: 100%;
                        border-radius: 0.75rem 0.75rem 0 0 !important;
                    }
                    [src="static/resource/images/loading.gif"] {
                        width: 1.5rem !important;
                        height: 1.5rem !important;
                    }
                }
                .iworks_protocol {
                    padding: 0.55rem 0.75rem 0.5rem; // 22 30
                    border-radius: 0 0 0.75rem 0.75rem;
                    background-color: rgb(241, 245, 248);
                    .iworks {
                        font-size: 0.55rem;
                        color: rgb(86, 200, 255);
                        margin-bottom: 0.375rem;
                        i {
                            width: 0.85rem;
                            height: 0.85rem;
                            border-radius: 50%;
                            vertical-align: middle;
                            margin-right: 0.3rem;
                        }
                        span {
                            vertical-align: middle;
                        }
                    }
                    .section {
                        margin-left: 1.2rem;
                        font-size: 0.55rem;
                        line-height: 0.95rem;
                        margin-top: 0.175rem;
                        color: rgb(101, 109, 112);
                    }
                    .line {
                        width: 100%;
                        height: 0.025rem;
                        background: rgb(226, 228, 231);
                        transform: scaleY(0.8);
                    }
                }
            }
            .iworks_comment {
                width: 12.65rem; // 466px
                line-height: 1;
                .message_image_content {
                    position: relative;
                    width: 100%;
                    height: 8.65rem;
                    border-radius: 0.75rem 0.75rem 0 0 !important;
                    img {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        top: 50%;
                        left: 50%;
                        border-radius: 0.75rem 0.75rem 0 0 !important;
                        transform: translate(-50%, -50%);
                        background-color: black;
                    }
                    [src="static/resource/images/loading.gif"] {
                        width: 1.5rem !important;
                        height: 1.5rem !important;
                    }
                    .patient_info {
                        color: #fff;
                        position: absolute;
                        left: 50%;
                        transform: translate(-50%, 0);
                        text-align: left;
                        font-size: 0.55rem;
                        bottom: 0.45rem;
                        padding: 0 0.75rem;
                        width: 10.15rem;
                        white-space: pre-wrap;
                        span {
                            margin-right: 0.7rem;
                            line-height: 0.8rem;
                        }
                        span:last-child {
                            margin-right: 0;
                        }
                    }
                }
                .comment_right {
                    color: #000;
                    padding: 0.725rem 0.75rem 0.525rem 0.75rem;
                    font-size: 0.55rem;
                    p {
                        font-size: 0.5rem; // 20px
                        color: rgb(101, 109, 112);
                        max-height: 2rem;
                        overflow: hidden;
                        // 评论者样式
                        .commentor {
                            color: rgb(0, 197, 157);
                            margin-top: 0.55rem;
                            margin-right: 0.3rem;
                        }
                    }
                }
            }
            .iworks_tag {
                width: 12.65rem; // 466px
                line-height: 1;
                .message_image_content {
                    position: relative;
                    width: 100%;
                    height: 8.65rem;
                    border-radius: 0.75rem 0.75rem 0 0 !important;
                    img {
                        width: 100%;
                        height: 8.65rem;
                        top: 50%;
                        left: 50%;
                        position: absolute;
                        border-radius: 0.75rem 0.75rem 0 0 !important;
                        transform: translate(-50%, -50%);
                        background-color: black;
                    }
                    [src="static/resource/images/loading.gif"] {
                        width: 1.5rem !important;
                        height: 1.5rem !important;
                    }
                    .patient_info {
                        color: #fff;
                        position: absolute;
                        left: 50%;
                        transform: translate(-50%, 0);
                        text-align: left;
                        font-size: 0.55rem;
                        bottom: 0.45rem;
                        padding: 0 0.75rem;
                        width: 10.15rem;
                        white-space: pre-wrap;
                        span {
                            margin-right: 0.7rem;
                            line-height: 0.8rem;
                        }
                        span:last-child {
                            margin-right: 0;
                        }
                    }
                }
                .comment_right {
                    color: #000;
                    padding: 0.725rem 0.75rem; // 29px 30px
                    font-size: 0.55rem;
                    p {
                        font-size: 0.5rem; // 20px
                        color: rgb(101, 109, 112);
                        max-height: 2rem;
                        overflow: hidden;
                        margin-top: 0.55rem;
                        // 新增标签文本样式
                        .wrap {
                            display: inline-block;
                            height: 0.9rem;
                            line-height: 0.9rem;
                            padding: 0 0.2rem;
                            background-color: #00c59d;
                            color: #fff;
                            border-radius: 5px;
                        }
                        .commentor {
                            color: rgb(101, 109, 112);
                        }
                    }
                }
            }
            .sound_msg {
                line-height: 1;
                position: relative;
                box-sizing: border-box;
                min-width: 4rem;
                height: 1.95rem;
                display: flex;
                align-items: center;
                max-width: 12.65rem;
                justify-content: flex-start;
                .unread_tip {
                    position: absolute;
                    right: 0.5rem;
                    top: 0.5rem;
                    border-radius: 50%;
                    background-color: rgb(255, 103, 92);
                    width: 0.25rem;
                    height: 0.25rem;
                    z-index: 2;
                }
                img {
                    margin-left: 1.4rem;
                }
                & > span {
                    position: absolute;
                }
                & > .text {
                    margin-left: 1.35rem;
                }
                & > .sound_img {
                    background: transparent url("../../../../static/resource/images/sound_other.svg") no-repeat;
                    height: 0.9rem;
                    width: 0.7rem;
                    fill: #fff;
                }
                &.playing {
                    & > .sound_img {
                        background: url("../../../../static/resource/images/sound_other.gif") no-repeat;
                        background-size: 0.67rem 0.9rem;
                    }
                }
            }
            .file_msg {
                background-color: rgb(241, 245, 248);
                box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
                padding: 0.75rem; // 31px 30px
                max-width: 12.65rem;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                flex-direction: row-reverse;
                .text_wrap {
                    max-width: 78%;
                    max-width: 78%;
                    display: flex;
                    flex-direction: column;
                    .file_name {
                        text-overflow: ellipsis;
                        white-space: pre-wrap;
                        color: rgb(26, 26, 26);
                        max-width: 7.5rem;
                        font-size: 0.75rem;
                    }
                    .file_size {
                        color: #aab4b9;
                        font-size: 0.55rem;
                    }
                    .expired_status {
                        color: red;
                        font-size: 0.55rem;
                        text-align: right;
                    }
                    .file_expires_time {
                        margin-top: 0.525rem; // 21px
                        color: #aab4b9;
                        font-size: 0.55rem;
                    }
                }
                .file_type_icon {
                    position: relative;
                    height: 2.4rem;
                    width: 1.9rem;
                    line-height: 2.4rem;
                    text-align: center;
                    font-size: 0.75rem;
                    border-radius: 5px;
                    margin-left: 0.75rem; // 30px
                    color: #fff;
                    .white_wrapper {
                        position: absolute;
                        background-color: #fff;
                        width: 1rem;
                        height: 1rem;
                        top: 0.7rem;
                        left: 0.45rem;
                        border-radius: 50%;
                    }
                    .downloading_wrapper {
                        position: absolute;
                        top: 0.675rem;
                        left: 0.425rem;
                        width: 1.05rem;
                        height: 1.05rem;
                        border-radius: 50%;
                        .downloading_container {
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            width: 0.525rem;
                            overflow: hidden;
                            .downloading_halfCir {
                                width: 0.525rem;
                                height: 1.05rem;
                                background-color: rgb(101, 109, 112);
                            }
                        }
                        .downloading_container1 {
                            left: 0.525rem;
                            .downloading_halfCir {
                                left: 0;
                                border-radius: 0 1.05rem 1.05rem 0;
                                transform-origin: 0 50%;
                            }
                        }
                        .downloading_container2 {
                            left: 0;
                            .downloading_halfCir {
                                border-radius: 0.525rem 0 0 0.525rem;
                                transform-origin: 0.525rem 0.525rem;
                            }
                        }
                    }
                }
                // 主蓝色调
                .word,
                .au,
                .mov,
                .psd,
                .temp,
                .bmp,
                .html,
                .url,
                .mid,
                .midi,
                .mp4,
                .png,
                .ini,
                .txt {
                    background-color: rgb(86, 200, 255);
                }
                .bg_word,
                .bg_au,
                .bg_mov,
                .bg_psd,
                .bg_temp,
                .bg_bmp,
                .bg_html,
                .bg_url,
                .bg_mid,
                .bg_midi,
                .bg_mp4,
                .bg_png,
                .bg_ini,
                .bg_txt {
                    background-color: rgb(61, 139, 179);
                }
                // 主红色调
                .powerpoint,
                .avi,
                .f4v,
                .fla,
                .flv,
                .pdf,
                .swf,
                .tif,
                .tiff,
                .wav,
                .jpeg,
                .jpg {
                    background-color: rgb(255, 103, 92);
                }
                .bg_powerpoint,
                .bg_avi,
                .bg_f4v,
                .bg_fla,
                .bg_flv,
                .bg_pdf,
                .bg_swf,
                .bg_tif,
                .bg_tiff,
                .bg_wav,
                .bg_jpeg,
                .bg_jpg {
                    background-color: rgb(179, 71, 64);
                }
                // 主绿色调
                .excel,
                .ape,
                .bat,
                .flac,
                .gif,
                .mkv,
                .rtf {
                    background-color: rgb(4, 153, 114);
                }
                .bg_excel,
                .bg_ape,
                .bg_bat,
                .bg_flac,
                .bg_gif,
                .bg_mkv,
                .bg_rtf {
                    background-color: rgb(5, 107, 80);
                }
                // 主橙色调
                .zip,
                .cab,
                .chm,
                .css,
                .eml,
                .hlp,
                .mp3,
                .pst,
                .wma,
                .wmv {
                    background-color: rgb(255, 177, 68);
                }
                .bg_zip,
                .bg_cab,
                .bg_chm,
                .bg_css,
                .bg_eml,
                .bg_hlp,
                .bg_mp3,
                .bg_pst,
                .bg_wma,
                .bg_wmv,
                .bg_bin,
                .bg_mpeg,
                .bg_mpg,
                .bg_unkown {
                    background-color: rgb(179, 123, 47);
                }
                // 主紫色调
                .bin,
                .mpeg,
                .mpg {
                    background-color: rgb(116, 128, 234);
                }
                // 未知文件主灰色
                .unkown {
                    background-color: rgb(170, 180, 185);
                    color: #fff;
                    font-size: 1.2rem;
                }
                // 4个字母的文件类型字体大小特殊处理
                .temp,
                .html,
                .midi,
                .tiff,
                .jpeg,
                .flac,
                .mpeg {
                    font-size: 0.7rem;
                }
            }
            .iworks_protocol_msg {
                color: #fff;
                background-color: rgb(116, 128, 234);
                padding: 0.75rem;
                width: 12.65rem;
                height: 5.55rem;
                line-height: 1;
                .iworks-fields {
                    display: flex;
                    flex-direction: column;
                    .iworks-field {
                        font-size: 0.55rem;
                        max-width: 7.5rem;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        padding-top: 0.3rem;
                        display: inline;
                    }
                }
                .iworks_icon {
                    float: right;
                    height: 2.4rem;
                    width: 1.9rem;
                    text-align: center;
                    font-size: 0.4rem;
                    border-radius: 5px;
                    background-color: rgb(86, 200, 255);
                    margin-top: 0.125rem;
                    padding: 0.5rem 0;
                    box-sizing: border-box;
                    i {
                        width: 0.75rem;
                        height: 0.75rem;
                        margin: auto;
                        display: block;
                    }
                    div {
                        margin-top: 1.425rem;
                        font-size: 0.55rem;
                        max-width: 7.5rem;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    span {
                        display: block;
                        font-size: 0.4rem;
                        margin-top: 0.25rem;
                    }
                }
                .text_wrap {
                    float: left;
                    margin: 0.025rem 0;
                    position: relative;
                    p {
                        text-overflow: ellipsis;
                        max-width: 7.5rem;
                        overflow: hidden;
                        white-space: nowrap;
                        max-width: 9rem;
                        font-size: 0.75rem;
                    }
                    div {
                        width: 7.5rem;
                        //                             overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        position: relative;
                        display: flex; // 去掉多余的空白
                        > span,
                        p {
                            display: inline-block;
                            margin-top: 1.425rem;
                            font-size: 0.55rem;
                            max-width: 7.5rem;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                        > span:nth-child(1) {
                            position: absolute;
                            margin-top: 0.525rem;
                            display: block;
                            max-width: 7.5rem;
                        }
                    }
                }
            }
            .homework_detail_msg {
                background: #333 !important;
                color: #fff;
                width: 13rem;
                .homework_detail {
                    position: relative;
                    .homework_title {
                        display: flex;
                        align-items: center;
                        font-size: 0.8rem;
                        margin-bottom: 0.4rem;
                        & > img {
                            margin-right: 0.5rem;
                        }
                    }
                    .homework_wraper {
                        display: flex;
                        .homework_left {
                            flex: 1;
                        }
                        .homework_right {
                            position: relative;
                            width: 2.2rem;
                            height: 3.3rem;
                            margin-right: 0.2rem;
                            img {
                                display: block;
                                width: 100%;
                                height: 100%;
                            }
                            p {
                                position: absolute;
                                bottom: 0;
                                left: 0;
                                width: 100%;
                                color: #fff;
                                height: 1.1rem;
                                line-height: 1.1rem;
                                text-align: center;
                                font-size: 0.6rem;
                            }
                        }
                    }
                    .sub_title {
                        color: #56c8ff;
                        font-size: 0.7rem;
                        margin-bottom: 0.2rem;
                    }
                    .content {
                        color: #a5afb4;
                        & > div {
                            font-size: 0.6rem;
                            line-height: 1.6;
                            white-space: nowrap;
                        }
                    }
                }
            }
            .img_msg {
                .message_image_content {
                    width: 11.65rem;
                    height: 8.65rem;
                    overflow: hidden;
                    border-radius: 0.75rem;
                    img {
                        width: 100%;
                        //                             border-radius: .75rem;
                    }
                    [src="static/resource/images/loading.gif"] {
                        width: 1.5rem !important;
                        height: 1.5rem !important;
                    }
                }
            }
            .error_img_msg {
                .message_image_content {
                    width: 6rem;
                    height: 4.5rem;
                    border-radius: 0.75rem;
                    img {
                        width: 100%;
                        border-radius: 0.75rem;
                    }
                }
                .comment_right {
                    p {
                        font-size: 0.4rem;
                        text-align: center;
                        line-height: 1.5rem;
                    }
                }
            }
            .video_msg {
                position: relative;
                min-height: 3rem;
                img {
                    height: auto;
                }
                .message_image_content {
                    width: 11.65rem;
                    height: 8.65rem;
                    border-radius: 0.75rem;
                    img {
                        width: 100%;
                    }
                    [src="static/resource/images/loading.gif"] {
                        width: 1.5rem !important;
                        height: 1.5rem !important;
                    }
                }
                .iworks_protocol {
                    padding: 0.55rem 0.75rem; // 22 30
                    line-height: 1;
                    .iworks {
                        font-size: 0.55rem;
                        color: rgb(86, 200, 255);
                        margin-bottom: 0.375rem;
                        i {
                            width: 0.85rem;
                            height: 0.85rem;
                            border-radius: 50%;
                            vertical-align: middle;
                            margin-right: 0.18rem;
                        }
                        span {
                            vertical-align: middle;
                        }
                    }
                    .section {
                        margin-left: 1.2rem;
                        font-size: 0.55rem;
                        line-height: 0.95rem;
                        margin-top: 0.175rem;
                        color: rgb(101, 109, 112);
                    }
                    .line {
                        width: 100%;
                        height: 0.025rem;
                        background: rgb(226, 228, 231);
                        transform: scaleY(0.8);
                    }
                }
            }
            .exam_msg {
                max-width: 13.2rem !important;
                width: 13.2rem;
            }
            // .exam_msg{
            //     position: relative;
            //     background: transparent;
            //     .message_image_content {
            //         width: 10.5rem;
            //         height: 7.8rem;
            //         background: #000;
            //         border-radius: .75rem;
            //         .resource_deleted{
            //             background: #fff;
            //             width: 100%;
            //             height: auto;
            //         }
            //         .patient_info{
            //             position: absolute;
            //             width: 222%;
            //             line-height: 1.8rem;
            //             left: 0;
            //             bottom: 0;
            //             background: #aab4b9;
            //             color: #fff;
            //             font-size: 1rem;
            //             padding: 0 1.2rem;
            //             box-sizing: border-box;
            //             transform: scale(.45);
            //             transform-origin: left bottom;
            //             span{
            //                 margin-right: .7rem;
            //             }
            //         }
            //     }
            //     &::after{
            //         content: '';
            //         display: block;
            //         position: absolute;
            //         width: 1.8rem;
            //         height: 7rem;
            //         right: -1.5rem;
            //         top: 0.6rem;
            //         background: url('/static/resource/images/exam_images.png');
            //         background-size: 100% 100%;
            //     }
            //     .piece_tip{
            //         position: absolute;
            //         left: 11.5rem;
            //         top: 7rem;
            //         display: flex;
            //         font-size: 0.6rem;
            //         color: #00c59d;
            //         align-items: center;
            //         white-space: nowrap;
            //         letter-spacing: .1rem;
            //         img{
            //             width: .5rem;
            //             height: .5rem;
            //             margin-right: .2rem;
            //         }
            //     }
            //     .iworks_protocol {
            //         padding: 0.55rem 0.75rem;// 22 30
            //         line-height: 1;
            //         width: 10.5rem;
            //         box-sizing: border-box;
            //         .iworks {
            //             font-size: .55rem;
            //             color: rgb(86, 200, 255);
            //             margin-bottom:0.375rem;
            //             i {
            //                 width: 0.85rem;
            //                 height: 0.85rem;
            //                 border-radius: 50%;
            //                 vertical-align: middle;
            //                 margin-right: 0.18rem;
            //             }
            //             span {
            //                 vertical-align: middle;
            //             }
            //         }
            //         .section {
            //             margin-left: 1.2rem;
            //             font-size: .55rem;
            //             line-height:0.95rem;
            //             margin-top: 0.175rem;
            //             color: rgb(101, 109, 112);
            //         }
            //         .line {
            //             width: 100%;
            //             height: 0.025rem;
            //             background: rgb(226, 228, 231);
            //             transform: scaleY(0.8);
            //         }
            //     }
            // }
            .is_iworks {
                .message_image_content {
                    border-radius: 0.75rem 0.75rem 0 0;
                    .iworks_protocol {
                        .line {
                            width: 100%;
                            height: 0.025rem;
                            background: rgb(226, 228, 231);
                            transform: scaleY(0.8);
                        }
                    }
                }
            }
            .no_padding {
                padding: 0;
                &:before {
                    display: none;
                }
            }
            .text_message {
                // user-select: text !important;
                // -webkit-user-select:text !important;
                min-width: 2.8rem;
                min-height: 1.95rem;
                text-align: left;
            }
        }
        .sending_spinner {
            float: right;
            margin-top: 0.4rem;
            @-webkit-keyframes van-spinner-rotate {
                0% {
                    -webkit-transform: rotate(0);
                    transform: rotate(0);
                }
                100% {
                    -webkit-transform: rotate(1turn);
                    transform: rotate(1turn);
                }
            }

            @keyframes van-spinner-rotate {
                0% {
                    -webkit-transform: rotate(0);
                    transform: rotate(0);
                }
                100% {
                    -webkit-transform: rotate(1turn);
                    transform: rotate(1turn);
                }
            }

            .van-spinner-snake {
                -webkit-animation: van-spinner-rotate 0.8s infinite linear;
                animation: van-spinner-rotate 0.8s infinite linear;
                border: 4px solid transparent;
                border-radius: 50%;
            }

            .van-spinner-snake {
                width: 0.6rem !important;
                height: 0.6rem !important;
                border-width: 0.1rem !important;
            }
        }
        .send_fail {
            font-size: 1.2rem;
            color: #ff6759;
            float: right;
        }
        // /* 引用消息样式 */
        .quote-message {
            background-color: rgba(240, 245, 249, 0.8);
            border-radius: 0.2rem;
            padding: 0.38rem;
            margin-top: 0.24rem;
            font-size: 0.57rem;
            border-left: 0.14rem solid #ccc;
            max-width: 13.2rem;
            min-width: 5.4rem;
            overflow: hidden;
            display: inline-flex;
            width: fit-content;
            flex-direction: column;

            .quote-sender {
                color: #666;
                font-weight: bold;
                margin-bottom: 0.2rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .quote-content-message {
                word-break: keep-all;
                overflow-wrap: break-word;
                white-space: normal;
                color: #666;
                max-height: 3rem;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                line-clamp: 2;

                &.image-content,
                &.video-content {
                    display: flex;
                    align-items: center;
                }

                &.image-content {
                    img {
                        max-width: 2.5rem;
                        max-height: 2.5rem;
                        margin-right: 0.3rem;
                        border-radius: 0.2rem;
                    }
                }

                &.video-content {
                    .video-thumbnail {
                        position: relative;
                        width: 2.4rem;
                        height: 2.4rem;
                        margin-right: 0.3rem;
                        background-color: #000;
                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                        }
                        .video-play-icon {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            color: white;
                            background-color: rgba(0, 0, 0, 0.5);
                            border-radius: 50%;
                            width: 1.25rem;
                            height: 1.25rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }
            }
        }
        &.self_chat {
            .avatar_wraper {
                float: right;
            }
            .message_item_wrapper {
                float: right;
                padding-right: 0.8rem;
                padding-left: 0;
                text-align: right;
                .message_item_name {
                    display: none;
                }
                .message_item_content {
                    background-color: #00c59d;
                    color: #fff;
                    border-radius: 0.75rem;
                    box-sizing: border-box;
                    max-width: 12.65rem;
                    &:before {
                        border-left: 0.3rem solid #00c59d;
                        border-right: none;
                        right: -0.3rem;
                        left: auto;
                    }
                    &.no_padding {
                        background-color: transparent;
                    }
                }
                .file_msg {
                    background-color: #f1f5f8;
                }
                .sound_msg {
                    line-height: 1;
                    position: relative;
                    box-sizing: border-box;
                    min-width: 4rem;
                    height: 1.95rem;
                    display: flex;
                    align-items: center;
                    max-width: 12.65rem;
                    justify-content: flex-end;
                    img {
                        margin-right: 0.7rem;
                        margin-left: 0;
                    }
                    & > .text {
                        position: static;
                        float: right;
                        text-align: center;
                        margin-right: 0.7rem;
                        margin-left: 0;
                    }
                    & > .sound_img {
                        position: static;
                        background: transparent url("../../../../static/resource/images/sound_me.svg") no-repeat;
                        width: 0.7rem;
                        height: 0.9rem;
                        fill: #fff;
                    }
                    .unread_tip {
                        display: none;
                    }
                    &.playing {
                        & > .sound_img {
                            background: url("../../../../static/resource/images/sound_me.gif") no-repeat;
                            background-size: 0.67rem 0.9rem;
                        }
                    }
                }
                .stop_sound {
                    display: flex;
                    .stop {
                        margin-left: 0.6rem;
                        display: inline-block;
                        width: 1.3rem;
                        height: 1.3rem;
                        .svg_icon_dial_cancel {
                            margin-top: 0.4rem;
                            fill: #fff;
                        }
                    }
                    span {
                        margin: 0 0.2rem;
                    }
                }
                // .exam_msg{
                //     &::after{
                //         left: -1.5rem;
                //         right: auto;
                //         background: url('/static/resource/images/exam_images2.png');
                //         background-size: 100% 100%;
                //     }
                //     .piece_tip{
                //         left: auto;
                //         right: 11.5rem;
                //     }
                // }
            }
            .quote-message {
                float: right;
            }
        }
    }
}
</style>
<style lang="scss">
.message_list_container {
    .van-list {
        & > div {
            //                         padding:0.2rem 0;
            &:nth-child(2) {
                margin-top: 1rem;
            }
            &:last-child {
                padding-bottom: 1rem;
            }
        }
    }

    .van-loadmore-bottom {
        padding: 0 !important;
    }

    .van-loadmore-text {
        font-size: 0.75rem;
        color: #aab4b9;
    }

    .delete_resource_tips {
        position: absolute;
        bottom: 0;
        font-size: 0.9rem;
    }
}

.multi_select_foot {
    display: flex;
    padding: 0.3rem 0;
    height: 1.8rem;
    border-top: 1px solid #ccc;
    position: fixed;
    bottom: 0;
    z-index: 10;
    width: 100%;
    background: #fff;
    align-items: center;
    span {
        line-height: 1;
        text-align: center;
        flex: 1;
        font-size: 0.8rem;
        word-break: keep-all;
    }
    .analyze {
        color: #00c59d;
    }
    .cancel {
        color: #ff6759;
    }
}
.chatMessageList {
    .van-checkbox-group {
        max-height: 200px;
        overflow: auto;
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 0.75rem;
        top: 0px;
        right: 0px;
        z-index: 1000;

        .van-checkbox {
            width: 100%;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            justify-content: flex-end;
            align-items: flex-end;
        }
    }

    .skeleton {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 11;
        background: #fff;
        overflow: hidden;
    }
}
@keyframes highlight-flash {
    0% {
        background-color: transparent;
    }
    25% {
        /* 可以用一个更饱和的颜色，但透明度低一些 */
        background-color: rgba(150, 200, 255, 0.5); /* 示例：淡蓝色 */
    }
    75% {
        /* 快速达到顶峰然后开始消失 */
        background-color: rgba(150, 200, 255, 0.5);
    }
    100% {
        background-color: transparent;
    }
}

.message_item {
    &.highlight {
        /* 非常短的时间，例如 0.8s 到 1.2s */
        animation: highlight-flash 1s linear; /* linear 或 ease-out 更直接 */
    }
}
</style>
