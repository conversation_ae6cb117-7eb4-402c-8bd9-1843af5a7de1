<template>
    <base-menu
        :menuShow.sync="isShowMenuVisible"
        ref="text_menu"
        :menuItems="menuItems"
        :eventData="eventData">
    </base-menu>
</template>
<script>
import base from '../lib/base'
import Tool from '@/common/tool.js'
import BaseMenu from './baseMenu.vue'
import { cloneDeep } from 'lodash'
import { htmlEscape } from '../lib/common_base'
export default {
    mixins: [base],
    name: 'TextMenuComponent',
    // 权限标识：启用权限响应式更新（因为使用了$checkPermission）
    permission: true,
    components: {
        BaseMenu
    },
    data(){
        return {
            isShowMenuVisible: false,
            showCopy: false,
            showPaste: false,
            showQuote: false,
            message: '',
            selectText: '',
            showWithdrawMessage: false,
            detailObj: {},
            eventData: {
                clientX: 0,
                clientY: 0
            }
        }
    },
    computed:{
        cid(){
            return this.$route.params.cid
        },
        menuItems() {
            const items = [];

            // 复制选项
            if (this.showCopy) {
                items.push({
                    label: this.$t('copy'),
                    handler: this.copy
                });
            }

            // 引用选项
            if (this.showQuote) {
                items.push({
                    label: this.$t('quote_title'),
                    handler: this.quote
                });
            }

            // 粘贴选项
            if (this.showPaste) {
                items.push({
                    label: this.$t('paste'),
                    handler: this.paste
                });
            }

            // 撤回消息选项
            if (this.showWithdrawMessage) {
                items.push({
                    label: this.$t('revocation_message'),
                    handler: this.withdrawMessage
                });
            }

            return items;
        }
    },
    watch: {
        isShowMenuVisible(newVal) {
            if (!newVal) {
                // 菜单隐藏时清除文本选择
                this.cancelSelection();
            }
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('showTextMenu').$on('showTextMenu',(data)=>{
                //点击右键时将文件放入currentFile
                this.message = Tool.decodeHTML(data.message);
                this.detailObj = data.detail||{};
                this.selectText = Tool.decodeHTML(data.selectText);
                this.isShowMenuVisible = true;

                // 设置事件数据
                if (data.event) {
                    this.eventData = {
                        clientX: data.event.clientX || data.event.center?.x || 0,
                        clientY: data.event.clientY || data.event.center?.y || 0
                    };
                }

                if(data.type==1){
                    this.showCopy = true;
                    this.showPaste = false;
                    // 检查引用权限：需要有 gmsg_id 且有权限
                    this.showQuote = this.detailObj && this.detailObj.gmsg_id && this.canQuoteMessage(this.detailObj);
                }else if(data.type==0){
                    this.showPaste = true;
                    this.showCopy = false;
                    this.showQuote = false;
                }
                this.ifNeedShowWithDraw();
            })
        })
    },
    methods:{
        // ==================== 权限检查方法 ====================
        /**
         * 检查消息权限的通用方法
         * @param {string} action - 权限动作 (withdraw, delete, quote, transmit等)
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        checkMessagePermission(action, message) {
            const context = {
                conversationId: this.cid,
                message: message
            };

            // 对于撤回权限，需要添加时间相关的上下文
            if (action === 'withdraw' && message && message.send_ts) {
                context.messageTime = new Date(message.send_ts).getTime();
                context.timeLimit = this.systemConfig.serverInfo.msg_withdrawal_max_time;
            }
            return this.$checkPermission(
                { conversationPermissionKey: `message.${action}` },
                context
            );
        },

        /**
         * 检查是否可以撤回消息（包含时间限制）
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        canWithdrawMessage(message) {
            return this.checkMessagePermission('withdraw', message);
        },

        /**
         * 检查是否可以引用消息
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        canQuoteMessage(message) {
            return this.checkMessagePermission('quote', message);
        },

        ifNeedShowWithDraw(){
            let msg = this.detailObj;
            if(msg && Object.keys(msg).length > 0){
                // 使用新的权限检查方法
                this.showWithdrawMessage = this.canWithdrawMessage(msg);
            } else {
                this.showWithdrawMessage = false;
            }
        },
        copy() {
            try {
                let textToCopy = '';

                // 确定要复制的文本
                if (this.selectText.length === 0) {
                    if (this.el) {
                        textToCopy = this.el.value;
                    } else {
                        this.$message.error(this.$t('copy_text_fail'));
                        return;
                    }
                } else {
                    textToCopy = this.selectText || this.message;
                }

                // 更新剪贴板状态
                this.$store.commit('clipboard/copyMessage', textToCopy);

                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;

                // 确保文本区域不可见
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);

                // 选择文本
                textArea.select();
                textArea.setSelectionRange(0, textToCopy.length);

                // 执行复制
                const successful = document.execCommand('copy');

                // 清理临时元素
                document.body.removeChild(textArea);

                if (successful) {
                    this.$message.success(this.$t('copy_text_success'));
                } else {
                    throw new Error('复制失败');
                }
            } catch (err) {
                console.error('复制失败:', err);
                this.$message.error(this.$t('copy_text_fail'));
            } finally {
                // 在复制完成后清除选择
                this.cancelSelection();
                this.isShowMenuVisible = false;
            }
        },
        paste(){
            let msg = this.$store.state.clipboard.clip_msg;
            window.vm.$root.eventBus.$emit('pasteClipboardMsg',msg);
            this.isShowMenuVisible = false;
        },
        cancelSelection() {
            // 清除所有文本选择
            const selection = window.getSelection();
            if (selection) {
                selection.removeAllRanges();
            }
        },
        withdrawMessage(){
            let msg = this.detailObj;
            if(msg && Object.keys(msg).length > 0){
                console.log(msg,'msg')
                // 使用新的权限检查方法
                if(this.canWithdrawMessage(msg)){
                    console.log(this.cid, msg);
                    this.tryToWithDrawMessages(this.cid, [msg]);
                } else {
                    this.$message.error(this.$t('exceeded_max_withdrawal'));
                }
            }
            this.isShowMenuVisible = false;
        },
        quote() {
            // 检查消息是否有 gmsg_id 和引用权限
            if (!this.detailObj || !this.detailObj.gmsg_id || !this.canQuoteMessage(this.detailObj)) {
                return;
            }
            let msg = cloneDeep(this.detailObj);
            // 发送引用消息到事件总线
            if (msg && Object.keys(msg).length > 0) {
                let quoteMessage = {
                    msg_body:htmlEscape(msg.original_msg_body),
                    gmsg_id:msg.gmsg_id,
                    msg_type:msg.msg_type,
                    sender_id:msg.sender_id,
                    nickname:msg.nickname || '',
                    send_ts:msg.send_ts || '',
                    resource_id:msg.resource_id || '',
                    url:msg.url|| '',
                    avatar:msg.avatar|| '',
                    thumb:msg.thumb|| '',
                    img_id:msg.img_id|| '',
                    img_has_gesture_video:msg.img_has_gesture_video|| '',
                    file_id:msg.file_id|| '',
                    file_name:msg.file_name|| '',
                    group_id:msg.group_id|| '',
                    original_file_name:msg.original_file_name|| '',
                    ultrasound_url: msg.ultrasound_url || '',
                    mp4FileUrl: msg.mp4FileUrl || '',
                }
                this.$root.eventBus.$emit('quoteMessage', quoteMessage);
            }

            // 隐藏菜单
            this.isShowMenuVisible = false;

            // 取消选区
            this.cancelSelection();
        },
    }
}
</script>
