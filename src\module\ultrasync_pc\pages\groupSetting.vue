<template>
<div>
    <CommonDialog class="group_setting"
        :title="$t('setting_title')"
        :show.sync="visible"
        width="600px"
        :close-on-click-modal="false"
        :modal="false"
        :submitText="$t('confirm_txt')"
        @closed="back"
        @submit="submit"
        v-loading="loading"
        submitBtnSize = "small"
    >

        <template v-if="isService">
            <div class="announcement">
                <p>{{service_description.title&&service_description.title[lan]||conversation.subject}}：</p>
                <div class="service_description_text">
                    <div v-for="item,i in serviceDescriptionText" :key="i" :class="{value:i%2==1}">
                        {{item}}<br/>
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="group_subject" v-show="isGroupChat">
                <span class="label">{{$t('group_chat_name')}}：</span>
                <el-input class="group_subject_input" v-model="subject" :placeholder="$t('group_chat_name')" maxlength="50"></el-input>
            </div>
            <div class="group_subject" v-show="isGroupChat">
                <span class="label">{{$t('group_nick_name')}}：</span>
                <el-input class="group_subject_input" v-model="groupNickname" :placeholder="$t('group_nick_name')" maxlength="50"></el-input>
            </div>
            <div class="announcement" v-if="isGroupChat">
                <p>{{$t('group_setting_announce')}}：</p>
                <el-input :row="3" :disabled="!hasGroupAnnouncementPermission" resize="none" type="textarea" class="group_subject_input" v-model="announcement" :placeholder="$t('no_announcement_tip')"></el-input>
            </div>

            <div class="setting_item">
                <span>{{$t('mute_notifications')}}：</span>
                <el-switch v-model="isMute"></el-switch>
            </div>
            <div class="setting_item">
                <span>{{$t('group_setting_view_mode')}}：</span>
                <el-radio v-model="view_mode" :label="0">{{$t('normal_view_mode')}}</el-radio>
                <el-radio v-model="view_mode" :label="1">{{$t('exam_view_mode')}}</el-radio>
            </div>

            <div class="setting_item" v-if="hasGroupPublicSettingPermission">
                <span>{{$t('group_setting_is_public')}}：</span>
                <el-radio v-model="is_public" :label="0">{{$t('private_group_text')}}</el-radio>
                <el-radio v-model="is_public" :label="1">{{$t('public_group_text')}}</el-radio>
                <span v-if="is_public==0">({{$t('private_group_tip')}})</span>
                <span v-if="is_public==1">({{$t('public_group_tip')}})</span>
            </div>
            <div class="setting_item" v-if="hasRegionLivePermission && (hasGroupRecordSettingPermission||!isGroupChat)">
                <span>{{$t('group_setting_is_live_record')}}：</span>
                <el-radio v-model="record_mode" :label="0" :disabled="!!conferenceState">{{$t('cancel_button_text')}}</el-radio>
                <el-radio v-model="record_mode" :label="1" :disabled="!!conferenceState">{{$t('confirm_button_text')}}</el-radio>
            </div>
            <div class="setting_item" v-show="isGroupChat">
                <el-button v-if="hasGroupStatisticsPermission&&hasRegionCloudStatisticPermission" type="primary" size="medium" @click="openGroupStatistic">{{$t('group_statistics')}}</el-button>
                <el-button type="primary" size="medium" @click="openBIDataShow" v-if="EnableQc_statistics&&isGroupChat">{{$t('bi_data_display')}}</el-button>
                <el-button type="primary" size="medium" @click="openGroupManage" v-if="hasGroupManagePermission">{{$t('group_manage')}}</el-button>
                <el-button v-if="isGroupChat&&hasExitGroupPermission" type="primary" size="medium" @click="ExitGroup" >{{$t('exit_group')}}</el-button>
                <el-button type="primary" size="medium" @click="openGroupQRCodeCard" >{{$t('group_qrcode_card_text')}}</el-button>
            </div>
        </template>
        <!-- <el-button type="primary" size="medium" class="submit_btn" @click="submit">{{$t('confirm_txt')}}</el-button> -->
        <router-view></router-view>
    </CommonDialog>

    <GroupQRCodeCard
        :show.sync="showQRCodeDialog"
        :conversation-id="cid"
    />
</div>

</template>
<script>
import base from '../lib/base'
import Tool from '@/common/tool.js'
import service from '../service/service'
import CommonDialog from "../MRComponents/commonDialog.vue";
import GroupQRCodeCard from "../pages/groupQRCodeCard.vue";
import { getLanguage } from '@/common/i18n';
export default {
    mixins: [base],
    name: 'GroupSettingPage',
    permission: true,
    components: {
        CommonDialog,
        GroupQRCodeCard
    },
    data(){
        return {
            visible: false,
            showQRCodeDialog: false,
            subject:'',
            voice_ctrl_mode:0,
            view_mode:0,
            record_mode:1,
            is_public:0,
            is_need_to_approve:0,
            conversation:{},
            cid:0,
            loading:false,
            applying:false,
            announcement:'',
            isMute:false,
            service_description:'',
            lan:'CN',
            groupNickname:''
        }
    },

    computed:{
        serviceDescriptionText(){
            let textArr = []
            if(this.service_description.content&&this.service_description.content[this.lan]){
                textArr = this.service_description.content[this.lan].split('\n')
            }else{
                textArr =[this.$t('no_service_tip')]
            }
            return textArr
        },
        isUnApplyTmpGroupMem(){
            var conversation = window.vm.$store.state.conversationList[this.cid];
            if(conversation){
                if(this.systemConfig.groupPublicState.SemiPublic == conversation.is_public){//半公开群
                    if(2 == conversation.attendeeList["attendee_" + this.user.uid].attendeeState){ //未申请的临时成员
                        return true;
                    }
                }
            }
            return false;
        },
        isGroupChat(){
            return this.conversation.is_single_chat==0;
        },
        isService(){
            return this.conversation.service_type!=0
        },
        EnableQc_statistics(){
            return this.$checkPermission({regionPermissionKey:'qcStatistics'})&&this.$store.state.systemConfig.serverInfo.qc_statistics
                &&this.$store.state.systemConfig.serverInfo.qc_statistics.enable
        },
        conferenceState(){
            return this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].conferenceState
        },
        hasRegionCloudStatisticPermission() {
            return this.$checkPermission({regionPermissionKey:'cloudStatistic'})
        },
        hasRegionLivePermission(){
            return this.$checkPermission({regionPermissionKey: 'live'})
        },
        hasGroupRecordSettingPermission(){
            return this.$checkPermission({regionPermissionKey: 'conference.record_setting'})
        },
        hasGroupManagePermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.manage'},{
                conversationId:this.cid,
            })
        },
        hasExitGroupPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'member.exit'},{
                conversationId:this.cid,
            })
        },
        hasGroupStatisticsPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'statistics.access'},{
                conversationId:this.cid,
            })
        },
        hasGroupPublicSettingPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.public_setting'},{
                conversationId:this.cid,
            })
        },
        hasGroupAnnouncementPermission(){
            this.conversationPermissionVersion;
            return this.$checkPermission({conversationPermissionKey: 'conversation.edit_announcement'},{
                conversationId:this.cid,
            })
        },
    },
    mounted(){
        this.$nextTick(()=>{
            this.visible = true
            this.initDate()
        })
    },

    methods:{
        initDate(){
            this.cid=this.$route.params.cid
            this.conversation=this.conversationList[this.cid]||{}
            this.voice_ctrl_mode=this.conversation.voice_ctrl_mode
            this.view_mode=this.conversation.view_mode
            this.is_public= this.systemConfig.groupPublicState.Private == this.conversation.is_public ? 0 : 1;
            this.isMute=this.conversation.preferences.is_mute?true:false;
            this.is_need_to_approve = this.systemConfig.groupPublicState.SemiPublic == this.conversation.is_public ? 1 : 0;
            this.record_mode=this.conversation.record_mode?1:0
            this.subject=this.conversation.subject
            this.groupNickname = this.conversation.attendeeList[`attendee_${this.user.uid}`].alias_name
            this.announcement=(this.conversation.announcement&&this.conversation.announcement.content)||''
            this.lan=getLanguage()
            this.service_description=this.conversation.service_description||{}
        },
        ExitGroup(){
            var that=this;
            let message = "";
            message = this.$t('user_exit_group_tip');//"所有数据将被清空，操作不可逆，是否继续？"
            this.$confirm(message,this.$t('tip_title'),{
                confirmButtonText:this.$t('confirm_button_text'),
                cancelButtonText:this.$t('cancel_button_text'),
                type:'warning'
            }).then(()=>{
                let data={};
                let message = "";
                message = "request_delete_attendees";
                data.attendees = [{uid:that.user.uid}];
                data.isActiveQuit = 1; //主动退群
                that.loading=true;
                that.conversation.socket.emit(message, data, function(is_succ, info){
                    console.log("callback " + message);
                    that.loading=false;
                    if(!is_succ){
                        console.log(info);
                        that.$message.error(that.$t('user_exit_group_fail'));
                    }
                });
            })
        },
        submitEditGroupNickname(){
            if(this.groupNickname!==this.conversation.groupNickname){
                window.main_screen.conversation_list[this.cid].setAttendeeAliasName({
                    aliasName:this.groupNickname
                },(res)=>{
                    if(res.error_code === 0){
                        this.$store.commit('conversationList/updateAttendeeAliasName',{
                            uid:this.user.uid,
                            cid:this.cid,
                            aliasName:this.groupNickname
                        })
                    }
                });
            }
        },
        submit(){
            var that=this;
            if (this.subject!=this.conversation.subject) {
                //修改了群名
                this.conversation.socket.emit('edit_subject',this.subject)
            }
            this.submitEditGroupNickname()
            if (this.view_mode!=this.conversation.view_mode) {
                //修改了视图模式
                if (this.user.uid==this.conversation.creator_id) {
                    //会话创建者可以设置默认视图
                    that.conversation.socket.emit('edit_view_mode',{
                        view_mode:that.view_mode
                    },function(is_succ,data){
                        //修改成功
                        if (is_succ) {
                            that.$store.commit('conversationList/updateViewMode',{
                                cid:that.cid,
                                value:that.view_mode
                            });
                            that.$root.eventBus.$emit('shouldTogglePageType',that.view_mode)
                        }
                    })
                }else{
                    //会话参与者可以设置默认视图本地化设置
                    let settingsObj=JSON.parse(window.localStorage.getItem(`user_${this.user.uid}_viewmode`)||"{}");
                    settingsObj[`group_${this.cid}`]=this.view_mode;
                    window.localStorage.setItem(`user_${this.user.uid}_viewmode`,JSON.stringify(settingsObj));
                    that.$store.commit('conversationList/updateViewMode',{
                        cid:that.cid,
                        value:this.view_mode
                    });
                }
            }

            let is_public = this.systemConfig.groupPublicState.Private;
            if (1 == this.is_public) {
                if (0 == this.is_need_to_approve) {
                    is_public = this.systemConfig.groupPublicState.Public;
                } else {
                    is_public = this.systemConfig.groupPublicState.SemiPublic;
                }
            } else {
                is_public = this.systemConfig.groupPublicState.Private;
            }
            if (is_public!=this.conversation.is_public) {
                //修改了群公开属性
                var data={
                    gid:that.cid,
                    is_public:is_public
                }
                that.conversation.socket.emit('edit_public',data,function(is_succ,data){
                    //修改成功
                    that.$store.commit('conversationList/updateIsPublic',{
                        cid:that.cid,
                        is_public:is_public
                    })
                })
            }
            if (this.record_mode!=this.conversation.record_mode) {
                //修改了录制设置
                var data={
                    gid:that.cid,
                    record_mode:that.record_mode?1:0
                }
                that.conversation.socket.emit('edit_record_mode',data,function(is_succ,data){
                    if(is_succ){
                        //修改成功
                        that.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:that.cid,
                            record_mode:that.record_mode
                        });
                    }else{//修改失败
                        that.record_mode = !that.record_mode;
                    }
                })
            }
            if (this.voice_ctrl_mode!=this.conversation.voice_ctrl_mode) {
                //修改了语音设置
                var data={
                    gid:parseInt(that.cid),
                    voice_ctrl_mode:that.voice_ctrl_mode?1:0
                }
                that.conversation.socket.emit('edit_voice_ctrl_mode',data,function(is_succ,data){
                    if(is_succ){
                        //修改成功
                        that.$store.commit('conversationList/updataVoiceCtrlMode', {
                            cid:that.cid,
                            voice_ctrl_mode:that.voice_ctrl_mode
                        });
                    }else{//修改失败
                        that.voice_ctrl_mode = !that.voice_ctrl_mode;
                    }
                })
            }
            if (this.hasGroupAnnouncementPermission&&(!this.conversation.announcement&&this.announcement)||(this.conversation.announcement&&this.announcement!=this.conversation.announcement.content)) {
                //修改了群说明
                var params={
                    content:that.announcement
                }
                that.conversation.socket.emit('edit_announcement',params,function(is_succ,data){
                    //修改成功
                    if (is_succ) {
                        data.cid=that.cid;
                        that.$store.commit('conversationList/updateAnnounce',data);
                    }
                })
            }
            let is_mute=this.isMute?1:0
            if (is_mute!=this.conversation.preferences.is_mute) {
                //修改了消息免打扰
                this.conversation.socket.emit('set_attendee_preferences',{
                    is_mute:is_mute
                },(result)=>{
                    if(result.error_code){
                        this.$message.error(that.$t('update_failed_text'))
                    }else{
                        this.$store.commit('conversationList/updateMuteToConversation',{
                            is_mute:is_mute,
                            cid:that.cid
                        })
                        this.$store.commit('chatList/updateMuteToChatList',{
                            is_mute:is_mute,
                            cid:that.cid
                        })
                    }
                })
            }
            this.$message.success(this.$t('update_success_text'))
            this.back();
        },
        openGroupStatistic(){
            const requestConfig = this.systemConfig.server_type
            let ajaxServer= requestConfig.protocol+requestConfig.host+requestConfig.port;
            let lang=getLanguage();
            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }

            const url = Tool.transferLocationToCe(`${ajaxServer}/statistic.html#/index/live?dataFrom=group&id=${this.cid}&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${lang}`)
            window.localStorage.setItem('stat_query', JSON.stringify({dataFrom: 'group',id: this.cid}));
            if([1,5].includes(window.clientType)) {
                window.open(url,'blank');
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({url})
            }
        },
        testBackEndApi() {
            window.main_screen.conversation_list[this.cid].testBackEndApi({agoraUid: 1031}, res => {
                console.log(res.data)
            })
        },
        openBIDataShow(){
            var that = this;
            console.log('group_id:',this.cid)
            const requestConfig = this.systemConfig.server_type
            let ajaxServer= requestConfig.protocol+requestConfig.host+requestConfig.port;
            let lang=getLanguage();
            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }
            const url = Tool.transferLocationToCe(`${ajaxServer}/statistic.html#/remote_ultrasound_data_center?dataFrom=group&id=${this.cid}&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${lang}`)
            if(Tool.ifBrowserClientType(that.systemConfig.clientType)) {
                window.open(url,'blank');
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({url})
            }
        },
        openGroupManage(){
            this.$router.push(this.$route.fullPath+'/group_manage')
        },
        openGroupQRCodeCard(){
            this.showQRCodeDialog = true;
        }
    }
}
</script>

<style lang="scss">
.group_setting.el-dialog__wrapper{
    // .el-dialog{
    //     height:auto !important;
    //     .el-dialog__body{
    //         height: auto;
    //         padding-bottom: 50px;
    //     }
    // }
    .group_subject{
        display:flex;
        margin:8px 0;
        .group_subject_input{
            flex:1;
        }
        .label{
            line-height:40px;
            min-width: 80px;
        }
    }
    .announcement{
        p{
            line-height:2;
        }
        .service_description_text{
            line-height: 1.8;
            padding: 6px 10px;
            border: 1px solid #aaa;
            border-radius: 6px;
            .value{
                text-indent:20px;
            }
        }
    }
    .setting_item{
        line-height: 40px;
        font-size: 16px;
    }
    // .submit_btn{
    //     position: absolute;
    //     right: 10px;
    //     bottom: 10px;
    // }
    .el-radio__inner::after{
        transition:none;
    }
}
</style>
