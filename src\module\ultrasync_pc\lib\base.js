import Tool from '@/common/tool.js'
import {formatString,getAudioDeviceExceptionInfo,htmlEscape,getResourceTempStatus,judgeIfCurrentYear,} from '../lib/common_base'
import moment from 'moment'
import {CHAT_TYPE} from '../lib/constants'
import {getLanguage} from '@/common/i18n'
import permissionManager from '@/common/permission/PermissionManager.js'
export default {
    data(){
        return {
            conversationList:this.$store.state.conversationList,
            systemConfig:this.$store.state.systemConfig,
            globalParams:this.$store.state.globalParams,
            dynamicGlobalParams:this.$store.state.dynamicGlobalParams,
            user:this.$store.state.user,
            gallery:this.$store.state.gallery,
            currentOpenConversationInfo:null
        }
    },
    created(){

    },
    mounted(){
        this.$root.eventBus.$off('createConversationFail').$on('createConversationFail',this.createConversationFail)
    },
    computed:{
        isCef(){
            return this.globalParams.isCef
        },
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment
        },
        isWorkStation(){
            return this.isCef&&Tool.ifAppWorkstationClientType(this.systemConfig.clientType)
        },
        isPCBrowser(){
            return Tool.checkAppClient('PCBrowser')
        },
    },
    methods:{
        back(length){
            console.log('emit back')
            if(typeof length === 'number'){
                this.$router.go(length*-1)
            }else{
                this.$router.back()
            }
            return this;
        },
        setDefaultImg(list){
            for(let item of list){
                if(!item.avatar && item.type == 3) {
                    // 群落
                    item.avatar='static/resource_pc/images/groupset.png'
                    item.avatar_local='static/resource_pc/images/groupset.png'
                }                else if(item.is_single_chat==0){
                    if(!item.avatar) {
                        // 群聊
                        item.avatar='static/resource_pc/images/b1.png'
                        item.avatar_local='static/resource_pc/images/b1.png'
                    }
                }else{
                    if (item.service_type==this.systemConfig.ServiceConfig.type.FileTransferAssistant) {
                        //文件助手
                        item.avatar='static/resource_pc/images/transfer.png'
                        item.avatar_local='static/resource_pc/images/transfer.png'
                    } else {
                        //单聊
                        const userStatus=item.user_status||item.status
                        if (userStatus===this.systemConfig.userStatus.Destroy) {
                            item.avatar='static/resource_pc/images/destroy.png'
                            item.avatar_local='static/resource_pc/images/destroy.png'
                            item.sex=2;
                            if (item.nickname) {
                                item.nickname=item.nickname.replace('destroy',this.$t('destroy_replace_text'));
                            }else if (item.subject) {
                                item.subject=item.subject.replace('destroy',this.$t('destroy_replace_text'));
                            }
                            continue ;
                        }
                        if (!item.avatar||/user\/avatar\/default\/0\.png/.test(item.avatar)||/static\/resource\/images\//.test(item.avatar)) {
                            if(item.sex==1){
                                item.avatar='static/resource_pc/images/b3-1.png'
                                item.avatar_local='static/resource_pc/images/b3-1.png'
                            }else{
                                item.avatar='static/resource_pc/images/b2-1.png'
                                item.avatar_local='static/resource_pc/images/b2-1.png'
                            }
                        }
                        if (item.sex==1) {
                            item.default_avatar='static/resource_pc/images/b3-1.png'
                        }else{
                            item.default_avatar='static/resource_pc/images/b2-1.png'
                        }
                    }

                }
            }
            return list;
        },
        replaceUrlsWithLinks(oText) {
            const aRegex = /<a\b[^>]*>(.*?)<\/a>/g;
            let text = oText.replace(aRegex, function(match, url) {
                return url;
            });
            const regex = /(<a[^>]*>.*?<\/a>)|((https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
            const wrappedText = text.replace(regex, (match, p1, p2) => {
                if (p1) {
                // 已经在 <a> 标签内的链接，直接返回
                    return match;
                } else {
                // 将链接包裹在 <a> 标签中
                    return `<a href="javascript:void(0)" style="text-decoration:underline;word-break: break-all;" class="openLinkByDefaultBrowser" data-url="${p2}">${p2}</a>`
                }
            });
            return wrappedText;
        },
        parseMessageBody(messageBody=''){
            messageBody=htmlEscape(messageBody)
            messageBody=messageBody.replace(/\n/g,'<br/>')
            messageBody = this.replaceUrlsWithLinks(messageBody)
            return messageBody
        },
        changeDefaultImg(changeObj){
            if (changeObj.sex!=undefined) {
                if (/static\/resource_pc/.test(this.user.avatar)) {
                    let item={};
                    if(changeObj.sex==1){
                        item.avatar='static/resource_pc/images/b3-1.png'
                        item.avatar_local='static/resource_pc/images/b3-1.png'
                    }else{
                        item.avatar='static/resource_pc/images/b2-1.png'
                        item.avatar_local='static/resource_pc/images/b2-1.png'
                    }
                    this.$store.commit('user/updateUser',item)
                }
            }
        },
        parseObjToArr(data){
            var arr=[]
            for(let item in data){
                arr.push({...data[item],id:item})
            }
            return arr
        },
        async openConversation(id,open_type,start_type,callback){
            //open_type ,1:从会话列表打开 2:从群组列表打开 3:从好友列表打开
            //           4:点击图片进入画廊 5:新建群后自动打开 6:单聊变群聊后自动打开
            //           7:点击搜索聊天记录打开会话 8:电视墙打开会话 9:群落设备墙打开画廊 10:不打开会话
            //           11:加好友成功自动打开会话发送提示消息，12：跳转历史记录
            var that=this
            this.currentOpenConversationInfo = {
                id,
                open_type,
                start_type,
                callback
            }
            if(open_type==3){
                let fid=id
                let chatList=this.$store.state.chatList.list
                for(let chat of chatList){
                    if (chat.fid==fid) {
                        //会话列表有则从会话列表打开会话
                        if (start_type&&start_type.type==this.systemConfig.start_type.RequestUltrasoundDesktopByMonitorWall) {
                            //电视墙开启会话
                            this.openConversation(chat.cid,8,start_type,callback)
                        }else{
                            this.openConversation(chat.cid,2,start_type,callback)
                        }
                        return;
                    }
                }
                console.log('create conversation')
                //会话列表没有则新开一个会话
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:start_type,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async function(is_succ,cid){
                    if (is_succ) {
                        let transmitQueue=that.$root.transmitQueue['f-'+fid];
                        that.$store.commit('conversationList/initConversation',cid)
                        that.$store.commit('examList/initExamObj',cid)
                        await Tool.handleAfterConversationCreated(cid,'openConversation')
                        callback&&callback(is_succ,cid)
                        if (start_type&&start_type.type==that.systemConfig.start_type.SendTo) {
                            return
                        }
                        //新开的会话，转发数据存在id下，应转移到cid下
                        if(transmitQueue) {
                            that.$root.transmitQueue[cid]=transmitQueue;
                        }else{
                            if (start_type&&start_type.type==that.systemConfig.start_type.RequestUltrasoundDesktopByMonitorWall) {
                                that.$router.replace(`/main/index/chat_window/${cid}/tv_wall`)
                            }else{
                                that.$router.push(`/main/index/chat_window/${cid}`)
                                that.$root.eventBus.$emit('changeSelectChat',{cid})
                            }

                        }

                    }else{
                        callback&&callback(is_succ)
                        that.$message.error(this.$t('start_conversation_error'))
                    }

                })
            }else if(open_type==11){
                let fid=id
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:start_type,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async function(is_succ,cid){
                    if (is_succ) {
                        that.$store.commit('conversationList/initConversation',cid)
                        that.$store.commit('examList/initExamObj',cid)
                        await Tool.handleAfterConversationCreated(cid,'openConversation')
                        callback&&callback(is_succ,cid)
                    }else{
                        that.$message.error(that.$t('start_conversation_error'))
                        callback&&callback(is_succ)
                    }
                })
            }else{
                //open_type1,2,4,5,6,7
                let cid=id
                if(this.conversationList[cid]&&this.conversationList[cid].hasOwnProperty('type')&&window.main_screen.conversation_list[cid]){
                    //会话已开启过
                    await Tool.handleAfterConversationCreated(cid,'openConversation')
                    callback&&callback(true,this.conversationList[cid])

                }else{
                    this.$store.commit('conversationList/initConversation',cid)
                    that.$store.commit('examList/initExamObj',cid)
                    if (this.$root.endMessageIds[cid]) {
                        start_type={
                            msg_id:this.$root.endMessageIds[cid],
                            type:this.systemConfig.start_type.HistoryChatMessage
                        }
                    }
                    this.$root.socket.emit("request_start_conversation",cid,start_type,async (is_succ,data)=>{
                        if(is_succ){
                            await Tool.handleAfterConversationCreated(cid,'openConversation')
                            callback&&callback(is_succ,data)
                        }else{
                            callback&&callback(is_succ)
                        }

                    })
                }
                if (start_type&&start_type.type==this.systemConfig.start_type.SendTo) {
                    return
                }
                switch(open_type){
                case 1:
                    this.$router.replace(`/main/index/chat_window/${cid}`);
                    break;
                case 2:
                case 5:
                case 7:
                    this.$router.replace(`/main/index/chat_window/${cid}`);
                    this.$root.eventBus.$emit('changeSelectChat',{cid:cid})
                    break;
                case 12:
                    this.$router.replace(`/main/index/chat_window/${cid}/`);
                    this.$root.eventBus.$emit('changeSelectChat',{cid:cid})
                    this.$router.replace(`/main/index/chat_window/${cid}/chat_history_search_list`);
                    break;
                case 4:
                    this.$router.push(`/main/index/chat_window/${cid}/gallery`);
                    break;
                case 6:
                    history.go(-1)
                    setTimeout(()=>{
                        that.$router.push(`/main/index/chat_window/${cid}`)
                        this.$root.eventBus.$emit('changeSelectChat',{cid:cid})
                    },200)
                    break;
                case 8:
                    that.$router.replace(`/main/index/chat_window/${cid}/tv_wall`)
                    break;
                case 9:
                    let groupset_id=that.$route.params.groupset_id
                    that.$router.push(`/main/index/chat_window/${cid}/groupset_wall/${groupset_id}/gallery`)
                    break;
                case 10:
                    break;
                }
            }
        },
        async openConversationByUserId(id,callback){
            let fid=id
            let chatList=this.$store.state.chatList.list
            let cid = 0
            for(let chat of chatList){
                if (chat.fid==fid) {
                    //会话列表有则从会话列表打开会话
                    cid = chat.cid
                    break;
                }
            }
            if(cid){
                if(this.conversationList[cid]&&this.conversationList[cid].hasOwnProperty('type')&&window.main_screen.conversation_list[cid]){
                    //会话已开启过
                    await Tool.handleAfterConversationCreated(cid,'openConversation')
                    callback&&callback(true,cid)
                }else{
                    this.$store.commit('conversationList/initConversation',cid)
                    this.$store.commit('examList/initExamObj',cid)
                    this.$root.socket.emit("request_start_conversation",cid,undefined,async (is_succ,data)=>{
                        if(is_succ){
                            await Tool.handleAfterConversationCreated(cid,'openConversation')
                            callback&&callback(is_succ,cid)
                        }else{
                            callback&&callback(is_succ,cid)
                        }

                    })

                }
            }else if(fid){
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:null,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async(is_succ,cid)=>{
                    if (is_succ) {
                        let transmitQueue=this.$root.transmitQueue['f-'+fid];
                        this.$store.commit('conversationList/initConversation',cid)
                        this.$store.commit('examList/initExamObj',cid)
                        await Tool.handleAfterConversationCreated(cid,'openConversation')
                        callback&&callback(is_succ,cid)
                        if(transmitQueue) {
                            this.$root.transmitQueue[cid]=transmitQueue;
                        }

                    }else{
                        callback&&callback(is_succ)
                        this.$message.error(this.$t('start_conversation_error'))
                    }

                })
            }
        },
        openGallery(file,type=1,list){
            var cid=file.group_id;
            var param={
                openFile:file,
                openType:type
            }
            param.list=list
            this.$store.commit('gallery/setGallery',param);
            this.openConversation(cid,4);
        },
        callImageMenu(event,file,from){
            // if(!file.url && !file.url_local){
            //     return
            // }
            console.log('callImageMenu',file,from)
            event.preventDefault()
            if (this.chatType===CHAT_TYPE['GALLERY']) {
                //画廊不唤起菜单
                return
            }
            if(Number(window.vm.$root.currentLiveCid)){
                this.$message.error(this.$t('playing_video_tip'))
                return
            }
            let params={
                event:event,
                file:file,
                from:from
            }
            if (file.is_private) {
                params.openType=1
            }else if (file.ai_analyze) {
                params.openType=2
            }else{
                params.openType=0
            }
            console.log('callImageMenu',params)
            window.vm.$root.eventBus.$emit('showImageMenu',params)
        },
        callLiveMenu(event,file,reviewType,from){
            event.preventDefault()
            if (this.chatType===CHAT_TYPE['GALLERY']) {
                //画廊不唤起菜单
                return
            }
            let params={
                event:event,
                file:file,
                from:from,
                openType:3,
                reviewType
            }
            window.vm.$root.eventBus.$emit('showImageMenu',params)
        },
        callTextMenu(event,message,type,detail){
            event.preventDefault();

            let el = event.target;
            let selectText = window.getSelection().toString();
            let selection = window.getSelection();
            // 判断用户是否是自行选中该消息
            if(!selectText && type==1){
                let range = document.createRange();
                range.selectNodeContents(el);
                selection.removeAllRanges();
                selection.addRange(range);
                selectText = window.getSelection().toString();
            }
            if (this.chatType===CHAT_TYPE['GALLERY']) {
                //画廊不唤起菜单
                return
            }
            let params={
                event:event,
                message:message,
                selectText:selectText,
                type:type,
                detail
            }
            window.vm.$root.eventBus.$emit('showTextMenu',params,selectText);
            this.$root.eventBus.$off('pasteClipboardMsg').$on('pasteClipboardMsg',(data)=>{
                let selection = window.getSelection()
                let range = selection.getRangeAt(0)
                let node = range.createContextualFragment(data)
                range.insertNode(node)
                selection.collapse(range.endContainer,range.endOffset)
                range.collapse(false);
            })

        },
        tryToWithDrawMessages(cid, list, callback){ // 撤回前条件判断
            let that = this;
            if (0 == list.length) {
                return;
            }

            let uid = that.$store.state.user.uid;
            let total_count = 0;
            let sent_by_others_count = 0;
            let queue = that.$root.withDrawList;
            for (let i in list) {
                let item = list[i];
                total_count++;
                let senderId = item.sender_id||item.creator_id
                if (senderId != uid) {
                    sent_by_others_count++;
                    continue;
                }

                if (!queue[item.group_id]) {
                    queue[item.group_id] = [];
                }

                let obj = {...item}
                Object.keys(obj).map(ele=>{
                    let hasResourceId = ele ==='resource_id'&&obj[ele]
                    let hasGmsgId = ele ==='gmsg_id'&&obj[ele]
                    let hasMsgType = ele ==='msg_type'&&obj[ele]
                    if (hasResourceId||hasGmsgId||hasMsgType) {
                        obj[ele] = parseInt(obj[ele]);
                    }
                    return ele
                })
                queue[item.group_id].push(obj);
            }

            if (0 < sent_by_others_count) {
                let tip = "";
                if (1 == total_count) {
                    tip = that.$t('withdraw_chat_message_fail_sended_by_others');
                } else {
                    tip = formatString(that.$t('withdraw_chat_message_fail_sended_by_others'), {1:sent_by_others_count});
                }

                this.$message.error(tip)
            } else {
                that.withDrawMessages(cid, callback);
            }
        },
        withDrawMessages(cid, callback){
            let that = this;
            let queue = that.$root.withDrawList;
            let withDrawTimeList = that.$root.withDrawTimeList;
            for(let i in queue){
                if (cid && cid != i) {
                    continue;
                }

                if (0 == queue[i].length) {
                    continue;
                }

                let conversation=that.conversationList[i];
                if (conversation && conversation.socket) {
                    conversation.socket.emit("withdraw_chat_message", queue[i][0], function (is_succ, data) {
                        if(!is_succ){
                            this.$message.error(that.$t('tip_title'),that.$t('withdraw_chat_message_fail'));
                        }
                    });
                    let currentTimeStamp = new Date().getTime();
                    let gmsg_id = queue[i][0].gmsg_id;
                    withDrawTimeList[gmsg_id] = currentTimeStamp;
                    delete queue[i];
                    callback && callback(false);
                } else {
                    window.main_screen.controller.emit("request_start_conversation", i);
                }
            }
        },
        tryToDeleteMessages(cid, list, callback){
            let that = this;
            if (0 == list.length) {
                return;
            }
            let uid = that.user.uid;
            let total_count = 0;
            let sent_by_others_count = 0;
            let queue = that.$root.deleteQueue;
            // 使用会话权限进行判断
            for (let i in list) {
                let item = list[i];
                total_count++;
                const canDelete = permissionManager.checkPermission({
                    conversationPermissionKey: 'message.delete'
                }, {
                    conversationId: cid,
                    message: item,
                });
                if (!canDelete) {
                    sent_by_others_count++;
                    continue;
                }
                if (!queue[item.group_id]) {
                    queue[item.group_id] = [];
                }
                var param = {};
                if (item.resource_id) {
                    item.resource_id = parseInt(item.resource_id);
                }
                if (item.gmsg_id) {
                    item.gmsg_id = parseInt(item.gmsg_id);
                }
                if (item.msg_type) {
                    item.msg_type = parseInt(item.msg_type);
                }
                queue[item.group_id].push(item);
            }
            if (0 < sent_by_others_count) {
                let tip = "";
                if (1 == total_count) {
                    tip = that.$t('delete_chat_message_fail_sended_by_others');
                } else {
                    tip = formatString(that.$t('delete_chat_message_warm_sended_by_others'), {1:sent_by_others_count});
                }
                this.$MessageBox.alert(tip)
            } else {
                that.deleteMessages(cid, callback);
            }
        },
        deleteMessages(cid, callback){
            let that = this;
            let queue = that.$root.deleteQueue;

            for(let i in queue){
                if (cid && cid != i) {
                    continue;
                }

                if (0 == queue[i].length) {
                    continue;
                }
                let conversation=that.conversationList[i];
                if (conversation && conversation.socket) {
                    conversation.socket.emit("delete_chat_messages", queue[i], function (is_succ, data) {
                        if(!is_succ){
                            that.$message.error(that.$t('tip_title'),that.$t('delete_chat_message_fail'));
                        }
                    });
                    delete queue[i];
                    callback && callback(false);
                } else {

                    window.main_screen.controller.emit("request_start_conversation", i);
                }
            }
        },
        async deleteResourceByGroupId(file,gmsg_id = 0){
            return new Promise((resolve,reject)=>{
                console.log("deleteResourceByGroup",file);
                let params={
                    resource_id:file.resource_id,
                }
                if(gmsg_id){
                    params.gmsg_id = gmsg_id
                }
                window.main_screen.conversation_list[file.group_id].deleteResourceByGroup(params,(res)=>{
                    console.error(res)
                    if(res.error_code){
                        this.$message.error(this.$t(res.error_msg)||this.$t('delete_chat_message_fail'))
                        reject(false)
                    }else{
                        this.$message.success(this.$t('operate_success'))
                        resolve(true)
                    }
                })
            })
        },
        async deleteExam(file){
            return new Promise((resolve,reject)=>{
                console.log("deleteExam",file);
                let params={
                    exam_id:file.exam_id,
                }
                window.main_screen.conversation_list[file.group_id].deleteExam(params,(res)=>{
                    console.error(res)
                    if(res.error_code){
                        this.$message.error(this.$t(res.error_msg)||this.$t('delete_case_fail'))
                        reject(false)
                    }else{
                        this.$message.success(this.$t('delete_case_success'))
                        resolve(true)
                    }
                })
            })
        },
        setErrorImage(file){
            if(!file.img_encode_type){
                return
            }
            if(file.error_image){
                return
            }
            const encode_type = file.img_encode_type.toUpperCase()
            if(encode_type === 'DCM'){
                this.$set(file,'error_image','static/resource_pc/images/file_icon/dcm.png')
                this.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                    resource_id:file.resource_id,
                    data:{
                        error_image:'static/resource_pc/images/file_icon/dcm.png',
                        loaded:true
                    }
                });
            }else if(encode_type === 'PDF'){
                this.$set(file,'error_image','static/resource_pc/images/file_icon/pdf.png')
                this.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                    resource_id:file.resource_id,
                    data:{
                        error_image:'static/resource_pc/images/file_icon/pdf.png',
                        loaded:true
                    }
                });
            }else{
                this.$set(file,'error_image','static/resource_pc/images/slt_err.png')
                this.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                    resource_id:file.resource_id,
                    data:{
                        error_image:'static/resource_pc/images/file_icon/slt_err.png',
                        loaded:true
                    }
                });

            }
            file.loaded=true
            this.$forceUpdate()
        },
        getRenderImageUrl(file,field=''){
            if(getResourceTempStatus(file.resource_id,'error_image')){
                return getResourceTempStatus(file.resource_id,'error_image')
            }else if(getResourceTempStatus(file.resource_id,'loaded')){
                if(field){
                    return getResourceTempStatus(file.resource_id,field)
                }
                return getResourceTempStatus(file.resource_id,'realUrl')
            }else{
                return file.url
            }
        },
        getDownloadUrl(imageObj){
            let downloadUrl=''
            switch(imageObj.msg_type){
            case this.systemConfig.msg_type.Image:
                downloadUrl=imageObj.url.replace(imageObj.thumb,"");
                break;
            case this.systemConfig.msg_type.OBAI://ai
                downloadUrl=imageObj.url.replace("thumbnail.jpg",`ScreenShot.jpg`);
                break;
            case this.systemConfig.msg_type.Frame://pdf,dcm
                downloadUrl=imageObj.url.replace("thumbnail.jpg",`SingleFrame.${imageObj.img_encode_type}`);
                break;
            case this.systemConfig.msg_type.Video:
                downloadUrl=imageObj.url.replace(imageObj.thumb,"");
                break;
            case this.systemConfig.msg_type.Cine:
                downloadUrl=imageObj.url.replace('thumbnail.jpg','DeviceVideo.');
                downloadUrl=downloadUrl+imageObj.img_encode_type||''
                // downloadUrl=imageObj.url.replace("thumbnail.jpg","DevicePoster.jpg");
                break;
            case this.systemConfig.msg_type.VIDEO_CLIP:
                downloadUrl=imageObj.mp4FileUrl
                break;
            case this.systemConfig.msg_type.RealTimeVideoReview:
                // downloadUrl=imageObj.mp4FileUrl||imageObj.ultrasound_url
                downloadUrl=imageObj.ultrasound_url
                break;
            case this.systemConfig.msg_type.File:
            case this.systemConfig.msg_type.IWORKS_PROTOCOL:
                downloadUrl = imageObj.url
                break;
            default:
                break;
            }
            return downloadUrl;
        },
        checkAudioDevice(fn){
            console.log("checkAudioDevice");
            var that = this;
            //枚举获取输入输出设备
            var media_devices = {audioinput: [], audiooutput: []};
            var default_input_device_name = "";
            var default_output_device_name = "";
            var AudioDeviceCheck = that.systemConfig.AudioDeviceCheck;

            navigator.mediaDevices.enumerateDevices()
                .then(function(devices) {
                    devices.forEach(function(device) {
                        if(("default" != device.deviceId) && ("communications" != device.deviceId) ){
                            if ('audiooutput' === device.kind) {
                                media_devices.audiooutput.push(device);
                            } else if ('audioinput' === device.kind) {
                                media_devices.audioinput.push(device);
                            }
                        }else if(("default" == device.deviceId)){
                            if ('audioinput' === device.kind) {
                                default_input_device_name = Tool.getNameForAudioDeviceLabel(device.label);
                            } else if ('audiooutput' === device.kind) {
                                default_output_device_name = Tool.getNameForAudioDeviceLabel(device.label);
                            }
                        }
                    });

                    var input_name = "";
                    var output_name = "";
                    if(0 == media_devices.audioinput.length || 0 == media_devices.audiooutput.length){ //无输入或输出设备，不处理
                        var error_info = AudioDeviceCheck.no_input_output_device ;
                        if(0 == media_devices.audioinput.length && 0 == media_devices.audiooutput.length){
                            error_info = AudioDeviceCheck.no_input_output_device;
                        }else if(0 == media_devices.audiooutput.length){
                            error_info = AudioDeviceCheck.no_output_device;
                        }else{
                            error_info = AudioDeviceCheck.no_input_device;
                        }

                        that.DEBUG_TO_SERVER("[RT-Voice-Client] Exception: uid(" + that.uid + ") " + getAudioDeviceExceptionInfo(error_info));
                        fn({error:1, error_info:error_info});
                        return;
                    } else if((1 == media_devices.audioinput.length) && (1 == media_devices.audiooutput.length)){//一个输入和一个输出设备，不处理，程序继续运行，
                        // do nothing
                        //麦克风 (2- Logitech Wireless Headset) (046d:0a29)
                        input_name = Tool.getNameForAudioDeviceLabel( media_devices.audioinput[0].label);
                        output_name = Tool.getNameForAudioDeviceLabel( media_devices.audiooutput[0].label);
                    }else if(1 < media_devices.audiooutput.length || 1 < media_devices.audioinput.length){ //有多个输入或输出设备，首先判断是否做过声音设备检测，没有检测过则弹出对话框进行检查
                        //首先判断是否做过声音设备检测，没有检测过则弹出对话框进行检查
                        var is_do_audio_detect = localStorage.getItem('detect_audio_device');
                        if(1 != is_do_audio_detect){
                            fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                            return;
                        }
                        //有声音设备检测记录
                        var device_info = localStorage.getItem('last_audio_device_info');
                        if(device_info && "" != device_info){
                            var json_device_info = JSON.parse(device_info);
                            var already_set_input_device_name = json_device_info.input;
                            var already_set_output_device_name = json_device_info.output;

                            if(already_set_input_device_name == "" || already_set_output_device_name == ""){
                                fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                                return;
                            }

                            var find_input_device = false;
                            var find_output_device = false;
                            for(var i=0; i < media_devices.audioinput.length; i++){
                                if(-1 != media_devices.audioinput[i].label.indexOf(already_set_input_device_name)){
                                    find_input_device = true;
                                    break;
                                }
                            }
                            for(var j=0; j < media_devices.audiooutput.length; j++){
                                if(-1 != media_devices.audiooutput[j].label.indexOf(already_set_output_device_name)){
                                    find_output_device = true;
                                    break;
                                }
                            }

                            if(!find_input_device || !find_output_device){ //记录中的设备不在线时，删除记录，执行没有记录的规则 //else 记录中的设备依然在线时，使用记录的麦克风
                                localStorage.setItem('last_audio_device_info', "");
                                fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                                return;
                            }

                            input_name = already_set_input_device_name;
                            output_name = already_set_output_device_name;
                        }else{//若未设置过音频设备
                            fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                            return;
                        }
                    }

                    fn({error:0, input_name:input_name, output_name:output_name});
                })
                .catch(function(err) {
                    console.log("无法枚举输入输出设备");
                    console.log(err.name + ": " + err.message);
                    that.DEBUG_TO_SERVER("[RT-Voice-Client] error: 无法枚举输入输出设备 err.name=" + err.name+ " err.message=" + err.message);
                    fn({error:1, error_info:AudioDeviceCheck.enum_exception}) ;
                });
        },
        DEBUG_TO_SERVER(msg, data){
            if (window.main_screen) {
                window.main_screen.gateway.emit("debug", msg, data);
            }
        },
        createConversationFail(){
            this.$message.error(this.$t('init_conversation_err'))
            if(this.currentOpenConversationInfo){
                this.$store.commit('conversationList/deleteConversationList',{cid:this.currentOpenConversationInfo.id})
            }
        },
        afterMainScreenLoad(callback){
            let interval = setInterval(()=>{
                if(window.main_screen){
                    clearInterval(interval)
                    interval = null
                    callback&&callback()
                }
            },50)
        },
        limitImageSize(url,size=70){
            if(url.includes('https')){
                url = Tool.addParamsToUrl(url,{'x-oss-process':`image/resize,w_${size}`})
            }
            return url
        },
        checkFileExpired(item){
            if(item.resource_expired_at){
                // 创建一个moment对象表示当前时间
                const currentTime = new Date().getTime();
                // 创建一个moment对象表示要比较的时间
                const otherTime = new Date(item.resource_expired_at).getTime()
                // 比较两个时间的大小
                if (currentTime>otherTime) {
                    return true
                }
                return false
            }
            return false
        },
        formatTime(time) {
            return moment(time).format("YYYY-MM-DD HH:mm z");
        },
        formatTimeOrEllipsisYear(time) {
            if (judgeIfCurrentYear(time)) {
                return moment(time).format("MM-DD HH:mm z");
            }
            return moment(time).format("YYYY-MM-DD HH:mm z");
        },
        showVideoErrorTips(){
            if(!this.isInternalNetworkEnv){
                this.$notify.error(this.$t('video_cannot_played'))
            }
        },
        getBindULinkerDeviceIdFromStorage() {
            let bindULinkerDeviceId = ''
            try {
                // 从 localStorage 获取绑定设备ID，如果不存在则返回空字符串
                bindULinkerDeviceId = localStorage.getItem('bindULinkerDeviceId');

                // 如果 localStorage 中没有该设备ID，返回空字符串
                if (!bindULinkerDeviceId) {
                    return '';
                }

                return bindULinkerDeviceId;
            } catch (error) {
                console.error('Error reading bindULinkerDeviceId from localStorage:', error);
                return '';
            }
        },
        async sendSyncAccountOrLiveToULinker(cid){
            const bindULinkerDeviceId = this.getBindULinkerDeviceIdFromStorage()
            if(bindULinkerDeviceId){
                window.main_screen.sendSyncAccountOrLiveToULinker({
                    group_id:cid||0,
                    device_id:bindULinkerDeviceId,
                    language:getLanguage()
                },(res)=>{
                    console.log('sendSyncAccountOrLiveToULinker',res)
                    if(res.error_code === 0){
                        if(cid){
                            this.$message.success(this.$t('notify_linker_start_live_tips'))
                        }else{
                            this.$message.success(this.$t('notify_linker_sync_account_tips'))
                        }

                    }else{
                        this.$message.error(this.$t('operate_err'))
                    }

                })
            }
            // else{
            //     this.$message.error('当前设备还未绑定灵柯，无法进行远程发起直播')
            // }

        }
    }
}
