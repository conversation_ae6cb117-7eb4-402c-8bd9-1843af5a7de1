<template>
<div>
    <CommonDialog
      class="search_component"
      :title="$t('search')"
      :show.sync="isShowSearchDialog"
      :close-on-click-modal="false"
      :append-to-body='true'
      width="40%"
      :modal="false"
      :footShow="false"
      >
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane v-loading="searching" :label="$t('search_friend_text')" name="1">
                <p v-if="friends.length==0" class="no_search_tip">{{$t('no_search_data')}}</p>
                <div class="result_item clearfix" v-for="(friend,index) of friends" @click="openChat(friend)" :key="index">
                    <div class="fl">
                        <mr-avatar :url="getLocalAvatar(friend)" :key="friend.avatar"></mr-avatar>
                    </div>
                    <p class="nickname">{{friend.alias||friend.nickname}}</p>
                </div>
            </el-tab-pane>
            <el-tab-pane v-loading="searching" :label="$t('search_group_text')" name="2">
                <p v-if="groups.length==0" class="no_search_tip">{{$t('no_search_data')}}</p>
                <div class="result_item clearfix" v-for="(group,index) of groups" @click="clickGroupHandler(group)" :key="index">
                    <div class="fl">
                        <mr-avatar :url="getLocalAvatar(group)" :key="group.avatar"></mr-avatar>
                    </div>
                    <p class="nickname">{{group.subject}}</p>
                </div>
            </el-tab-pane>
            <el-tab-pane v-loading="searching" :label="$t('search_file_text')" name="3">
                <p v-if="fileList.length==0" class="no_search_tip">{{$t('no_search_data')}}</p>
                <div class="result_item clearfix" v-for="(file,index) of fileList" @click="clickMessageHandler(file)" :key="index">
                    <div class="fl">
                        <mr-avatar :url="getLocalAvatar(file)" :key="file.avatar"></mr-avatar>
                    </div>
                    <div class="right">
                        <p class="subject">{{file.subject}}</p>
                        <p class="time">{{getDisplayTime(file.message)}}</p>
                        <p v-if="file.message.msg_type==systemConfig.msg_type.File" class="file_name longwrap">{{$t('msg_type_file')}} {{file.message.file_name}}</p>
                        <p v-else-if="file.message.msg_type==systemConfig.msg_type.Frame||file.message.msg_type==systemConfig.msg_type.OBAI" class="file_name longwrap">{{$t('msg_type_consultation_file')}} {{file.message.img_id}}</p>
                        <p v-else class="file_name longwrap">{{$t('unknow_text')}}</p>
                    </div>

                </div>
            </el-tab-pane>
            <el-tab-pane v-loading="searching" :label="$t('search_history_text')" name="4">
                <p v-if="historyList.length==0" class="no_search_tip">{{$t('no_search_data')}}</p>
                <div class="result_item clearfix" v-for="(chat,index) of historyList" @click="clickMessageHandler(chat)" :key="index">
                    <div class="fl">
                        <mr-avatar :url="getLocalAvatar(chat)" :key="chat.avatar"></mr-avatar>
                    </div>
                    <div class="right">
                        <p class="subject">{{chat.subject}}</p>
                        <p class="time">{{getDisplayTime(chat.message)}}</p>
                        <p class="file_name longwrap">{{chat.message.msg_body}}</p>
                    </div>

                </div>
            </el-tab-pane>
            <el-tab-pane v-loading="searching" :label="$t('search_recent_chat_text')" name="5">
                <p v-if="chats.length==0" class="no_search_tip">{{$t('no_search_data')}}</p>
                <div class="result_item clearfix" v-for="(chat,index) of chats" @click="openChat(chat, 3)" :key="index">
                    <div class="fl">
                        <mr-avatar :url="getLocalAvatar(chat)" :key="chat.avatar"></mr-avatar>
                    </div>
                    <p class="nickname">{{remarkMap[chat.fid]||chat.subject}}</p>
                </div>
            </el-tab-pane>
            <el-tab-pane v-loading="searching" :label="$t('search_groupsets_text')" name="6" v-if="hasRegionGroupsetPermission">
                <p v-if="groupsets.length==0" class="no_search_tip">{{$t('no_search_data')}}</p>
                <div class="result_item clearfix" v-for="(groupset,index) of groupsets" @click="openChat(groupset)" :key="index">
                    <div class="fl">
                        <mr-avatar :url="getLocalAvatar(groupset)" :key="groupset.avatar"></mr-avatar>
                    </div>
                    <p class="nickname">{{groupset.subject}}</p>
                </div>
            </el-tab-pane>
        </el-tabs>
    </CommonDialog>
</div>

</template>
<script>
import base from '../lib/base'
import appOperateTool from '../lib/appOperateTool'
import Tool from '@/common/tool.js'
import {parseImageListToLocal,getLocalAvatar} from '../lib/common_base'
import CommonDialog from "../MRComponents/commonDialog.vue";

export default {
    mixins: [base,appOperateTool],
    name: 'search_more',
    permission: true,
    components: {CommonDialog},
    props:{
        searchText:{
            type: String,
            default: ''
        },
    },
    data(){
        return {
            getLocalAvatar,
            activeName:'1',
            isShowSearchDialog:false,
            groups:[],
            fileList:[],
            historyList:[],
            searching:false,
            friends:[],
            chats:[],
            groupsets:[]
        }
    },
    computed:{
        chatList() {
            return this.$store.state.chatList
        },
        friendList(){
            return this.$store.state.friendList.list;
        },
        groupList(){
            return this.$store.state.groupList
        },
        groupsetList(){
            return this.$store.state.groupset.list
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap
        },
        hasRegionGroupsetPermission(){
            return this.$checkPermission({regionPermissionKey: 'groupset'})
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('closeSearchDialog').$on('closeSearchDialog',()=>{
                this.isShowSearchDialog=false;
            })
        })
    },
    methods:{
        getDisplayTime(msg) {
            return Tool.getShowTime(msg)
        },
        search(activeName){
            if (this.searchText=='') {
                this.$message.error(this.$t('search_empty_tip'));
                return ;
            }
            this.activeName=activeName;
            this.isShowSearchDialog=true;
            if (activeName==1) {
                this.searchFriendList(this.searchText,this.friendList);
                return
            } else if(activeName == 2) {
                this.searchGroupList(this.searchText, this.groupList);
                return ;
            } else if(activeName == 5) {
                this.searchChatList(this.searchText, this.chatList)
                return
            }else if(activeName == 6) {
                this.searchGroupsetList(this.searchText)
                return
            }
            this.searching=true;
            this.groups=[]
            this.fileList=[]
            this.historyList=[]
            let param={
                search_type:parseInt(activeName),
                search_key:[this.searchText],
                timestamp_searching:new Date().getTime()
            }
            this.$root.socket.emit("search", param,(is_succ,data)=>{
                this.searching=false
                if (is_succ) {
                    // if (param.search_type==1) {
                    //     parseImageListToLocal(data["1"],'avatar')
                    //     this.friendList=this.setDefaultImg(data["1"]);
                    // }
                    if(param.search_type==3){
                        parseImageListToLocal(data["3"],'avatar')
                        this.fileList=this.setDefaultImg(data["3"])
                    } else if(param.search_type==4){
                        parseImageListToLocal(data["4"],'avatar')
                        this.historyList=this.setDefaultImg(data["4"])
                    }
                }else{
                    this.$message.error(this.$t('search_err'))
                }
            })
        },
        handleClick(tab){
            this.search(tab.name);
        },
        clickGroupHandler(group){
            this.isShowSearchDialog=false;
            this.openConversation(group.id,2)
        },
        needGetHistory(msg){
            let list=this.conversationList[msg.group_id].chatMessageList
            let need=true;
            for(let chat of list){
                if (chat.gmsg_id==msg.gmsg_id) {
                    need=false;
                    break;
                }
            }
            return need;
        },
        clickMessageHandler(item){
            let cid=item.message.group_id;
            this.isShowSearchDialog=false;
            this.$root.endMessageIds[cid]=item.message.gmsg_id
            let currentCid = this.$route.params.cid
            if (this.conversationList[cid]) {
                //会话已开启
                this.openConversation(cid,12)
                this.$nextTick(()=>{
                    this.$root.eventBus.$emit('setPageType',{cid:cid,examPageType:false})
                    let list=this.conversationList[cid].chatMessageList
                    if(list.length){
                    }else{
                        //this.$root.eventBus.$emit('getMoreHistory',{sort:1})
                    }
                })

            }else{
                this.$root.isScrollingList[cid] = true
                this.openConversation(cid,12)
                this.$nextTick(()=>{
                    this.$root.eventBus.$emit('setPageType',{cid:cid,examPageType:false})
                })

            }

        },
        searchFriendList(keyword,friendList) {
            this.friends = []
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length
            for(const item of friendList) {
                // 深拷贝一份再操作，避免破坏源对象结构
                let itemCopy = {...item}
                let nickname = item.alias||itemCopy.nickname
                if(re.test(nickname)) { // 正则匹配
                    this.friends.push(itemCopy)
                }
            }
        },
        searchGroupList(keyword,groupList){
            this.groups = []
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length
            for(const item of groupList) {
                // 深拷贝一份再操作，避免破坏源对象结构
                let itemCopy = {...item}
                let subject = item.subject
                if(re.test(subject)) { // 正则匹配
                    this.groups.push(itemCopy)
                }
            }
        },
        searchChatList(keyword, chatList){
            const re = new RegExp(`${keyword}`, 'ig')
            this.chats = []
            let filterObj = {}
            const keywordLength = keyword.length
            for(const item of chatList.list) {

                // 如果是群落，深度遍历
                if(item.type === 3 && item.list.length > 0) {
                    for(const chat of item.list) {
                        if(chat.cid in filterObj){ // 去重
                            continue
                        }
                        filterObj[chat.cid] = ''
                        if(chat.type === 3 && item.list.length > 0) { // 如果还是群落，则继续深度遍历
                            this.searchChatList(keyword, chat.list)
                        }else{
                            let itemCopy = {...chat}
                            let subject = itemCopy.subject
                            if(re.test(subject)) {
                                re.lastIndex = 0
                                this.chats.push(itemCopy)
                            }
                        }

                    }
                }else{
                    // 是好友或群聊，深拷贝一份再操作，避免破坏源对象结构
                    let itemCopy = {...item}
                    let subject = this.remarkMap[itemCopy.fid]||itemCopy.subject
                    if(re.test(subject)) {
                        re.lastIndex = 0
                        this.chats.push(itemCopy)
                    }
                }
            }
        },
        searchGroupsetList(keyword) {
            this.groupsets = []
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length

            for(const item of this.groupsetList) {
                // 深拷贝一份再操作，避免破坏源对象结构
                let itemCopy = {...item}
                let subject = itemCopy.subject
                if(re.test(subject)) {
                    re.lastIndex = 0
                    // itemCopy.avatar = 'static/resource_pc/images/groupset.png'
                    this.groupsets.push(itemCopy)
                }
            }
        },
        openGroupset(groupset_id){
            this.$router.replace('/main/index/chat_window/0/groupset_wall/'+groupset_id)
        },
        openChat(item, extra_type) {
            this.chats = []
            this.groupsets = []
            this.isShowSearchDialog = false
            this.$emit('update:searchText', '')
            if(extra_type === 3){ // 特殊处理最近聊天
                if(item.type == 2) { // 群
                    item.id = item.cid
                }else{               // 好友
                    item.id = item.fid
                }

            }
            if(item.type==3){
                this.openGroupset(item.id)
            }else if(item.type==2) {
                this.openConversation(item.id, 2)
            }else {
                this.openConversation(item.id, 3)
            }
        }
    }
}
</script>
<style lang="scss">
.search_component{
    .el-tabs{
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__content{
            flex:1;
            overflow: auto;
            .el-tab-pane{
                min-height:50px;
            }
            .result_item{
                margin:10px 0;
                cursor:pointer;
                .avatar{
                    width:44px;
                    height:44px;
                    border-radius:50%;
                }
                .nickname{
                    font-size:16px;
                    line-height: 44px;
                    padding-left: 56px;
                }
                .right{
                    height: 44px;
                    padding-left: 56px;
                    position: relative;
                    .file_name{
                        width: 64%;
                        margin:4px 0;
                    }
                    .time{
                        position:absolute;
                        right:8px;
                    }
                }
            }
        }
    }
}
</style>
