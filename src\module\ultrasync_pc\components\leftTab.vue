<template>
	<div class="left_container" v-show="isShow">
        <div class="tabs">
            <div class="tab_item" @click="switchTab(1)" :class="{active:tabIndex==1}" :title="$t('recent_chat_text')">
                <i class="icon iconfont iconioschatbubble tab_icon"></i>
                <span class="red_point" v-show="tabIndex!=1&&total>0"></span>
            </div>
            <div class="tab_item" @click="switchTab(2)" :class="{active:tabIndex==2}" :title="$t('contacts')">
                <i class="icon iconfont iconuser tab_icon"></i>
            </div>
            <div v-if="hasRegionGroupsetPermission" class="tab_item" @click="switchTab(3)" :class="{active:tabIndex==3}" :title="$t('groupset_text')">
                <i class="icon iconfont icongroups tab_icon"></i>
            </div>

            <!-- 右侧「+」菜单 -->
            <div class="tab_plus_area">
                <div class="tab_divider"></div>
                <el-dropdown
                    trigger="click"
                    placement="right-start"
                    @command="handleSubmenuCommand"
                    class="tab_plus_dropdown"
                    popper-class="custom-dropdown-right"
                >
                    <div class="tab_plus_btn">
                        <i class="el-icon-plus"></i>
                    </div>
                    <el-dropdown-menu slot="dropdown" class="tab_plus_menu">
                        <el-dropdown-item
                            v-for="subItem in filteredSubmenuItems"
                            :key="subItem.name"
                            :command="subItem.action"
                        >
                            <i :class="subItem.icon" style="margin-right:8px"></i>
                            <span>{{$t(subItem.labelKey)}}</span>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <div class="list_wrap">
            <tab-chat  class="left_list" v-show="tabIndex==1" ref="tab_chat"></tab-chat>
            <tab-friend class="left_list" v-show="tabIndex==2" ref="tab_friend"></tab-friend>
            <tab-group class="left_list" v-show="tabIndex==3" ref="tab_groupset"></tab-group>
        </div>

        <!-- 添加好友弹窗 - 动态加载 -->
        <component
            :is="addFriendComponent"
            v-model="addFriendVisible"
            @input="handleAddFriendVisibleChange"
        />

        <!-- 创建群组弹窗 - 动态加载 -->
        <component
            :is="addGroupComponent"
            v-model="addGroupVisible"
            @input="handleAddGroupVisibleChange"
        />

        <!-- 搜索群组弹窗 - 动态加载 -->
        <component
            :is="searchGroupComponent"
            v-model="searchGroupVisible"
            @input="handleSearchGroupVisibleChange"
        />
    </div>
</template>
<script>
import base from '../lib/base'
import tabChat from './leftTab_chat'
import tabFriend from './leftTab_friend'
import tabGroup from './leftTab_group'
export default {
    mixins: [base],
    name: 'LeftTab',
    permission: true,
    components: { tabChat , tabFriend , tabGroup},
    data(){
        return {
            tabIndex:1,
            isShow:true,
            // 右侧 + 下拉菜单项（参考 headerBar1.vue）
            submenuItems: [
                { name: 'add_friend', labelKey: 'add_friend_text', icon: 'icon iconfont iconuser-round-add', action: 'openAddFriend' },
                { name: 'add_group', labelKey: 'create_group_title', icon: 'icon iconfont iconusers-medical', action: 'openAddGroup' },
                { name: 'search_group', labelKey: 'search_group_title', icon: 'icon iconfont iconjiarubanji', action: 'openSearchGroup' }
            ],
            // 弹窗组件状态管理
            addFriendVisible: false,
            addFriendComponent: null,
            addGroupVisible: false,
            addGroupComponent: null,
            searchGroupVisible: false,
            searchGroupComponent: null
        }
    },
    watch:{
        '$route.path':{
            handler(newVal){
                this.isShow = !newVal.includes('ai_chat')
            },
        }
    },
    computed:{
        chatList(){
            return this.$store.state.chatList.list
        },
        notifications(){
            return this.$store.state.notifications
        },
        total(){
            let sum=0;
            sum+=this.notifications.friendApply.length
            sum+=this.notifications.groupApply.length
            for(let chat of this.chatList){
                sum+=chat.unread
            }
            return sum
        },
        filteredSubmenuItems(){
            return this.submenuItems
        },
        hasRegionGroupsetPermission(){
            return this.$checkPermission({regionPermissionKey: 'groupset'})
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('changeTab').$on('changeTab',this.changeTab)
        })
    },
    methods:{
        changeTab(index){
            this.switchTab(index);
        },
        switchTab(index){
            this.tabIndex=index;
            if(index == 1) {
                this.$refs.tab_friend.clearAllData()
                this.$refs.tab_groupset.clearAllData()
            }else if(index == 2) {
                this.$refs.tab_chat.clearAllData()
                this.$refs.tab_groupset.clearAllData()
            }else{
                this.$refs.tab_friend.clearAllData()
                this.$refs.tab_chat.clearAllData()
            }
        },
        // 右侧 + 按钮：处理下拉菜单命令
        handleSubmenuCommand(command){
            switch(command){
            case 'openAddFriend':
                this.openAddFriend();
                break;
            case 'openAddGroup':
                this.openAddGroup();
                break;
            case 'openSearchGroup':
                this.openSearchGroup();
                break;
            default:
                console.log('未知 + 菜单命令:', command);
            }
        },
        async openAddFriend(){
            // 动态加载并打开添加好友弹窗
            await this.loadAddFriend();
            this.$nextTick(() => {
                this.addFriendVisible = true;
            });
        },
        async openAddGroup(){
            // 动态加载并打开创建群组弹窗
            await this.loadAddGroup();
            this.$nextTick(() => {
                this.addGroupVisible = true;
            });
        },
        async openSearchGroup(){
            // 动态加载并打开搜索群组弹窗
            await this.loadSearchGroup();
            this.$nextTick(() => {
                this.searchGroupVisible = true;
            });
        },

        // 动态加载AddFriend组件
        async loadAddFriend() {
            if (!this.addFriendComponent) {
                try {
                    const module = await import("../pages/addFriend.vue");
                    this.addFriendComponent = module.default;
                } catch (error) {
                    console.error('加载AddFriend组件失败:', error);
                    this.$message.error('加载添加好友失败，请重试');
                }
            }
        },

        // 处理添加好友弹窗显示状态变化
        handleAddFriendVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.addFriendComponent = null;
                }, 300);
            }
        },

        // 动态加载AddGroup组件
        async loadAddGroup() {
            if (!this.addGroupComponent) {
                try {
                    const module = await import("../pages/addGroup.vue");
                    this.addGroupComponent = module.default;
                } catch (error) {
                    console.error('加载AddGroup组件失败:', error);
                    this.$message.error('加载创建群组失败，请重试');
                }
            }
        },

        // 处理创建群组弹窗显示状态变化
        handleAddGroupVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.addGroupComponent = null;
                }, 300);
            }
        },

        // 动态加载SearchGroup组件
        async loadSearchGroup() {
            if (!this.searchGroupComponent) {
                try {
                    const module = await import("../pages/searchGroup.vue");
                    this.searchGroupComponent = module.default;
                } catch (error) {
                    console.error('加载SearchGroup组件失败:', error);
                    this.$message.error('加载搜索群组失败，请重试');
                }
            }
        },

        // 处理搜索群组弹窗显示状态变化
        handleSearchGroupVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.searchGroupComponent = null;
                }, 300);
            }
        }
    }
}
</script>
<style lang="scss">
.left_container{
    width: 300px;
    display: flex;
    flex-direction: column;
    background-color: #E7EFF2;
    border-right: 1px solid #A9BFBE;
    font-size:16px;
    flex-shrink: 0;
    .tabs{
        height: 55px;
        border-bottom: 1px solid #C9D8D7; // 更柔和的分割线
        background-color: #E7EFF2; // 与整体背景一致
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 8px;
        .tab_item{
            flex:1;
            cursor: pointer;
            text-align:center;
            height:100%;
            position:relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 4px;
            padding: 0 12px;
            border-radius: 10px 10px 0 0;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;

            .tab_icon{
                font-size: 28px; // 进一步增大图标尺寸
                color: #5E7776; // 默认图标颜色更贴合整体色调
                transition: color 0.3s ease;
            }

            &:hover{
                background: #EEF6F6;
                .tab_icon{ color: #2F6E6C; }
            }

            &.active{
                background: #EAF5F5;
                border-bottom-color: #2AA6A4; // 统一为青绿色
                .tab_icon{
                    color: #2AA6A4;
                }
            }

            .red_point{
                position:absolute;
                width:8px;
                height:8px;
                border-radius:4px;
                background:#f00;
                top: 10px;
                right: 30px;
            }
        }

        // 右侧 + 区域
        .tab_plus_area{
            display: flex;
            align-items: center;
            flex-shrink: 0;
            margin-left: 8px;
            .tab_divider{
                width: 1px;
                height: 24px;
                background: #C9D8D7; // 与底部分割线一致
                margin: 0 10px 0 6px;
            }
            .tab_plus_dropdown{
                .tab_plus_btn{
                    width: 32px;
                    height: 32px;
                    border-radius: 8px; // 改为圆角矩形，与tab风格一致
                    border: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #5E7776;
                    background: #F8FCFC;
                    transition: background .2s ease, color .2s ease;
                    cursor: pointer;
                    &:hover{
                        background: #EAF5F5;
                        color: #2AA6A4;
                    }
                    &:active{
                        background: #DFF1F0;
                    }
                    i{
                        font-size: 18px;
                        font-weight: bold;
                    }
                }
            }
        }
    }
    .list_wrap{
//         padding: 5px 0;
        margin-bottom: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 10px;
        .left_list{
            position: relative;
            flex:1;
            display: flex;
            flex-direction: column;
            min-height: 0px;
            h5{
                position: relative;
                padding-left: 32px;
                height: 26px;
                line-height: 26px;
                cursor: pointer;
                margin: 2px;
                .iconsanjiaoxing{
                    font-size: 12px;
                    position: absolute;
                    left: 14px;
                    &.close{
                        transform: rotate(270deg);
                    }
                }
            }
            .chat_list{
                flex:3;
                overflow:auto;
                .chat_item{
                    height: 68px;
                    position: relative;
                    display:flex;
                    align-items:center;
                    padding-left:20px;
                    cursor: pointer;
                    border-bottom: 1px solid #dbdbdb;
                    &:hover{
                        background-color: #BFD1D1;
                    }
                    &.active{
                        border-right: none;
                        background-color: #A1B7B6;
                        .chat_right{
                            a{
                                color:#fff !important;
                                text-decoration: none !important;
                            }
                            .send_ts,.message,.attendee_name{
                                color:#fff !important;
                            }
                        }
                    }
                    .avatar{
                        width: 46px;
                        height: 46px;
                        border-radius: 50%;
                        object-fit: contain;
                    }
                    .chat_right{
                        width:0;
                        padding: 0 8px 0 18px;;
					    height: 46px;
					    margin: 10px 0;
                        flex:1;
					    .up{
                            display:flex;
					    	.subject{
					    		flex:1;
					    		height:22px;
                                position:relative;
                                word-break: keep-all;
					    		span{ display: inline-block; }
					    		.subject_tip{
							    	position: absolute;
								    width: 100%;
								    height: 22px;
								    left:0px;
								    top: 0px;
							    }
					    	}
					    	.send_ts{
					    		font-size:12px;
					    		line-height:22px;
					    		margin-left:6px;
                                white-space: nowrap;
					    	}
                            .wrap_subject{
                                word-break: break-all;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                height: 44px;
                            }
					    }
						.down{
							display:flex;
							height:24px;
                            a{
                                color:#555;
                                text-decoration: none !important;
                            }
							.message{
                                flex:1;
								font-size: 14px;
							    color: #555;
							    line-height: 30px;
							    white-space: nowrap;
							    text-overflow: ellipsis;
							    overflow: hidden;
                                &>img{
                                    margin:0 2px;
                                    vertical-align: text-bottom;
                                }
							}
                            .attendee_name{
                                font-size: 14px;
                                color: #555;
                                line-height: 30px;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }
                            .icon_bar{
                                flex-shrink: 0;
                                i{
                                    font-weight: bold;
                                    color: #2d993b;
                                    font-size: 22px;
                                    margin: 0 2px;
                                }
                                .icontransfer{
                                    color: #333;
                                    font-size: 20px;
                                }
                            }
						}
                        .chat_right_title{
                            width: 100%;
                            height: 100%;
                            display: flex;
                            align-items: center;
                        }
                    }
                    .unread{
                        position: absolute;
                        left: 60px;
                        bottom: 8px;
                        background: #f00;
                        color: #fff;
                        width: 18px;
                        height: 18px;
                        line-height: 16px;
                        font-size: 12px;
                        text-align: center;
                        border-radius: 50%;
                    }
                }
                & .chat_item:last-child{
                    border:none;
                }
            }
        }
    }
}

/* 右侧弹出过渡（使用与 leftSideBar 一致的类名 custom-dropdown-right） */
.el-dropdown-menu.el-popper.custom-dropdown-right{
    &.el-zoom-in-top-enter-active{ animation: slideInFromRight .18s ease-out !important; transform-origin: left center !important; }
    &.el-zoom-in-top-leave-active{ animation: slideOutToRight .18s ease-in !important; transform-origin: left center !important; }
}

@keyframes slideInFromRight{
    0%{ opacity: 0; transform: translateX(8px) scale(.98); }
    100%{ opacity: 1; transform: translateX(0) scale(1); }
}
@keyframes slideOutToRight{
    0%{ opacity: 1; transform: translateX(0) scale(1); }
    100%{ opacity: 0; transform: translateX(8px) scale(.98); }
}
</style>
